# PillarScan - La voix citoyenne qui transforme la France 🇫🇷

<div align="center">
  <img src="./apps/frontend/public/pillarscan-logo.svg" alt="PillarScan Logo" width="200"/>
  
  **Transforming citizen frustrations into collective action**
  
  [![Next.js](https://img.shields.io/badge/Next.js-15-black?logo=next.js)](https://nextjs.org/)
  [![Strapi](https://img.shields.io/badge/Strapi-5.15-2F2E8B?logo=strapi)](https://strapi.io/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?logo=typescript)](https://www.typescriptlang.org/)
  [![Turborepo](https://img.shields.io/badge/Turborepo-latest-EF4444?logo=turborepo)](https://turbo.build/)
  [![pnpm](https://img.shields.io/badge/pnpm-10.8-F69220?logo=pnpm)](https://pnpm.io/)
  
  [Documentation](./PILLARSCAN-COMPLETE-PLAN.md) | [Specifications](./.specs) | [Demo](#) | [Contributing](#contributing)
</div>

## 🎯 Overview

PillarScan is a revolutionary citizen engagement platform that transforms everyday frustrations into measurable actions. Built for France, designed for the world.

### The Problem

- 72% of French citizens believe democracy doesn't work well
- 80% have given up reporting problems through official channels
- Current systems turn complaints into noise, not solutions

### Our Solution

Transform the broken cycle:

```
❌ Old: Complaint → Ignored → Frustration → Distrust
✅ New: Expression → AI Analysis → Right Actor → Action → Impact
```

## 🚀 Quick Start

### Prerequisites

- Node.js >= 20
- pnpm >= 10.8.0
- PostgreSQL >= 14 (for production)
- Redis (optional, for caching)

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/pillarscan.git
cd pillarscan

# Install dependencies
pnpm install

# Run development servers
pnpm dev
```

This will start:

- Frontend: http://localhost:3000
- Backend: http://localhost:1337
- Backend Admin: http://localhost:1337/admin

## 🏗️ Architecture

This is a **Turborepo** monorepo containing:

```
pillarscan/
├── apps/
│   ├── frontend/          # Next.js 15 app with Turbopack
│   └── backend/           # Strapi v5.15 CMS
├── packages/              # Shared packages (coming soon)
├── turbo.json            # Turborepo configuration
└── pnpm-workspace.yaml   # pnpm workspace config
```

### Tech Stack

#### Frontend (`apps/frontend`)

- **Next.js 15** with App Router and Turbopack
- **TypeScript** for type safety
- **Tailwind CSS v4** for styling
- **React 19** for UI components
- Coming soon: Shadcn/ui, TanStack Query, Zustand

#### Backend (`apps/backend`)

- **Strapi v5.15** headless CMS
- **SQLite** (dev) / **PostgreSQL** (prod)
- **TypeScript** support
- Coming soon: Custom plugins for AI, workflows, analytics

## 📚 Development

### Available Commands

```bash
# Development
pnpm dev          # Start all apps in development mode
pnpm dev:frontend # Start only frontend
pnpm dev:backend  # Start only backend

# Building
pnpm build        # Build all apps for production
pnpm start        # Start all apps in production mode

# Code Quality
pnpm lint         # Lint all apps
pnpm format       # Format code with Prettier
pnpm typecheck    # Run TypeScript checks

# Strapi specific (run from apps/backend)
pnpm strapi console    # Strapi console
pnpm strapi generate   # Generate Strapi resources
```

### Project Structure

#### Frontend Structure

```
apps/frontend/
├── src/
│   ├── app/              # Next.js app router pages
│   │   ├── (auth)/       # Auth layout group
│   │   ├── (citizen)/    # Citizen portal
│   │   ├── (admin)/      # Admin dashboards
│   │   └── api/          # API routes
│   ├── components/       # React components
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utilities and configs
│   └── types/           # TypeScript types
├── public/              # Static assets
└── package.json
```

#### Backend Structure

```
apps/backend/
├── src/
│   ├── api/             # API endpoints
│   │   ├── expression/  # Citizen expressions
│   │   ├── pillar/      # 12 pillars system
│   │   └── action/      # Action tracking
│   ├── plugins/         # Custom Strapi plugins
│   └── extensions/      # Core extensions
├── config/              # Strapi configuration
├── database/            # Database migrations
└── package.json
```

## 🌟 Features

### For Citizens

- 📝 **Express Concerns**: Simple form to report problems or share ideas
- 📍 **Geolocate Issues**: Automatic location detection
- 📊 **Track Impact**: See real changes from your expressions
- 🔔 **Real-time Updates**: Get notified when action is taken

### For Validators

- 🤖 **AI Assistance**: Smart classification suggestions
- ⚡ **Efficient Workflow**: Batch operations and keyboard shortcuts
- 📈 **Quality Metrics**: Track validation accuracy
- 🔍 **Advanced Filters**: Find expressions quickly

### For Officials

- 🗺️ **Territory Dashboard**: Real-time heatmaps of citizen concerns
- 📊 **Trend Analysis**: Identify emerging issues early
- 🎯 **Action Management**: Assign and track resolutions
- 📑 **Report Generation**: Create official reports with one click

### The 12 Pillars System

Citizens can categorize their expressions into 12 key areas of French society:

- 🏥 **Santé** (Health)
- 🎓 **Éducation** (Education)
- 🚇 **Transport** (Transportation)
- 🏘️ **Logement** (Housing)
- 💼 **Emploi** (Employment)
- 🛡️ **Sécurité** (Security)
- 🌳 **Environnement** (Environment)
- ⚖️ **Justice** (Justice)
- 💰 **Pouvoir d'achat** (Purchasing Power)
- 🤝 **Vie sociale** (Social Life)
- 🏛️ **Démocratie** (Democracy)
- 🎭 **Culture** (Culture)

## 🔧 Configuration

### Environment Variables

Create `.env.local` files in each app:

#### Frontend (`apps/frontend/.env.local`)

```env
NEXT_PUBLIC_API_URL=http://localhost:1337
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_token
```

#### Backend (`apps/backend/.env`)

```env
HOST=0.0.0.0
PORT=1337
APP_KEYS=your_app_keys
API_TOKEN_SALT=your_api_token_salt
ADMIN_JWT_SECRET=your_admin_jwt_secret
JWT_SECRET=your_jwt_secret
DATABASE_URL=postgresql://user:password@localhost:5432/pillarscan
```

## 🚀 Deployment

### Frontend Deployment (Vercel)

```bash
cd apps/frontend
vercel --prod
```

### Backend Deployment (Docker)

```bash
cd apps/backend
docker build -t pillarscan-backend .
docker run -p 1337:1337 pillarscan-backend
```

See [PILLARSCAN-COMPLETE-PLAN.md](./PILLARSCAN-COMPLETE-PLAN.md) for detailed deployment instructions.

## 🧪 Testing

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run e2e tests
pnpm test:e2e
```

## 📊 Performance

Target metrics:

- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3s
- **API Response Time**: < 100ms (p95)
- **Lighthouse Score**: > 95

## 🔐 Security

- **GDPR Compliant**: Full compliance with European data protection
- **Data Encryption**: At rest and in transit
- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Protection against abuse
- **Security Headers**: CSP, HSTS, X-Frame-Options

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style

- Follow the existing code style
- Use TypeScript strict mode
- Write tests for new features
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- Built with ❤️ for the citizens of France
- Inspired by the French tradition of civic engagement
- Powered by amazing open-source technologies

## 📞 Support

- **Documentation**: [Full documentation](./PILLARSCAN-COMPLETE-PLAN.md)
- **Issues**: [GitHub Issues](https://github.com/yourusername/pillarscan/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/pillarscan/discussions)
- **Email**: <EMAIL>

---

<div align="center">
  
**Transform frustration into action. Give power back to the people.**

Made with 🇫🇷 for France, designed for the world.

</div>
