# Spécification du Workflow de Modération - PillarScan Expressions

## 1. Vue d'ensemble du système de publication

### 1.1 Principe fondamental

Le système fonctionne comme une **maison d'édition moderne** où :

- Chaque utilisateur est un **auteur** qui rédige ses expressions
- Chaque expression peut rester en **brouillon privé** ou être soumise à **publication**
- Chaque auteur a un **éditeur/validateur attitré** dans son périmètre géographique ou thématique
- La publication nécessite une **validation** selon des règles prédéfinies

### 1.2 Analogie avec le monde réel

```
┌─────────────────────────────────────────────────────────┐
│                    MONDE RÉEL                           │
├─────────────────────────────────────────────────────────┤
│  Auteur écrit     →  Éditeur révise  →  Publication    │
│  dans son         →  et valide       →  pour le public │
│  bureau privé     →                   →                 │
└─────────────────────────────────────────────────────────┘
                           ↓
┌─────────────────────────────────────────────────────────┐
│                    PILLARSCAN                           │
├─────────────────────────────────────────────────────────┤
│  Utilisateur      →  Validateur      →  Expression     │
│  crée expression  →  de périmètre    →  publique       │
│  (brouillon)      →  examine         →  visible        │
└─────────────────────────────────────────────────────────┘
```

## 2. Les états d'une expression

### 2.1 Diagramme des états

```
                    ┌──────────────┐
                    │   CRÉATION   │
                    └──────┬───────┘
                           │
                    ┌──────▼───────┐
                    │  BROUILLON   │◄─────────┐
                    │   (privé)    │          │
                    └──────┬───────┘          │
                           │                  │
                   [Soumettre à validation]   │
                           │                  │
                    ┌──────▼───────┐          │
                    │  EN ATTENTE  │          │
                    │ DE VALIDATION│          │
                    └──────┬───────┘          │
                           │                  │
                    ┌──────┴──────┐           │
                    │             │           │
              [Approuvé]     [Rejeté]        │
                    │             │           │
                    ▼             └───────────┘
            ┌──────────────┐
            │   PUBLIÉ     │
            │  (visible)   │
            └──────────────┘
```

### 2.2 Description des états

| État           | Description                      | Visibilité              | Actions possibles                |
| -------------- | -------------------------------- | ----------------------- | -------------------------------- |
| **Brouillon**  | Expression en cours de rédaction | Auteur uniquement       | Modifier, Supprimer, Soumettre   |
| **En attente** | Soumise pour validation          | Auteur + Validateur     | Annuler soumission, Voir statut  |
| **Rejeté**     | Non approuvée par le validateur  | Auteur uniquement       | Modifier, Re-soumettre, Archiver |
| **Publié**     | Validée et visible publiquement  | Tous (selon paramètres) | Signaler, Partager               |

## 3. Les acteurs du système

### 3.1 Hiérarchie des rôles

```
┌─────────────────────────────────────────┐
│         SUPER ADMINISTRATEUR            │
│  • Configure les périmètres             │
│  • Assigne les validateurs              │
└────────────────┬────────────────────────┘
                 │
        ┌────────┴────────┐
        │                 │
┌───────▼──────┐  ┌───────▼──────┐
│  VALIDATEUR  │  │  VALIDATEUR  │
│  Région A    │  │  Thème Santé │
└───────┬──────┘  └───────┬──────┘
        │                 │
   ┌────┴───┬────────┬───┴────┐
   │        │        │        │
┌──▼──┐ ┌──▼──┐ ┌──▼──┐ ┌──▼──┐
│USER │ │USER │ │USER │ │USER │
│ 01  │ │ 02  │ │ 03  │ │ 04  │
└─────┘ └─────┘ └─────┘ └─────┘
```

### 3.2 Responsabilités par rôle

#### Utilisateur (Auteur)

- Crée des expressions en brouillon
- Peut modifier ses brouillons sans limite
- Décide quand soumettre à validation
- Reçoit les feedbacks du validateur
- Peut republier après corrections

#### Validateur

- Reçoit les notifications de nouvelles soumissions
- Examine le contenu selon une grille de critères
- Peut approuver ou rejeter avec commentaires
- Voit les statistiques de son périmètre
- Ne peut PAS modifier le contenu

#### Super Administrateur

- Définit les périmètres (géographiques, thématiques)
- Assigne les validateurs aux utilisateurs
- Configure les règles de validation
- Supervise les métriques globales

## 4. Les périmètres de validation

### 4.1 Types de périmètres

```
┌─────────────────────────────────────────────┐
│           PÉRIMÈTRES GÉOGRAPHIQUES          │
├─────────────────────────────────────────────┤
│  • National (tout le pays)                  │
│  • Régional (ex: Région Centre)             │
│  • Départemental (ex: Loiret)               │
│  • Communal (ex: Orléans)                   │
│  • Quartier (ex: La Source)                 │
└─────────────────────────────────────────────┘

┌─────────────────────────────────────────────┐
│           PÉRIMÈTRES THÉMATIQUES            │
├─────────────────────────────────────────────┤
│  • Par pilier (ex: Santé, Éducation)        │
│  • Par type d'entité (ex: Hôpitaux)         │
│  • Par urgence (expressions critiques)      │
│  • Par langue (pour pays multilingues)      │
└─────────────────────────────────────────────┘
```

### 4.2 Affectation automatique

Lors de la création d'un compte utilisateur :

1. **Géolocalisation** → Détermine le périmètre géographique
2. **Préférences** → Détermine les thématiques d'intérêt
3. **Algorithme** → Assigne le validateur le plus pertinent
4. **Notification** → Informe l'utilisateur et le validateur

## 5. Le processus de validation

### 5.1 Flux de validation standard

```
Utilisateur                  Système                    Validateur
    │                           │                           │
    ├─[Crée expression]────────►│                           │
    │                           ├─[Sauvegarde brouillon]   │
    │                           │                           │
    ├─[Soumet validation]──────►│                           │
    │                           ├─[Change état]            │
    │                           ├─[Notifie validateur]─────►│
    │                           │                           │
    │                           │◄──────[Examine contenu]───┤
    │                           │                           │
    │                           │◄──────[Décision]──────────┤
    │                           │                           │
    │◄──[Notification résultat]─┤                           │
    │                           │                           │
```

### 5.2 Critères de validation

Le validateur examine selon une **grille objective** :

| Critère        | Description                      | Action si non conforme       |
| -------------- | -------------------------------- | ---------------------------- |
| **Légalité**   | Pas de contenu illégal           | Rejet automatique            |
| **Respect**    | Pas d'insultes ou diffamation    | Demande de reformulation     |
| **Pertinence** | Lié aux piliers SMATFLOW         | Suggestion de classification |
| **Clarté**     | Message compréhensible           | Demande de précisions        |
| **Preuves**    | Si accusation, éléments factuels | Demande de documentation     |

### 5.3 Interface du validateur

```
┌─────────────────────────────────────────────────┐
│          TABLEAU DE BORD VALIDATEUR             │
├─────────────────────────────────────────────────┤
│                                                 │
│  🔴 En attente : 12                            │
│  🟡 En cours d'examen : 3                      │
│  🟢 Validées aujourd'hui : 28                  │
│                                                 │
│  ┌─────────────────────────────────────┐       │
│  │ Expression #2847                     │       │
│  │ Auteur: Marie D. (Orléans)          │       │
│  │ Pilier: Santé                       │       │
│  │ "L'hôpital X manque de personnel..." │       │
│  │                                      │       │
│  │ [✓ APPROUVER] [✗ REJETER] [💬 COMMENTER] │  │
│  └─────────────────────────────────────┘       │
└─────────────────────────────────────────────────┘
```

## 6. Règles et garde-fous

### 6.1 Délais de traitement

- **SLA (Service Level Agreement)** : 72h maximum pour une décision
- **Escalade automatique** : Si pas de réponse, remonte au validateur supérieur
- **Publication automatique** : Après 7 jours sans réponse (paramétrable)

### 6.2 Mécanismes anti-abus

```
┌─────────────────────────────────────────────┐
│           PROTECTION UTILISATEURS           │
├─────────────────────────────────────────────┤
│ • Maximum 3 rejets → Changement validateur  │
│ • Historique des décisions consultable      │
│ • Possibilité d'appel à un super-validateur │
│ • Statistiques de rejet par validateur      │
└─────────────────────────────────────────────┘

┌─────────────────────────────────────────────┐
│           PROTECTION VALIDATEURS            │
├─────────────────────────────────────────────┤
│ • Limite du nombre d'expressions/jour       │
│ • Rotation automatique des validateurs      │
│ • Formation et guides de validation         │
│ • Système de notation des validateurs       │
└─────────────────────────────────────────────┘
```

## 7. Notifications et communications

### 7.1 Points de notification

| Événement             | Destinataire       | Canal        | Message type                                 |
| --------------------- | ------------------ | ------------ | -------------------------------------------- |
| Nouvelle soumission   | Validateur         | Email + App  | "Nouvelle expression à valider"              |
| Décision prise        | Auteur             | Email + App  | "Votre expression a été [approuvée/rejetée]" |
| Délai dépassé         | Validateur + Admin | Email urgent | "Action requise sous 24h"                    |
| Changement validateur | Auteur             | Email        | "Nouveau validateur assigné"                 |

### 7.2 Tableau de bord temps réel

```
┌─────────────────────────────────────────────┐
│         MÉTRIQUES EN TEMPS RÉEL             │
├─────────────────────────────────────────────┤
│                                             │
│  Taux d'approbation : ████████░░ 82%       │
│  Temps moyen validation : 18h               │
│  Validateurs actifs : 47/52                 │
│  Expressions en attente : 234               │
│                                             │
│  Par région :                               │
│  • Nord : ████████░░ 15 en attente         │
│  • Sud  : ███░░░░░░░ 8 en attente          │
│  • Est  : ████████░░ 12 en attente         │
│                                             │
└─────────────────────────────────────────────┘
```

## 8. Cas d'usage particuliers

### 8.1 Expression urgente/sensible

Si l'expression concerne :

- Une urgence humanitaire
- Un danger imminent
- Une violation grave des droits

→ **Circuit court** : Validation prioritaire en 2h

### 8.2 Collecte déléguée

Quand un utilisateur assermenté collecte pour un tiers :

- Le validateur voit les DEUX identités
- Validation renforcée si première collecte
- Traçabilité complète de la chaîne

### 8.3 Multi-validation

Pour certains sujets sensibles :

- Validation par 2 validateurs indépendants
- Les deux doivent approuver
- En cas de désaccord → Super-validateur

## 9. Évolution et apprentissage

### 9.1 Machine Learning progressif

```
┌─────────────────────────────────────────────┐
│          APPRENTISSAGE DU SYSTÈME           │
├─────────────────────────────────────────────┤
│                                             │
│  Expressions     Décisions      Patterns    │
│  validées   →    analysées  →   détectés    │
│                                             │
│  Résultat : Pré-validation automatique      │
│  • Détection spam : 95% précision           │
│  • Classification piliers : 88% précision   │
│  • Suggestion corrections : Active          │
│                                             │
└─────────────────────────────────────────────┘
```

### 9.2 Amélioration continue

- Feedback des utilisateurs sur les décisions
- Ajustement des critères selon les retours
- Formation continue des validateurs
- Optimisation des périmètres

## 10. Vision à long terme

Le système évoluera vers une **auto-régulation communautaire** où :

1. Les meilleurs contributeurs deviennent validateurs
2. La communauté vote sur les critères
3. L'IA pré-valide 80% des cas simples
4. Les validateurs se concentrent sur les cas complexes

Ce workflow de modération garantit la **qualité** des expressions tout en respectant la **liberté d'expression** et en construisant une **intelligence collective** fiable.
