# Système de Rôles et Permissions - PillarScan

## 1. Introduction au système de permissions

### 1.1 Concept fondamental

Le système de permissions fonctionne comme un **immeuble sécurisé** :

- Cha<PERSON> **rôle** est comme un badge d'accès
- Cha<PERSON> **permission** est comme une clé pour une porte spécifique
- Les **utilisateurs** sont les personnes qui portent ces badges
- Les **ressources** sont les pièces à protéger

### 1.2 Matrice de sécurité globale

```
┌─────────────────────────────────────────────────────────┐
│                   PYRAMIDE DES ACCÈS                    │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    🔺 Super Admin                       │
│                   /             \                       │
│                  /   Tout voir   \                      │
│                 /   Tout faire    \                     │
│                /___________________\                    │
│               🔺 Admin Régional     🔺                  │
│              /         |            \                   │
│             /    Gérer sa région     \                  │
│            /_________________________\                  │
│           🔺 Validateur  🔺 Modérateur 🔺               │
│          /               |              \               │
│         /     Valider les expressions    \              │
│        /_________________________________\              │
│       🔺 Contributeur    🔺    Observateur 🔺           │
│      /                                      \           │
│     /        Utilisateurs de base            \          │
│    /__________________________________________\         │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 2. Les rôles détaillés

### 2.1 Tableau des rôles principaux

| Rôle                     | Icône | Description               | Nombre estimé | Nomination    |
| ------------------------ | ----- | ------------------------- | ------------- | ------------- |
| **Super Admin**          | 👑    | Contrôle total du système | 1-3           | Fondateurs    |
| **Admin National**       | 🏛️    | Gestion niveau pays       | 5-10          | Direction     |
| **Admin Régional**       | 🏢    | Gestion niveau région     | 50-100        | Coordinateurs |
| **Validateur Senior**    | ⭐    | Validation + formation    | 100-200       | Mérite        |
| **Validateur**           | ✅    | Validation expressions    | 500-1000      | Sélection     |
| **Contributeur Vérifié** | 🔷    | Contribution certifiée    | 10K+          | Vérification  |
| **Contributeur**         | 📝    | Utilisateur standard      | Illimité      | Inscription   |
| **Observateur**          | 👁️    | Lecture seule             | Illimité      | Public        |

### 2.2 Rôles spécialisés

```
┌─────────────────────────────────────────────────────────┐
│                   RÔLES SPÉCIALISÉS                     │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  📊 Analyste          │  🔍 Peut exporter les données  │
│  🚨 Urgentiste        │  📍 Traite les cas urgents    │
│  🌐 Traducteur        │  🗣️ Valide les traductions    │
│  📱 Collecteur Mobile │  🚶 Collecte sur le terrain    │
│  🎓 Formateur         │  📚 Forme les validateurs      │
│  🛡️ Auditeur         │  🔎 Vérifie les processus      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 3. Les permissions détaillées

### 3.1 Structure des permissions

Chaque permission suit le format : **RESSOURCE.ACTION**

```
expression.create    = Créer une expression
expression.read      = Lire une expression
expression.update    = Modifier une expression
expression.delete    = Supprimer une expression
expression.validate  = Valider une expression
expression.publish   = Publier une expression
```

### 3.2 Tableau complet des permissions

#### 3.2.1 Permissions sur les Expressions

| Permission                 | Description                   | Rôles autorisés                 |
| -------------------------- | ----------------------------- | ------------------------------- |
| **expression.create**      | Créer ses propres expressions | Tous sauf Observateur           |
| **expression.read.own**    | Voir ses propres expressions  | Tous sauf Observateur           |
| **expression.read.all**    | Voir toutes les expressions   | Validateur et +                 |
| **expression.read.public** | Voir expressions publiées     | Tous                            |
| **expression.update.own**  | Modifier ses brouillons       | Contributeur et +               |
| **expression.update.any**  | Modifier toute expression     | Super Admin seulement           |
| **expression.delete.own**  | Supprimer ses brouillons      | Contributeur et +               |
| **expression.delete.any**  | Supprimer toute expression    | Admin et +                      |
| **expression.submit**      | Soumettre à validation        | Contributeur et +               |
| **expression.validate**    | Approuver/Rejeter             | Validateur et +                 |
| **expression.publish**     | Rendre public                 | Validateur et +                 |
| **expression.archive**     | Archiver                      | Contributeur (own), Admin (any) |

#### 3.2.2 Permissions sur les Entités

| Permission        | Description          | Rôles autorisés           |
| ----------------- | -------------------- | ------------------------- |
| **entity.create** | Créer une entité     | Contributeur Vérifié et + |
| **entity.read**   | Voir les entités     | Tous                      |
| **entity.update** | Modifier une entité  | Validateur et +           |
| **entity.delete** | Supprimer une entité | Admin et +                |
| **entity.verify** | Vérifier officielle  | Admin et +                |
| **entity.merge**  | Fusionner doublons   | Admin et +                |

#### 3.2.3 Permissions sur les Médias

| Permission           | Description           | Rôles autorisés   |
| -------------------- | --------------------- | ----------------- |
| **media.upload**     | Uploader des fichiers | Contributeur et + |
| **media.read.own**   | Voir ses médias       | Contributeur et + |
| **media.read.all**   | Voir tous les médias  | Validateur et +   |
| **media.delete.own** | Supprimer ses médias  | Contributeur et + |
| **media.delete.any** | Supprimer tout média  | Admin et +        |
| **media.moderate**   | Signaler inapproprié  | Validateur et +   |

#### 3.2.4 Permissions de Modération

| Permission                | Description          | Rôles autorisés        |
| ------------------------- | -------------------- | ---------------------- |
| **moderation.view.queue** | Voir file d'attente  | Validateur et +        |
| **moderation.assign**     | Assigner validateurs | Admin Régional et +    |
| **moderation.override**   | Outrepasser décision | Admin et +             |
| **moderation.bulk**       | Actions en masse     | Validateur Senior et + |
| **moderation.urgent**     | Traiter les urgences | Urgentiste et +        |

#### 3.2.5 Permissions d'Administration

| Permission                | Description           | Rôles autorisés     |
| ------------------------- | --------------------- | ------------------- |
| **user.create**           | Créer utilisateurs    | Admin et +          |
| **user.read**             | Voir profils          | Validateur et +     |
| **user.update**           | Modifier utilisateurs | Admin et +          |
| **user.delete**           | Supprimer comptes     | Super Admin         |
| **user.assign.role**      | Attribuer rôles       | Admin et +          |
| **user.assign.validator** | Assigner validateurs  | Admin Régional et + |

#### 3.2.6 Permissions d'Analyse

| Permission                  | Description      | Rôles autorisés     |
| --------------------------- | ---------------- | ------------------- |
| **analytics.view.own**      | Voir ses stats   | Contributeur et +   |
| **analytics.view.region**   | Stats régionales | Admin Régional et + |
| **analytics.view.global**   | Stats globales   | Admin National et + |
| **analytics.export**        | Exporter données | Analyste et +       |
| **analytics.create.report** | Créer rapports   | Analyste et +       |

## 4. Matrice de permissions par rôle

### 4.1 Vue synthétique

```
┌─────────────────────────────────────────────────────────────────┐
│ Rôle / Permission │ Obs │ Cont │ C.Vér │ Valid │ Admin │ Super │
├─────────────────────────────────────────────────────────────────┤
│ EXPRESSIONS       │     │      │       │       │       │       │
│ Créer             │  ❌  │  ✅   │  ✅    │  ✅    │  ✅    │  ✅    │
│ Lire (propres)    │  ❌  │  ✅   │  ✅    │  ✅    │  ✅    │  ✅    │
│ Lire (toutes)     │  ❌  │  ❌   │  ❌    │  ✅    │  ✅    │  ✅    │
│ Modifier (propre) │  ❌  │  ✅   │  ✅    │  ✅    │  ✅    │  ✅    │
│ Supprimer (propre)│  ❌  │  ✅   │  ✅    │  ✅    │  ✅    │  ✅    │
│ Valider           │  ❌  │  ❌   │  ❌    │  ✅    │  ✅    │  ✅    │
├─────────────────────────────────────────────────────────────────┤
│ ENTITÉS          │     │      │       │       │       │       │
│ Créer            │  ❌  │  ❌   │  ✅    │  ✅    │  ✅    │  ✅    │
│ Modifier         │  ❌  │  ❌   │  ❌    │  ✅    │  ✅    │  ✅    │
│ Vérifier         │  ❌  │  ❌   │  ❌    │  ❌    │  ✅    │  ✅    │
├─────────────────────────────────────────────────────────────────┤
│ ADMINISTRATION   │     │      │       │       │       │       │
│ Gérer users      │  ❌  │  ❌   │  ❌    │  ❌    │  ✅    │  ✅    │
│ Assigner rôles   │  ❌  │  ❌   │  ❌    │  ❌    │  ✅    │  ✅    │
│ Config système   │  ❌  │  ❌   │  ❌    │  ❌    │  ❌    │  ✅    │
└─────────────────────────────────────────────────────────────────┘

Légende : ✅ = Autorisé | ❌ = Interdit | Obs = Observateur | Cont = Contributeur
          C.Vér = Contributeur Vérifié | Valid = Validateur | Admin = Admin
```

## 5. Règles de gestion des permissions

### 5.1 Héritages et cascades

```
┌─────────────────────────────────────────────────────────┐
│               RÈGLES D'HÉRITAGE                         │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  1. Un rôle supérieur hérite TOUTES les permissions    │
│     des rôles inférieurs                               │
│                                                         │
│  2. Les permissions peuvent être :                      │
│     • Additives : s'ajoutent aux existantes           │
│     • Exclusives : remplacent les précédentes         │
│     • Restrictives : limitent le scope                │
│                                                         │
│  3. Ordre de priorité :                                │
│     Permission explicite > Rôle > Groupe > Défaut      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 5.2 Permissions contextuelles

Certaines permissions dépendent du contexte :

| Permission              | Contexte         | Exemple          |
| ----------------------- | ---------------- | ---------------- |
| **expression.update**   | État = brouillon | ✅ Peut modifier |
| **expression.update**   | État = publié    | ❌ Ne peut plus  |
| **expression.validate** | Propre région    | ✅ Peut valider  |
| **expression.validate** | Autre région     | ❌ Ne peut pas   |
| **user.assign.role**    | Rôle ≤ son rôle  | ✅ Peut assigner |
| **user.assign.role**    | Rôle > son rôle  | ❌ Ne peut pas   |

## 6. Gestion des périmètres

### 6.1 Types de périmètres

```
┌─────────────────────────────────────────────────────────┐
│                PÉRIMÈTRES D'ACTION                      │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  🌍 Global      : Tout le système                       │
│  🏴 National    : Un pays entier                        │
│  🏘️ Régional    : Une région/état                       │
│  🏢 Local       : Ville/département                     │
│  🎯 Thématique  : Un ou plusieurs piliers               │
│  👥 Groupe      : Ensemble d'utilisateurs               │
│  🔤 Linguistique: Une langue spécifique                 │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 6.2 Matrice périmètre/rôle

| Rôle               | Périmètre par défaut | Extensible | Exemple                   |
| ------------------ | -------------------- | ---------- | ------------------------- |
| **Super Admin**    | Global               | Non        | Tout                      |
| **Admin National** | National             | Non        | France entière            |
| **Admin Régional** | Régional             | Oui        | Île-de-France + Grand Est |
| **Validateur**     | Local/Thématique     | Oui        | Paris + Santé             |
| **Contributeur**   | Personnel            | Non        | Ses propres contenus      |

## 7. Workflows d'attribution

### 7.1 Processus de promotion

```
                    Contributeur
                         │
                         │ 50 expressions validées
                         │ 90% taux approbation
                         ▼
                 Contributeur Vérifié
                         │
                         │ Formation complète
                         │ 6 mois d'ancienneté
                         ▼
                    Validateur
                         │
                         │ 500 validations
                         │ Excellence reconnue
                         ▼
                 Validateur Senior
                         │
                         │ Nomination
                         │ Responsabilités
                         ▼
                  Admin Régional
```

### 7.2 Critères d'attribution

| Rôle cible               | Critères requis                                                        | Processus   | Durée      |
| ------------------------ | ---------------------------------------------------------------------- | ----------- | ---------- |
| **Contributeur Vérifié** | • 10+ expressions publiées<br>• Compte vérifié<br>• 0 avertissement    | Automatique | Immédiat   |
| **Validateur**           | • Contributeur Vérifié<br>• Formation en ligne<br>• Test réussi (80%+) | Semi-auto   | 1 semaine  |
| **Validateur Senior**    | • 6 mois validateur<br>• 500+ validations<br>• Score qualité 95%+      | Nomination  | 2 semaines |
| **Admin Régional**       | • Validateur Senior<br>• Leadership<br>• Recommandations               | Comité      | 1 mois     |

## 8. Sécurité et audit

### 8.1 Journalisation des actions

```
┌─────────────────────────────────────────────────────────┐
│              ÉVÉNEMENTS TRACÉS                          │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  🔴 CRITIQUE : Attribution/retrait de rôle Admin        │
│  🔴 CRITIQUE : Suppression de masse                     │
│  🔴 CRITIQUE : Accès aux données sensibles              │
│                                                         │
│  🟡 IMPORTANT : Validation d'expressions                │
│  🟡 IMPORTANT : Modification d'entités                  │
│  🟡 IMPORTANT : Export de données                       │
│                                                         │
│  🟢 STANDARD : Connexion/déconnexion                    │
│  🟢 STANDARD : Création de contenu                      │
│  🟢 STANDARD : Consultation                             │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 8.2 Alertes automatiques

| Événement                     | Seuil    | Action        | Notification   |
| ----------------------------- | -------- | ------------- | -------------- |
| Tentatives connexion échouées | 5/heure  | Blocage IP    | Admin sécurité |
| Validations massives          | 50/heure | Investigation | Admin régional |
| Suppressions anormales        | 10/jour  | Suspension    | Super admin    |
| Changements de permissions    | Tout     | Log détaillé  | Audit trail    |

## 9. Délégation et suppléance

### 9.1 Mécanismes de délégation

```
┌─────────────────────────────────────────────────────────┐
│             DÉLÉGATION TEMPORAIRE                       │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  Validateur A (en congés)                               │
│       │                                                 │
│       └──► Délègue à Validateur B                      │
│            • Durée : 2 semaines                         │
│            • Périmètre : Ses zones                      │
│            • Permissions : Validation seulement         │
│                                                         │
│  Notification → B accepte → A confirme → Actif         │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 9.2 Règles de délégation

1. **Temporalité** : Toujours limitée dans le temps
2. **Traçabilité** : Toutes les actions tracées au nom des deux
3. **Révocabilité** : Le délégant peut annuler à tout moment
4. **Non-transitivité** : B ne peut pas déléguer à C
5. **Scope limité** : Jamais plus de permissions que le délégant

## 10. Interface de gestion

### 10.1 Dashboard administrateur

```
┌─────────────────────────────────────────────────────────┐
│           GESTION DES RÔLES ET PERMISSIONS              │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  👥 Utilisateurs        🔐 Rôles         ⚙️ Permissions │
│  ├─ Total : 45,231      ├─ Définis : 12  ├─ Active : 47│
│  ├─ Actifs : 38,412     ├─ Utilisés : 11 ├─ Custom : 8 │
│  └─ En ligne : 1,247    └─ Custom : 3    └─ Conflits: 0│
│                                                         │
│  📊 Répartition des rôles :                             │
│  ├─ Contributeurs :     ████████████░░░ 85%            │
│  ├─ Validateurs :       ██░░░░░░░░░░░░ 10%            │
│  ├─ Admins :            █░░░░░░░░░░░░░ 4%             │
│  └─ Autres :            ░░░░░░░░░░░░░░ 1%             │
│                                                         │
│  🚨 Alertes récentes :                                  │
│  • 3 tentatives d'escalade de privilèges               │
│  • 1 compte admin inactif depuis 30j                    │
│  • 12 demandes de rôle en attente                      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 10.2 Outils de gestion

1. **Éditeur visuel de permissions** : Drag & drop des permissions
2. **Simulateur** : Tester les permissions d'un utilisateur
3. **Analyseur de conflits** : Détecter les incohérences
4. **Générateur de rapports** : Audit et conformité
5. **API de gestion** : Intégration avec systèmes tiers

## 11. Évolution et gouvernance

### 11.1 Processus d'évolution

```
Demande nouvelle permission
           │
           ▼
    Analyse d'impact
           │
           ▼
    Comité sécurité
           │
           ▼
    Test en sandbox
           │
           ▼
    Déploiement progressif
           │
           ▼
    Monitoring 30 jours
```

### 11.2 Comité de gouvernance

- Révision trimestrielle des rôles
- Audit annuel des permissions
- Ajustements selon l'usage réel
- Documentation des décisions
- Formation continue des admins

Ce système de permissions garantit à la fois la **sécurité**, la **flexibilité** et la **scalabilité** nécessaires pour gérer des millions d'utilisateurs tout en maintenant l'intégrité du système PillarScan.
