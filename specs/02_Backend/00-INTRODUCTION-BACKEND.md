# Introduction Backend - L'Intelligence derrière PillarScan

## 🧠 Vue d'ensemble pour tous

### En termes simples

Le **backend PillarScan** est le cerveau invisible qui :

- 📥 Reçoit les expressions citoyennes
- 🤖 Les analyse avec intelligence
- 📤 Les envoie aux bonnes personnes
- 📊 Mesure l'impact des actions

```
CITOYEN                    BACKEND                    ACTION
  │                          │                          │
  └──► Expression ──────────►│                          │
                             │► Analyse                 │
                             │► Classification          │
                             │► Routage               ►│► Services
                             │► Suivi                   │
                             │► Notification         ◄─┤
  ◄────── Résultat ◄─────────┤                          │
```

### Pourquoi Strapi ?

Nous avons choisi **Strapi** (CMS headless français 🇫🇷) car :

- ✅ **Plus rapide** : 6 mois au lieu de 18 mois de développement
- ✅ **Plus flexible** : Modification sans coder
- ✅ **Plus sûr** : Communauté active, mises à jour régulières
- ✅ **Plus économique** : -70% de coûts de développement

## 📚 Les documents de cette section

### Pour les décideurs (non-technique)

1. **Ce document** - Vue d'ensemble (15 min)
2. **01-WORKFLOW-MODERATION.md** - Comment garantir la qualité (20 min)
3. **03-ROLES-PERMISSIONS.md** - Qui fait quoi (15 min)

### Pour les équipes techniques

4. **02-ARCHITECTURE-DONNEES.md** - Structure des données
5. **04-PROCESSUS-CLASSIFICATION-IA.md** - Intelligence artificielle
6. **05-FLUX-DONNEES-INTEGRATIONS.md** - Connexions systèmes
7. **06-SECURITE-CONFORMITE.md** - Protection et RGPD

## 🎯 Les enjeux clés du backend

### 1. **Volume et performance**

```
Paris seul = 2.2M habitants
Si 5% utilisent PillarScan = 110,000 utilisateurs
× 3 expressions/mois = 330,000 expressions/mois
× 12 mois = ~4 millions d'expressions/an
```

Le backend doit gérer ce volume sans ralentir !

### 2. **Intelligence et pertinence**

```
Expression brute ──► IA analyse ──► Bonne catégorie
                         │
                         └──► Bon service alerté
                                    │
                                    └──► Action rapide
```

### 3. **Fiabilité et sécurité**

- Disponibilité 99.9% (max 8h/an d'arrêt)
- Protection données personnelles (RGPD)
- Traçabilité complète des actions
- Résistance aux attaques

## 🏗️ Architecture simplifiée

### Les couches du système

```
┌─────────────────────────────────────────────────┐
│              APPLICATIONS FRONTEND               │
│         (Web, Mobile, Bornes, API...)           │
├─────────────────────────────────────────────────┤
│                  API GATEWAY                     │
│            (Point d'entrée unique)              │
├─────────────────────────────────────────────────┤
│                STRAPI CORE                       │
│  ┌─────────────┬──────────────┬──────────────┐ │
│  │  CONTENT    │   BUSINESS    │   PLUGINS    │ │
│  │  TYPES      │   LOGIC       │   SYSTÈME    │ │
│  └─────────────┴──────────────┴──────────────┘ │
├─────────────────────────────────────────────────┤
│              BASE DE DONNÉES                     │
│         (PostgreSQL + Redis cache)              │
├─────────────────────────────────────────────────┤
│            SERVICES EXTERNES                     │
│    (IA, Email, SMS, Géoloc, Stats...)          │
└─────────────────────────────────────────────────┘
```

### Les composants clés

#### 🗄️ **Content Types** (Types de contenu)

Ce sont les "moules" pour stocker les données :

- **Expression** : Ce que dit le citoyen
- **Citoyen** : Qui s'exprime
- **Pilier** : Catégorie (Santé, Transport...)
- **Action** : Ce qui est fait
- **Impact** : Le résultat mesurable

#### ⚙️ **Plugins Strapi**

Extensions qui ajoutent des super-pouvoirs :

- **IA Classification** : Catégorise automatiquement
- **Modération** : Filtre le contenu inapproprié
- **Notifications** : Alerte les bonnes personnes
- **Analytics** : Mesure tout
- **Géolocalisation** : Carte interactive

#### 🔄 **Workflows** (Flux de travail)

Les processus automatisés :

- Réception → Analyse → Classification → Routage
- Modération → Validation → Publication
- Action → Suivi → Notification → Mesure

## 🌟 Ce qui rend notre backend unique

### 1. **Multi-tenant** (Multi-locataire)

```
Une seule installation
        │
        ├── Paris (config spécifique)
        ├── Lyon (config spécifique)
        ├── Marseille (config spécifique)
        └── ... 36,000 communes
```

### 2. **Scalabilité infinie**

```
Charge faible          Charge normale         Charge pic
1 serveur              3 serveurs            10+ serveurs
│                      │ │ │                 │││││││││││
└─► Auto-scaling ─────►└─┴─┴─► Auto-scaling ►└┴┴┴┴┴┴┴┴┴┘
```

### 3. **Intelligence contextuelle**

L'IA comprend le contexte français :

- Argot et expressions locales
- Spécificités régionales
- Urgences selon le contexte
- Historique local

## 📊 Exemples concrets

### Cas 1 : Expression simple

```
"Les poubelles débordent rue de la République Lyon"
                    │
                    ▼
Backend analyse en 0.2 secondes :
├─ Géolocalise : 45.7640°N, 4.8357°E
├─ Catégorise : CADRE DE VIE > Propreté
├─ Urgence : Moyenne (sanitaire)
├─ Route vers : Services Métropole Lyon
└─ Notifie : Responsable secteur
```

### Cas 2 : Urgence détectée

```
"Fuite de gaz forte odeur square Léon Blum Toulouse"
                    │
                    ▼
Backend en mode URGENCE :
├─ Détection mots-clés : "fuite gaz" = DANGER
├─ Alerte immédiate : GRDF + Pompiers
├─ SMS maire + adjoints
├─ Push notification 500m autour
└─ Suivi temps réel activé
```

## 🔐 Sécurité et confidentialité

### Protection des données citoyennes

- ✅ Chiffrement bout en bout
- ✅ Anonymisation possible
- ✅ Droit à l'oubli (RGPD)
- ✅ Audit trail complet

### Conformité réglementaire

- 🇫🇷 RGPD (Europe)
- 🔒 Hébergement données France
- 📜 Certifications SecNumCloud
- 🛡️ Tests sécurité réguliers

## 📈 Performance et monitoring

### Métriques clés surveillées

```
┌────────────────────────────────────────┐
│         TABLEAU DE BORD OPS            │
├────────────────────────────────────────┤
│ Temps réponse API : 45ms ✅            │
│ Disponibilité : 99.97% ✅              │
│ Expressions/minute : 1,234             │
│ Erreurs : 0.02% ✅                     │
│ CPU : 23% ████░░░░░░                  │
│ RAM : 67% ████████░░                  │
│ Queue modération : 89 items            │
└────────────────────────────────────────┘
```

## 🚀 Évolution prévue

### Phase 1 (2024) - MVP

- ✓ Core Strapi
- ✓ IA basique
- ✓ 20 villes pilotes

### Phase 2 (2025) - Scale

- ML avancé
- Blockchain transparency
- 1000+ communes

### Phase 3 (2026) - Innovation

- IA prédictive
- IoT sensors
- API ouverte complète

## 💡 Points clés à retenir

1. **Strapi = Rapidité + Flexibilité**

    - Gain de temps énorme
    - Modification sans coder
    - Communauté active

2. **IA + Humain = Qualité**

    - IA pour la rapidité
    - Humains pour la nuance
    - Amélioration continue

3. **Scalable by design**
    - Commence petit
    - Grandit selon besoins
    - Coûts maîtrisés

## ❓ Questions fréquentes

**Q: Pourquoi pas développer from scratch ?**
R: 18 mois et 3x plus cher pour un résultat similaire

**Q: Strapi peut gérer des millions d'utilisateurs ?**
R: Oui, avec la bonne architecture (voir Netflix, IBM)

**Q: Et si Strapi disparaît ?**
R: Open source = code source disponible, migration possible

**Q: Quelle base de données ?**
R: PostgreSQL (robuste) + Redis (cache rapide)

---

**Prochaine étape** → Découvrez le [01-WORKFLOW-MODERATION.md](./01-WORKFLOW-MODERATION.md) pour comprendre comment nous garantissons la qualité des expressions citoyennes.
