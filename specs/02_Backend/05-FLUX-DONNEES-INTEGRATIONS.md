# Flux de Données et Intégrations - Écosystème PillarScan

## 1. Vue d'ensemble des flux

### 1.1 Architecture des flux de données

L'écosystème PillarScan fonctionne comme une **ville intelligente** où l'information circule entre différents quartiers (modules) via des autoroutes (APIs) et des intersections (intégrations).

```
┌─────────────────────────────────────────────────────────────┐
│                   ÉCOSYSTÈME PILLARSCAN                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│    Mobile App          Web App           Admin Panel        │
│        │                  │                   │             │
│        └──────────────────┴───────────────────┘             │
│                           │                                 │
│                      API Gateway                            │
│                           │                                 │
│    ┌──────────────────────┴──────────────────────┐         │
│    │                                             │         │
│    ▼                    ▼                        ▼         │
│  Strapi            Service IA            External APIs     │
│  Backend           Classification        • Maps            │
│  • Expressions     • NLP                 • Weather         │
│  • Users           • ML Models           • News            │
│  • Media           • Analytics           • Government      │
│    │                    │                        │         │
│    └──────────────────┬─┴────────────────────────┘         │
│                       │                                     │
│                  Database                    Storage        │
│                  PostgreSQL                  S3/CDN         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Types de flux principaux

| Type de flux          | Direction           | Fréquence    | Volume     | Criticité |
| --------------------- | ------------------- | ------------ | ---------- | --------- |
| **Création contenu**  | App → Backend       | Continue     | Moyen      | Haute     |
| **Classification IA** | Backend ↔ IA       | Continue     | Élevé      | Haute     |
| **Synchronisation**   | Backend → Apps      | Temps réel   | Faible     | Moyenne   |
| **Analytics**         | Backend → Dashboard | Batch        | Élevé      | Basse     |
| **Médias**            | App → CDN → Users   | Continue     | Très élevé | Moyenne   |
| **Notifications**     | Backend → Users     | Événementiel | Moyen      | Haute     |

## 2. Flux détaillé : Création d'expression

### 2.1 Diagramme de séquence complet

```
Utilisateur    App Mobile    API Gateway    Strapi    Service IA    CDN    Notifications
    │              │             │            │           │          │          │
    ├─[Rédige]────►│             │            │           │          │          │
    │              ├─[Valide]    │            │           │          │          │
    │              │             │            │           │          │          │
    ├─[Photo]─────►├─[Compress]  │            │           │          │          │
    │              ├─[Upload]────┼────────────┼───────────┼─────────►│          │
    │              │◄────────────┼────────────┼───────────┼──────────┤[URL]     │
    │              │             │            │           │          │          │
    ├─[Soumet]────►├─[Prepare]   │            │           │          │          │
    │              ├─[Send]──────►│            │           │          │          │
    │              │             ├─[Auth]     │           │          │          │
    │              │             ├─[Validate] │           │          │          │
    │              │             ├─[Create]───►│           │          │          │
    │              │             │            ├─[Store]   │          │          │
    │              │             │            ├─[Queue]───►│          │          │
    │              │             │            │           ├─[Process]│          │
    │              │             │            │◄──────────┤[Results] │          │
    │              │             │            ├─[Update]  │          │          │
    │              │             │◄───────────┤[ID]       │          │          │
    │              │◄────────────┤[Response]  │           │          │          │
    │◄─────────────┤[Confirm]    │            │           │          │          │
    │              │             │            ├─[Trigger]─┼──────────┼─────────►│
    │              │             │            │           │          │       [Push]
    │◄─────────────┼─────────────┼────────────┼───────────┼──────────┼──────────┤
```

### 2.2 Étapes détaillées du flux

#### Étape 1 : Préparation côté client

```json
{
    "timestamp": "2024-11-25T10:30:00Z",
    "device": {
        "id": "device-uuid",
        "platform": "iOS",
        "app_version": "1.2.0"
    },
    "expression": {
        "content": "Texte de l'expression...",
        "type": "problem",
        "urgency": 4,
        "location": {
            "lat": 3.848,
            "lng": 11.5021,
            "accuracy": 10
        },
        "media_ids": ["media-1-uuid", "media-2-uuid"]
    },
    "user_context": {
        "locale": "fr_CM",
        "network": "4G",
        "battery": 75
    }
}
```

#### Étape 2 : Validation API Gateway

- Authentification JWT
- Rate limiting (10 req/min)
- Validation schéma
- Sanitization inputs

#### Étape 3 : Traitement Strapi

- Création entité Expression
- Association médias
- Déclenchement webhooks
- Audit logging

#### Étape 4 : Classification IA asynchrone

- Message queue (RabbitMQ)
- Processing parallèle
- Callback results
- Update expression

## 3. Flux détaillé : Modération et validation

### 3.1 États et transitions

```
┌─────────────────────────────────────────────────────────────┐
│                  MACHINE À ÉTATS - MODÉRATION               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│     ┌─────────┐      ┌─────────┐      ┌─────────┐        │
│     │BROUILLON│      │EN REVUE │      │ PUBLIÉ  │        │
│     └────┬────┘      └────┬────┘      └────┬────┘        │
│          │                 │                 │             │
│          ▼                 ▼                 ▼             │
│    [Soumettre]       [Approuver]      [Archiver]         │
│          │            [Rejeter]             │             │
│          │                 │                 │             │
│          ▼                 ▼                 ▼             │
│     ┌─────────┐      ┌─────────┐      ┌─────────┐        │
│     │EN ATTENTE│◄─────│ REJETÉ  │      │ ARCHIVÉ │        │
│     └─────────┘      └─────────┘      └─────────┘        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Flux de données modération

```
                  Expression soumise
                         │
                         ▼
                 ┌───────────────┐
                 │ File attente  │
                 │  Modération   │
                 └───────┬───────┘
                         │
          ┌──────────────┴──────────────┐
          │                             │
          ▼                             ▼
    Validateur A                  Validateur B
    (Principal)                   (Backup)
          │                             │
          ▼                             │
    Notification Push                   │
    + Email                            │
          │                             │
          ▼                             │
    Interface Web                       │
    Modération                         │
          │                             │
    [Decision prise]                    │
          │                             │
          ▼                             │
    Update Status ──────────────────────┘
          │
          ▼
    Notifications
    • Auteur
    • Système
    • Analytics
```

## 4. Intégrations externes

### 4.1 Services cartographiques

```
┌─────────────────────────────────────────────────────────────┐
│               INTÉGRATION SERVICES MAPS                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PillarScan                         Service Maps            │
│  ┌─────────────┐    REST API      ┌──────────────┐        │
│  │ Expression  │ ─────────────────►│ Geocoding    │        │
│  │ + GPS       │   GET /geocode    │ Service      │        │
│  └─────────────┘                   └──────┬───────┘        │
│         ▲                                  │                │
│         │          Enriched data           │                │
│         └──────────────────────────────────┘                │
│                                                             │
│  Données envoyées :           Données reçues :              │
│  • Latitude/Longitude         • Adresse complète           │
│  • Adresse partielle         • Quartier/Ville/Région      │
│                              • Points d'intérêt proches    │
│                              • Données démographiques      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 APIs gouvernementales

| API                         | Usage             | Fréquence    | Données                |
| --------------------------- | ----------------- | ------------ | ---------------------- |
| **Registre Entreprises**    | Vérifier entités  | À la demande | SIRET, statut, adresse |
| **Statistiques Nationales** | Contexte régional | Quotidien    | Population, économie   |
| **Météo Nationale**         | Corrélations      | Horaire      | Conditions, alertes    |
| **Santé Publique**          | Épidémies         | Temps réel   | Alertes, capacités     |
| **Éducation**               | Établissements    | Hebdomadaire | Écoles, effectifs      |

### 4.3 Pattern d'intégration résilient

```
┌─────────────────────────────────────────────────────────────┐
│              PATTERN CIRCUIT BREAKER                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  État: FERMÉ (Normal)                                       │
│  ├─ Requêtes passent                                       │
│  ├─ Compteur échecs = 0                                    │
│  └─ Si échecs > seuil → OUVERT                            │
│                                                             │
│  État: OUVERT (Protection)                                  │
│  ├─ Requêtes bloquées                                      │
│  ├─ Retour immédiat erreur                                 │
│  └─ Après timeout → SEMI-OUVERT                            │
│                                                             │
│  État: SEMI-OUVERT (Test)                                   │
│  ├─ 1 requête test                                         │
│  ├─ Si succès → FERMÉ                                      │
│  └─ Si échec → OUVERT                                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 5. Synchronisation temps réel

### 5.1 WebSocket pour updates live

```
┌─────────────────────────────────────────────────────────────┐
│                 ARCHITECTURE WEBSOCKET                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│   Clients                    Load Balancer                  │
│   ┌─────┐                    ┌─────────┐                  │
│   │App 1│◄──────────────────►│         │                  │
│   └─────┘                    │  Nginx  │                  │
│   ┌─────┐                    │         │                  │
│   │App 2│◄──────────────────►│         │                  │
│   └─────┘                    └────┬────┘                  │
│   ┌─────┐                         │                        │
│   │App 3│◄────────────┬──────────┘                        │
│   └─────┘             │                                    │
│                       ▼                                    │
│              ┌─────────────────┐                          │
│              │  WebSocket      │                          │
│              │  Server Cluster │                          │
│              └────────┬────────┘                          │
│                       │                                    │
│                       ▼                                    │
│              ┌─────────────────┐                          │
│              │   Redis Pub/Sub │                          │
│              └─────────────────┘                          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 Événements temps réel

| Événement                 | Canal         | Destinataires       | Payload              |
| ------------------------- | ------------- | ------------------- | -------------------- |
| **expression.created**    | user:{id}     | Auteur              | Expression complète  |
| **expression.validated**  | user:{id}     | Auteur + Validateur | Statut + feedback    |
| **expression.classified** | user:{id}     | Auteur              | Classification IA    |
| **notification.new**      | user:{id}     | Destinataire        | Message + action     |
| **stats.updated**         | region:{code} | Admins région       | Métriques temps réel |

## 6. Gestion des médias

### 6.1 Pipeline de traitement médias

```
                Upload initial
                      │
                      ▼
              ┌──────────────┐
              │  Validation  │
              │ • Type       │
              │ • Taille     │
              │ • Antivirus  │
              └──────┬───────┘
                     │
                     ▼
              ┌──────────────┐
              │ Optimisation │
              │ • Compression│
              │ • Resize     │
              │ • Watermark  │
              └──────┬───────┘
                     │
        ┌────────────┴────────────┐
        │                         │
        ▼                         ▼
┌──────────────┐         ┌──────────────┐
│  Stockage    │         │  Génération  │
│  Original    │         │  Thumbnails  │
│  (S3 privé)  │         │  (CDN public)│
└──────────────┘         └──────────────┘
```

### 6.2 Stratégie de cache CDN

```
┌─────────────────────────────────────────────────────────────┐
│                    HIÉRARCHIE CACHE                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  L1: Browser Cache                                          │
│  ├─ Images: 1 an (immutable)                               │
│  ├─ CSS/JS: 1 mois (versioned)                             │
│  └─ API: No cache                                          │
│                                                             │
│  L2: CDN Edge Cache                                         │
│  ├─ Images: 1 an                                           │
│  ├─ Thumbnails: 1 semaine                                  │
│  └─ Dynamic resize: 1 jour                                 │
│                                                             │
│  L3: Origin Cache (Varnish)                                │
│  ├─ Static assets: 1 jour                                  │
│  ├─ API responses: 5 minutes                               │
│  └─ User-specific: No cache                                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 7. Analytics et reporting

### 7.1 Pipeline de données analytiques

```
    Événements utilisateurs
             │
             ▼
    ┌─────────────────┐
    │  Collecte       │
    │  (Amplitude)    │
    └────────┬────────┘
             │
             ▼
    ┌─────────────────┐
    │  Streaming      │
    │  (Kafka)        │
    └────────┬────────┘
             │
    ┌────────┴────────┐
    │                 │
    ▼                 ▼
┌──────────┐   ┌──────────┐
│ Real-time │   │  Batch   │
│ Analytics │   │ Analytics│
│(Spark)    │   │(Airflow) │
└─────┬─────┘   └─────┬────┘
      │               │
      └───────┬───────┘
              │
              ▼
    ┌─────────────────┐
    │  Data Warehouse │
    │  (BigQuery)     │
    └────────┬────────┘
             │
             ▼
    ┌─────────────────┐
    │  Dashboards     │
    │  (Metabase)     │
    └─────────────────┘
```

### 7.2 Métriques clés trackées

| Catégorie       | Métriques          | Granularité | Usage      |
| --------------- | ------------------ | ----------- | ---------- |
| **Engagement**  | DAU, MAU, Sessions | Heure/Jour  | Product    |
| **Contenu**     | Expressions/user   | Jour        | Community  |
| **Qualité**     | Taux validation    | Temps réel  | Modération |
| **Performance** | Latence, Errors    | Minute      | Tech       |
| **Impact**      | Piliers trends     | Semaine     | Strategy   |

## 8. Sécurité des flux

### 8.1 Chiffrement bout en bout

```
┌─────────────────────────────────────────────────────────────┐
│                 SÉCURITÉ DES FLUX                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  App Mobile                                Backend          │
│  ┌─────────┐     TLS 1.3      ┌─────────┐                 │
│  │ Données │ ◄───────────────► │   API   │                 │
│  │ Chiffrées│     + Cert Pin    │ Gateway │                 │
│  └─────────┘                   └────┬────┘                 │
│                                     │                       │
│                                     ▼                       │
│                            ┌─────────────┐                  │
│                            │   Strapi    │                  │
│                            │  Encrypted  │                  │
│                            │  at rest    │                  │
│                            └──────┬──────┘                  │
│                                   │                         │
│                                   ▼                         │
│                            ┌─────────────┐                  │
│                            │  PostgreSQL │                  │
│                            │  TDE + RBAC │                  │
│                            └─────────────┘                  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 Audit et compliance

- Tous les flux sont loggés
- Rétention 90 jours minimum
- Anonymisation après 1 an
- Conformité RGPD/CCPA
- Audit trails immutables

## 9. Optimisations et performance

### 9.1 Stratégies d'optimisation

| Technique        | Application          | Gain              |
| ---------------- | -------------------- | ----------------- |
| **Compression**  | API responses (gzip) | -70% bandwidth    |
| **Pagination**   | Listes longues       | -90% load time    |
| **Lazy loading** | Images/médias        | -50% initial load |
| **Debouncing**   | Recherche/filtres    | -80% requests     |
| **Caching**      | Données statiques    | -95% DB queries   |
| **CDN**          | Assets globaux       | -60% latency      |

### 9.2 Monitoring des flux

```
┌─────────────────────────────────────────────────────────────┐
│              DASHBOARD MONITORING FLUX                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  API Gateway:          Backend:           Database:         │
│  • RPS: 1,250         • CPU: 45%         • Connections: 120│
│  • P99: 250ms         • RAM: 67%         • Slow queries: 3 │
│  • Errors: 0.1%       • Disk I/O: 30%    • Replication: OK │
│                                                             │
│  Top endpoints:                                             │
│  1. GET /expressions    45% ████████                       │
│  2. POST /expressions   25% ████                           │
│  3. GET /media         20% ███                             │
│  4. PUT /validate      10% ██                              │
│                                                             │
│  Alertes actives:                                           │
│  ⚠️ Latence élevée région Europe (+50ms)                   │
│  ⚠️ Queue classification: 450 messages                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 10. Plan de reprise et résilience

### 10.1 Architecture haute disponibilité

```
┌─────────────────────────────────────────────────────────────┐
│                    MULTI-RÉGION SETUP                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Region A (Primary)          Region B (Standby)            │
│  ┌──────────────┐           ┌──────────────┐              │
│  │   App Stack  │           │   App Stack  │              │
│  │   (Active)   │◄─────────►│  (Passive)   │              │
│  └──────┬───────┘   Sync    └──────┬───────┘              │
│         │                           │                       │
│         ▼                           ▼                       │
│  ┌──────────────┐           ┌──────────────┐              │
│  │   Database   │◄─────────►│   Database   │              │
│  │   (Master)   │ Streaming │   (Replica)  │              │
│  └──────────────┘           └──────────────┘              │
│                                                             │
│  RTO: 15 minutes            RPO: 5 minutes                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 10.2 Procédures de failover

1. **Détection** : Health checks toutes les 30s
2. **Décision** : Automatique si 3 checks fail
3. **Bascule** : DNS update + promotion replica
4. **Validation** : Tests automatisés
5. **Communication** : Alertes équipes + users

Cette architecture de flux garantit la **fiabilité**, la **performance** et la **scalabilité** nécessaires pour supporter des millions d'utilisateurs actifs tout en maintenant une expérience fluide et temps réel.
