// <PERSON>ript to create a profile for the admin user
const sqlite3 = require("better-sqlite3");
const path = require("path");
const crypto = require("crypto");

// Open the database
const dbPath = path.join(__dirname, ".tmp/data.db");
console.log("Opening database:", dbPath);

try {
    const db = sqlite3(dbPath);

    // Get the admin user
    const adminUser = db
        .prepare("SELECT id, username, email FROM up_users WHERE email = ?")
        .get("<EMAIL>");

    if (!adminUser) {
        console.error("Admin user not found!");
        process.exit(1);
    }

    console.log("Found admin user:", adminUser);

    // Check if profile already exists
    const existingLink = db
        .prepare("SELECT * FROM profiles_user_lnk WHERE user_id = ?")
        .get(adminUser.id);

    if (existingLink) {
        console.log("Profile already linked to user");
        const profile = db
            .prepare("SELECT * FROM profiles WHERE id = ?")
            .get(existingLink.profile_id);
        console.log("Existing profile:", profile);
    } else {
        // Generate a unique document_id for Strapi 5
        const documentId = crypto.randomBytes(16).toString("hex");

        // Create a new profile
        const insertProfile = db.prepare(`
      INSERT INTO profiles (
        document_id,
        nom, 
        role, 
        statut, 
        preferences, 
        date_inscription, 
        derniere_activite,
        nb_expressions,
        score_reputation,
        compte_verifie,
        created_at,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

        const now = new Date().toISOString();
        const preferences = JSON.stringify({
            notifications_email: true,
            notifications_push: true,
            langue: "fr",
            theme: "auto",
        });

        const result = insertProfile.run(
            documentId,
            "Admin User",
            "super_admin",
            "actif",
            preferences,
            now,
            now,
            0,
            0.0,
            1, // verified
            now,
            now,
        );

        console.log("Profile created with ID:", result.lastInsertRowid);
        console.log("Document ID:", documentId);

        // Link profile to user
        const linkProfile = db.prepare(
            "INSERT INTO profiles_user_lnk (profile_id, user_id) VALUES (?, ?)",
        );
        linkProfile.run(result.lastInsertRowid, adminUser.id);

        console.log("Profile linked to user successfully!");
    }

    // Verify the link
    const verification = db
        .prepare(
            `
    SELECT u.id as user_id, u.username, u.email, p.id as profile_id, p.document_id, p.nom, p.role
    FROM up_users u
    LEFT JOIN profiles_user_lnk pul ON u.id = pul.user_id
    LEFT JOIN profiles p ON pul.profile_id = p.id
    WHERE u.id = ?
  `,
        )
        .get(adminUser.id);

    console.log("\nVerification:", verification);

    db.close();
    console.log("\nProfile setup completed!");
} catch (error) {
    console.error("Error:", error);
}
