import type { Schema, Struct } from "@strapi/strapi";

export interface AdminApiToken extends Struct.CollectionTypeSchema {
    collectionName: "strapi_api_tokens";
    info: {
        description: "";
        displayName: "Api Token";
        name: "Api Token";
        pluralName: "api-tokens";
        singularName: "api-token";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        accessKey: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }> &
            Schema.Attribute.DefaultTo<"">;
        encryptedKey: Schema.Attribute.Text &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        expiresAt: Schema.Attribute.DateTime;
        lastUsedAt: Schema.Attribute.DateTime;
        lifespan: Schema.Attribute.BigInteger;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::api-token"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "admin::api-token-permission">;
        publishedAt: Schema.Attribute.DateTime;
        type: Schema.Attribute.Enumeration<["read-only", "full-access", "custom"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"read-only">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
    collectionName: "strapi_api_token_permissions";
    info: {
        description: "";
        displayName: "API Token Permission";
        name: "API Token Permission";
        pluralName: "api-token-permissions";
        singularName: "api-token-permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::api-token-permission"> &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        token: Schema.Attribute.Relation<"manyToOne", "admin::api-token">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
    collectionName: "admin_permissions";
    info: {
        description: "";
        displayName: "Permission";
        name: "Permission";
        pluralName: "permissions";
        singularName: "permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::permission"> &
            Schema.Attribute.Private;
        properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        publishedAt: Schema.Attribute.DateTime;
        role: Schema.Attribute.Relation<"manyToOne", "admin::role">;
        subject: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
    collectionName: "admin_roles";
    info: {
        description: "";
        displayName: "Role";
        name: "Role";
        pluralName: "roles";
        singularName: "role";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        code: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::role"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "admin::permission">;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        users: Schema.Attribute.Relation<"manyToMany", "admin::user">;
    };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
    collectionName: "strapi_transfer_tokens";
    info: {
        description: "";
        displayName: "Transfer Token";
        name: "Transfer Token";
        pluralName: "transfer-tokens";
        singularName: "transfer-token";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        accessKey: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }> &
            Schema.Attribute.DefaultTo<"">;
        expiresAt: Schema.Attribute.DateTime;
        lastUsedAt: Schema.Attribute.DateTime;
        lifespan: Schema.Attribute.BigInteger;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::transfer-token"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "admin::transfer-token-permission">;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminTransferTokenPermission extends Struct.CollectionTypeSchema {
    collectionName: "strapi_transfer_token_permissions";
    info: {
        description: "";
        displayName: "Transfer Token Permission";
        name: "Transfer Token Permission";
        pluralName: "transfer-token-permissions";
        singularName: "transfer-token-permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::transfer-token-permission"> &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        token: Schema.Attribute.Relation<"manyToOne", "admin::transfer-token">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
    collectionName: "admin_users";
    info: {
        description: "";
        displayName: "User";
        name: "User";
        pluralName: "users";
        singularName: "user";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        blocked: Schema.Attribute.Boolean &
            Schema.Attribute.Private &
            Schema.Attribute.DefaultTo<false>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        email: Schema.Attribute.Email &
            Schema.Attribute.Required &
            Schema.Attribute.Private &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        firstname: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        isActive: Schema.Attribute.Boolean &
            Schema.Attribute.Private &
            Schema.Attribute.DefaultTo<false>;
        lastname: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "admin::user"> &
            Schema.Attribute.Private;
        password: Schema.Attribute.Password &
            Schema.Attribute.Private &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        preferedLanguage: Schema.Attribute.String;
        publishedAt: Schema.Attribute.DateTime;
        registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
        resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
        roles: Schema.Attribute.Relation<"manyToMany", "admin::role"> & Schema.Attribute.Private;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        username: Schema.Attribute.String;
    };
}

export interface ApiActionAction extends Struct.CollectionTypeSchema {
    collectionName: "actions";
    info: {
        description: "Actions taken in response to citizen expressions";
        displayName: "Action";
        pluralName: "actions";
        singularName: "action";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        budget_estime: Schema.Attribute.Decimal;
        budget_reel: Schema.Attribute.Decimal;
        commentaires_publics: Schema.Attribute.Text;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        date_creation: Schema.Attribute.DateTime;
        date_debut_prevue: Schema.Attribute.DateTime;
        date_debut_reelle: Schema.Attribute.DateTime;
        date_fin_prevue: Schema.Attribute.DateTime;
        date_fin_reelle: Schema.Attribute.DateTime;
        description: Schema.Attribute.Text & Schema.Attribute.Required;
        entite_responsable: Schema.Attribute.Relation<"manyToOne", "api::entite.entite">;
        expression: Schema.Attribute.Relation<"manyToOne", "api::expression.expression"> &
            Schema.Attribute.Required;
        impact_mesure: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::action.action"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        preuves: Schema.Attribute.Media<"images" | "videos" | "files", true>;
        priorite: Schema.Attribute.Enumeration<["basse", "normale", "haute", "critique"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"normale">;
        progression: Schema.Attribute.Integer &
            Schema.Attribute.SetMinMax<
                {
                    max: 100;
                    min: 0;
                },
                number
            > &
            Schema.Attribute.DefaultTo<0>;
        publishedAt: Schema.Attribute.DateTime;
        responsable: Schema.Attribute.Relation<"manyToOne", "api::profile.profile">;
        resultats: Schema.Attribute.Text;
        statut: Schema.Attribute.Enumeration<
            ["planifiee", "en_cours", "terminee", "suspendue", "annulee"]
        > &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"planifiee">;
        titre: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        type_action: Schema.Attribute.Enumeration<
            [
                "investigation",
                "reparation",
                "amelioration",
                "communication",
                "formation",
                "politique",
                "autre",
            ]
        > &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        visible_public: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    };
}

export interface ApiEntiteEntite extends Struct.CollectionTypeSchema {
    collectionName: "entites";
    info: {
        description: "Organizations, people, and groups mentioned in expressions";
        displayName: "Entit\u00E9";
        pluralName: "entites";
        singularName: "entite";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        actif: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<true>;
        contacts: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        derniere_mention: Schema.Attribute.DateTime;
        description: Schema.Attribute.Text;
        expressions: Schema.Attribute.Relation<"manyToMany", "api::expression.expression">;
        identifiants: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        lieu_principal: Schema.Attribute.Relation<"manyToOne", "api::lieu.lieu">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::entite.entite"> &
            Schema.Attribute.Private;
        logo: Schema.Attribute.Media<"images">;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        nb_mentions: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
        nom: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        publishedAt: Schema.Attribute.DateTime;
        reputation_score: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
        reseaux_sociaux: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        responsable: Schema.Attribute.Relation<"manyToOne", "api::profile.profile">;
        site_web: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 255;
            }>;
        sous_type: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        type: Schema.Attribute.Enumeration<["personne", "organisation", "groupe", "service"]> &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        verifie: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    };
}

export interface ApiExpressionExpression extends Struct.CollectionTypeSchema {
    collectionName: "expressions";
    info: {
        description: "Citizen expressions - the core content of PillarScan";
        displayName: "Expression";
        pluralName: "expressions";
        singularName: "expression";
    };
    options: {
        draftAndPublish: true;
    };
    attributes: {
        actions_prises: Schema.Attribute.Relation<"oneToMany", "api::action.action">;
        analyse_ia: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                categorie_suggeree: "";
                entites_extraites: [];
                mots_cles: [];
                score_confiance: 0;
                sentiment_score: 0;
                urgence_calculee: 3;
            }>;
        auteur: Schema.Attribute.Relation<"manyToOne", "api::profile.profile"> &
            Schema.Attribute.Required;
        contenu: Schema.Attribute.Text &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 10;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        date_creation: Schema.Attribute.DateTime;
        date_evenement: Schema.Attribute.Date & Schema.Attribute.Required;
        date_publication: Schema.Attribute.DateTime;
        date_resolution: Schema.Attribute.DateTime;
        date_soumission: Schema.Attribute.DateTime;
        entites: Schema.Attribute.Relation<"manyToMany", "api::entite.entite">;
        etat_emotionnel: Schema.Attribute.Enumeration<
            ["colere", "joie", "tristesse", "espoir", "neutre", "frustration"]
        > &
            Schema.Attribute.DefaultTo<"neutre">;
        geolocation: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        impact: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                commentaires: 0;
                partages: 0;
                soutiens: 0;
                vues: 0;
            }>;
        langue: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 5;
            }> &
            Schema.Attribute.DefaultTo<"fr">;
        lieu: Schema.Attribute.Relation<"manyToOne", "api::lieu.lieu"> & Schema.Attribute.Required;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::expression.expression"> &
            Schema.Attribute.Private;
        medias: Schema.Attribute.Media<"images" | "videos" | "files", true>;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        piliers: Schema.Attribute.Relation<"manyToMany", "api::pilier.pilier">;
        publishedAt: Schema.Attribute.DateTime;
        raison_rejet: Schema.Attribute.Text;
        satisfaction_resolution: Schema.Attribute.Integer &
            Schema.Attribute.SetMinMax<
                {
                    max: 5;
                    min: 1;
                },
                number
            >;
        score_ia: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        source: Schema.Attribute.Enumeration<["web", "mobile", "api", "import"]> &
            Schema.Attribute.DefaultTo<"web">;
        sous_piliers: Schema.Attribute.Relation<"manyToMany", "api::sous-pilier.sous-pilier">;
        statut: Schema.Attribute.Enumeration<
            ["brouillon", "en_moderation", "publie", "en_traitement", "resolu", "rejete", "archive"]
        > &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"brouillon">;
        tags: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
        titre: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        type_expression: Schema.Attribute.Enumeration<
            ["probleme", "satisfaction", "idee", "question"]
        > &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        urgence: Schema.Attribute.Integer &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMax<
                {
                    max: 5;
                    min: 1;
                },
                number
            > &
            Schema.Attribute.DefaultTo<3>;
        validateur: Schema.Attribute.Relation<"manyToOne", "api::validateur.validateur">;
        version: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<1>;
    };
}

export interface ApiLieuLieu extends Struct.CollectionTypeSchema {
    collectionName: "lieux";
    info: {
        description: "Geographic locations for expressions and user residence";
        displayName: "Lieu";
        pluralName: "lieux";
        singularName: "lieu";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        actif: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<true>;
        adresse_complete: Schema.Attribute.Text;
        code_postal: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 10;
            }>;
        coordonnees: Schema.Attribute.JSON & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        departement: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        enfants: Schema.Attribute.Relation<"oneToMany", "api::lieu.lieu">;
        entites: Schema.Attribute.Relation<"oneToMany", "api::entite.entite">;
        expressions: Schema.Attribute.Relation<"oneToMany", "api::expression.expression">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::lieu.lieu"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        niveau: Schema.Attribute.Enumeration<
            ["pays", "region", "departement", "ville", "quartier", "rue"]
        > &
            Schema.Attribute.Required;
        nom: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        parent: Schema.Attribute.Relation<"manyToOne", "api::lieu.lieu">;
        pays: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 2;
            }> &
            Schema.Attribute.DefaultTo<"FR">;
        publishedAt: Schema.Attribute.DateTime;
        region: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        residents: Schema.Attribute.Relation<"oneToMany", "api::profile.profile">;
        rue: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        type: Schema.Attribute.Enumeration<
            ["adresse", "zone", "point_interet", "commune", "departement", "region", "pays"]
        > &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"adresse">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        verifie: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        ville: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
    };
}

export interface ApiNotificationNotification extends Struct.CollectionTypeSchema {
    collectionName: "notifications";
    info: {
        description: "User notifications for expressions and system events";
        displayName: "Notification";
        pluralName: "notifications";
        singularName: "notification";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        action_label: Schema.Attribute.String;
        action_url: Schema.Attribute.String;
        category: Schema.Attribute.Enumeration<
            ["expression", "moderation", "system", "social", "update"]
        > &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"system">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        expression: Schema.Attribute.Relation<"manyToOne", "api::expression.expression">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::notification.notification"> &
            Schema.Attribute.Private;
        message: Schema.Attribute.Text & Schema.Attribute.Required;
        metadata: Schema.Attribute.JSON;
        publishedAt: Schema.Attribute.DateTime;
        read: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<false>;
        recipient: Schema.Attribute.Relation<"manyToOne", "api::profile.profile">;
        title: Schema.Attribute.String & Schema.Attribute.Required;
        type: Schema.Attribute.Enumeration<["info", "success", "warning", "error"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"info">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiPerimetrePerimetre extends Struct.CollectionTypeSchema {
    collectionName: "perimetres";
    info: {
        description: "Geographic or thematic perimeters for validator assignments";
        displayName: "P\u00E9rim\u00E8tre";
        pluralName: "perimetres";
        singularName: "perimetre";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        actif: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<true>;
        coordonnees_geo: Schema.Attribute.JSON;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        criteres_inclusion: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        description: Schema.Attribute.Text;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::perimetre.perimetre"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        nom: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 200;
            }>;
        piliers_concernes: Schema.Attribute.Relation<"manyToMany", "api::pilier.pilier">;
        priorite: Schema.Attribute.Integer &
            Schema.Attribute.SetMinMax<
                {
                    max: 10;
                    min: 1;
                },
                number
            > &
            Schema.Attribute.DefaultTo<1>;
        publishedAt: Schema.Attribute.DateTime;
        type: Schema.Attribute.Enumeration<
            [
                "global",
                "national",
                "regional",
                "departemental",
                "local",
                "thematique",
                "linguistique",
            ]
        > &
            Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        validateurs: Schema.Attribute.Relation<"manyToMany", "api::validateur.validateur">;
        zones_geographiques: Schema.Attribute.Relation<"manyToMany", "api::lieu.lieu">;
    };
}

export interface ApiPilierPilier extends Struct.CollectionTypeSchema {
    collectionName: "piliers";
    info: {
        description: "The 12 pillars of French society for expression classification";
        displayName: "Pilier";
        pluralName: "piliers";
        singularName: "pilier";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        actif: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<true>;
        code: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 20;
            }>;
        couleur: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 7;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.Text & Schema.Attribute.Required;
        domaine: Schema.Attribute.Enumeration<
            ["services_publics", "vie_quotidienne", "economie_social", "gouvernance_democratie"]
        > &
            Schema.Attribute.Required;
        expressions: Schema.Attribute.Relation<"manyToMany", "api::expression.expression">;
        icone: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 50;
            }>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::pilier.pilier"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        nom: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        nom_en: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        ordre: Schema.Attribute.Integer &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMax<
                {
                    max: 12;
                    min: 1;
                },
                number
            >;
        publishedAt: Schema.Attribute.DateTime;
        sous_piliers: Schema.Attribute.Relation<"oneToMany", "api::sous-pilier.sous-pilier">;
        statistiques: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                nb_expressions_mois: 0;
                nb_expressions_total: 0;
                taux_resolution: 0;
                temps_moyen_resolution: 0;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        validateurs_specialistes: Schema.Attribute.Relation<
            "manyToMany",
            "api::validateur.validateur"
        >;
    };
}

export interface ApiProfileProfile extends Struct.CollectionTypeSchema {
    collectionName: "profiles";
    info: {
        description: "Citizen profile with extended information for PillarScan";
        displayName: "Profile";
        pluralName: "profiles";
        singularName: "profile";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        compte_verifie: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        date_inscription: Schema.Attribute.DateTime;
        date_naissance: Schema.Attribute.Date;
        derniere_activite: Schema.Attribute.DateTime;
        expressions: Schema.Attribute.Relation<"oneToMany", "api::expression.expression">;
        genre: Schema.Attribute.Enumeration<["M", "F", "Autre"]> &
            Schema.Attribute.DefaultTo<"Autre">;
        lieu_residence: Schema.Attribute.Relation<"manyToOne", "api::lieu.lieu">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::profile.profile"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        nb_expressions: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
        nom: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        preferences: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                langue: "fr";
                notifications_email: true;
                notifications_push: true;
                theme: "auto";
            }>;
        publishedAt: Schema.Attribute.DateTime;
        role: Schema.Attribute.Enumeration<
            [
                "observateur",
                "contributeur",
                "contributeur_verifie",
                "validateur",
                "validateur_senior",
                "admin_regional",
                "admin_national",
                "super_admin",
            ]
        > &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"contributeur">;
        score_reputation: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
        statut: Schema.Attribute.Enumeration<["actif", "suspendu", "inactif"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"actif">;
        telephone: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 20;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        user: Schema.Attribute.Relation<"oneToOne", "plugin::users-permissions.user">;
        validateur_assigne: Schema.Attribute.Relation<"manyToOne", "api::validateur.validateur">;
    };
}

export interface ApiSousPilierSousPilier extends Struct.CollectionTypeSchema {
    collectionName: "sous_piliers";
    info: {
        description: "Sub-categories of the main pillars for detailed classification";
        displayName: "Sous-Pilier";
        pluralName: "sous-piliers";
        singularName: "sous-pilier";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        actif: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<true>;
        code: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 30;
            }>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.Text;
        expressions: Schema.Attribute.Relation<"manyToMany", "api::expression.expression">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::sous-pilier.sous-pilier"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        mots_cles: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
        nom: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        nom_en: Schema.Attribute.String &
            Schema.Attribute.SetMinMaxLength<{
                maxLength: 100;
            }>;
        ordre: Schema.Attribute.Integer &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMax<
                {
                    min: 1;
                },
                number
            >;
        pilier: Schema.Attribute.Relation<"manyToOne", "api::pilier.pilier"> &
            Schema.Attribute.Required;
        publishedAt: Schema.Attribute.DateTime;
        statistiques: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                nb_expressions_mois: 0;
                nb_expressions_total: 0;
                taux_resolution: 0;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface ApiValidateurValidateur extends Struct.CollectionTypeSchema {
    collectionName: "validateurs";
    info: {
        description: "Validators who moderate and approve citizen expressions";
        displayName: "Validateur";
        pluralName: "validateurs";
        singularName: "validateur";
    };
    options: {
        draftAndPublish: false;
    };
    attributes: {
        actif: Schema.Attribute.Boolean &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<true>;
        certifications: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        date_derniere_activite: Schema.Attribute.DateTime;
        date_nomination: Schema.Attribute.DateTime;
        disponibilite: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                horaires: "9h-18h";
                jours: ["lundi", "mardi", "mercredi", "jeudi", "vendredi"];
                vacances: false;
            }>;
        expressions_validees: Schema.Attribute.Relation<"oneToMany", "api::expression.expression">;
        formations_completees: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "api::validateur.validateur"> &
            Schema.Attribute.Private;
        metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
        niveau: Schema.Attribute.Enumeration<["junior", "senior", "expert", "formateur"]> &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"junior">;
        notes_performance: Schema.Attribute.Text;
        perimetres: Schema.Attribute.Relation<"manyToMany", "api::perimetre.perimetre">;
        profile: Schema.Attribute.Relation<"oneToOne", "api::profile.profile"> &
            Schema.Attribute.Required;
        profiles_suivis: Schema.Attribute.Relation<"oneToMany", "api::profile.profile">;
        publishedAt: Schema.Attribute.DateTime;
        quota_jour: Schema.Attribute.Integer &
            Schema.Attribute.SetMinMax<
                {
                    max: 200;
                    min: 1;
                },
                number
            > &
            Schema.Attribute.DefaultTo<50>;
        quota_utilise_jour: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
        specialites: Schema.Attribute.Relation<"manyToMany", "api::pilier.pilier">;
        statistiques: Schema.Attribute.JSON &
            Schema.Attribute.DefaultTo<{
                nb_validations_mois: 0;
                nb_validations_total: 0;
                score_qualite: 0;
                taux_approbation: 0;
                temps_moyen_validation: 0;
            }>;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginContentReleasesRelease extends Struct.CollectionTypeSchema {
    collectionName: "strapi_releases";
    info: {
        displayName: "Release";
        pluralName: "releases";
        singularName: "release";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        actions: Schema.Attribute.Relation<"oneToMany", "plugin::content-releases.release-action">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::content-releases.release"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String & Schema.Attribute.Required;
        publishedAt: Schema.Attribute.DateTime;
        releasedAt: Schema.Attribute.DateTime;
        scheduledAt: Schema.Attribute.DateTime;
        status: Schema.Attribute.Enumeration<["ready", "blocked", "failed", "done", "empty"]> &
            Schema.Attribute.Required;
        timezone: Schema.Attribute.String;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginContentReleasesReleaseAction extends Struct.CollectionTypeSchema {
    collectionName: "strapi_release_actions";
    info: {
        displayName: "Release Action";
        pluralName: "release-actions";
        singularName: "release-action";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        contentType: Schema.Attribute.String & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        entryDocumentId: Schema.Attribute.String;
        isEntryValid: Schema.Attribute.Boolean;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "plugin::content-releases.release-action"
        > &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        release: Schema.Attribute.Relation<"manyToOne", "plugin::content-releases.release">;
        type: Schema.Attribute.Enumeration<["publish", "unpublish"]> & Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
    collectionName: "i18n_locale";
    info: {
        collectionName: "locales";
        description: "";
        displayName: "Locale";
        pluralName: "locales";
        singularName: "locale";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        code: Schema.Attribute.String & Schema.Attribute.Unique;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::i18n.locale"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.SetMinMax<
                {
                    max: 50;
                    min: 1;
                },
                number
            >;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginReviewWorkflowsWorkflow extends Struct.CollectionTypeSchema {
    collectionName: "strapi_workflows";
    info: {
        description: "";
        displayName: "Workflow";
        name: "Workflow";
        pluralName: "workflows";
        singularName: "workflow";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        contentTypes: Schema.Attribute.JSON &
            Schema.Attribute.Required &
            Schema.Attribute.DefaultTo<"[]">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::review-workflows.workflow"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
        publishedAt: Schema.Attribute.DateTime;
        stageRequiredToPublish: Schema.Attribute.Relation<
            "oneToOne",
            "plugin::review-workflows.workflow-stage"
        >;
        stages: Schema.Attribute.Relation<"oneToMany", "plugin::review-workflows.workflow-stage">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginReviewWorkflowsWorkflowStage extends Struct.CollectionTypeSchema {
    collectionName: "strapi_workflows_stages";
    info: {
        description: "";
        displayName: "Stages";
        name: "Workflow Stage";
        pluralName: "workflow-stages";
        singularName: "workflow-stage";
    };
    options: {
        draftAndPublish: false;
        version: "1.1.0";
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        color: Schema.Attribute.String & Schema.Attribute.DefaultTo<"#4945FF">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "plugin::review-workflows.workflow-stage"
        > &
            Schema.Attribute.Private;
        name: Schema.Attribute.String;
        permissions: Schema.Attribute.Relation<"manyToMany", "admin::permission">;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        workflow: Schema.Attribute.Relation<"manyToOne", "plugin::review-workflows.workflow">;
    };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
    collectionName: "files";
    info: {
        description: "";
        displayName: "File";
        pluralName: "files";
        singularName: "file";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        alternativeText: Schema.Attribute.String;
        caption: Schema.Attribute.String;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        ext: Schema.Attribute.String;
        folder: Schema.Attribute.Relation<"manyToOne", "plugin::upload.folder"> &
            Schema.Attribute.Private;
        folderPath: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Private &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        formats: Schema.Attribute.JSON;
        hash: Schema.Attribute.String & Schema.Attribute.Required;
        height: Schema.Attribute.Integer;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::upload.file"> &
            Schema.Attribute.Private;
        mime: Schema.Attribute.String & Schema.Attribute.Required;
        name: Schema.Attribute.String & Schema.Attribute.Required;
        previewUrl: Schema.Attribute.String;
        provider: Schema.Attribute.String & Schema.Attribute.Required;
        provider_metadata: Schema.Attribute.JSON;
        publishedAt: Schema.Attribute.DateTime;
        related: Schema.Attribute.Relation<"morphToMany">;
        size: Schema.Attribute.Decimal & Schema.Attribute.Required;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        url: Schema.Attribute.String & Schema.Attribute.Required;
        width: Schema.Attribute.Integer;
    };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
    collectionName: "upload_folders";
    info: {
        displayName: "Folder";
        pluralName: "folders";
        singularName: "folder";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        children: Schema.Attribute.Relation<"oneToMany", "plugin::upload.folder">;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        files: Schema.Attribute.Relation<"oneToMany", "plugin::upload.file">;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::upload.folder"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        parent: Schema.Attribute.Relation<"manyToOne", "plugin::upload.folder">;
        path: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 1;
            }>;
        pathId: Schema.Attribute.Integer & Schema.Attribute.Required & Schema.Attribute.Unique;
        publishedAt: Schema.Attribute.DateTime;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginUsersPermissionsPermission extends Struct.CollectionTypeSchema {
    collectionName: "up_permissions";
    info: {
        description: "";
        displayName: "Permission";
        name: "permission";
        pluralName: "permissions";
        singularName: "permission";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        action: Schema.Attribute.String & Schema.Attribute.Required;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<
            "oneToMany",
            "plugin::users-permissions.permission"
        > &
            Schema.Attribute.Private;
        publishedAt: Schema.Attribute.DateTime;
        role: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.role">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
    };
}

export interface PluginUsersPermissionsRole extends Struct.CollectionTypeSchema {
    collectionName: "up_roles";
    info: {
        description: "";
        displayName: "Role";
        name: "role";
        pluralName: "roles";
        singularName: "role";
    };
    options: {
        draftAndPublish: false;
    };
    pluginOptions: {
        "content-manager": {
            visible: false;
        };
        "content-type-builder": {
            visible: false;
        };
    };
    attributes: {
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        description: Schema.Attribute.String;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.role"> &
            Schema.Attribute.Private;
        name: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 3;
            }>;
        permissions: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.permission">;
        publishedAt: Schema.Attribute.DateTime;
        type: Schema.Attribute.String & Schema.Attribute.Unique;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        users: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.user">;
    };
}

export interface PluginUsersPermissionsUser extends Struct.CollectionTypeSchema {
    collectionName: "up_users";
    info: {
        description: "";
        displayName: "User";
        name: "user";
        pluralName: "users";
        singularName: "user";
    };
    options: {
        draftAndPublish: false;
        timestamps: true;
    };
    attributes: {
        blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
        confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
        createdAt: Schema.Attribute.DateTime;
        createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        email: Schema.Attribute.Email &
            Schema.Attribute.Required &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        locale: Schema.Attribute.String & Schema.Attribute.Private;
        localizations: Schema.Attribute.Relation<"oneToMany", "plugin::users-permissions.user"> &
            Schema.Attribute.Private;
        password: Schema.Attribute.Password &
            Schema.Attribute.Private &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 6;
            }>;
        provider: Schema.Attribute.String;
        publishedAt: Schema.Attribute.DateTime;
        resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
        role: Schema.Attribute.Relation<"manyToOne", "plugin::users-permissions.role">;
        updatedAt: Schema.Attribute.DateTime;
        updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> & Schema.Attribute.Private;
        username: Schema.Attribute.String &
            Schema.Attribute.Required &
            Schema.Attribute.Unique &
            Schema.Attribute.SetMinMaxLength<{
                minLength: 3;
            }>;
    };
}

declare module "@strapi/strapi" {
    export module Public {
        export interface ContentTypeSchemas {
            "admin::api-token": AdminApiToken;
            "admin::api-token-permission": AdminApiTokenPermission;
            "admin::permission": AdminPermission;
            "admin::role": AdminRole;
            "admin::transfer-token": AdminTransferToken;
            "admin::transfer-token-permission": AdminTransferTokenPermission;
            "admin::user": AdminUser;
            "api::action.action": ApiActionAction;
            "api::entite.entite": ApiEntiteEntite;
            "api::expression.expression": ApiExpressionExpression;
            "api::lieu.lieu": ApiLieuLieu;
            "api::notification.notification": ApiNotificationNotification;
            "api::perimetre.perimetre": ApiPerimetrePerimetre;
            "api::pilier.pilier": ApiPilierPilier;
            "api::profile.profile": ApiProfileProfile;
            "api::sous-pilier.sous-pilier": ApiSousPilierSousPilier;
            "api::validateur.validateur": ApiValidateurValidateur;
            "plugin::content-releases.release": PluginContentReleasesRelease;
            "plugin::content-releases.release-action": PluginContentReleasesReleaseAction;
            "plugin::i18n.locale": PluginI18NLocale;
            "plugin::review-workflows.workflow": PluginReviewWorkflowsWorkflow;
            "plugin::review-workflows.workflow-stage": PluginReviewWorkflowsWorkflowStage;
            "plugin::upload.file": PluginUploadFile;
            "plugin::upload.folder": PluginUploadFolder;
            "plugin::users-permissions.permission": PluginUsersPermissionsPermission;
            "plugin::users-permissions.role": PluginUsersPermissionsRole;
            "plugin::users-permissions.user": PluginUsersPermissionsUser;
        }
    }
}
