# PillarScan Permissions System

## Overview

The PillarScan permissions system is automatically configured when the Strapi backend starts. It sets up role-based access control (RBAC) for all API endpoints according to the project specifications.

## Automatic Setup

Permissions are automatically configured during Strapi bootstrap in `/src/index.ts`. The setup includes:

1. **Creating custom roles** (if they don't exist)
2. **Configuring API permissions** for each role
3. **Setting up plugin permissions** (users-permissions, upload)

## Role Hierarchy

### Default Strapi Roles

- **Public**: Unauthenticated users
- **Authenticated**: Any logged-in user

### Custom PillarScan Roles

1. **Contributeur Vérifié** (`contributeur_verifie`)

    - Verified contributors with extended privileges
    - Can create entities and access more features

2. **Validateur** (`validateur`)

    - Can validate and moderate expressions
    - Access to moderation queue and validation tools

3. **Validateur Senior** (`validateur_senior`)

    - Experienced validators with training privileges
    - Can perform bulk operations

4. **Admin Régional** (`admin_regional`)

    - Regional administrators
    - Manage users and content in their region

5. **Admin National** (`admin_national`)

    - National administrators
    - Full access to national-level operations

6. **Super Admin** (`super_admin`)
    - Complete system control
    - All permissions enabled

## Permission Inheritance Principle

**Important**: The permission system follows a hierarchical inheritance model:

- **Authenticated users** automatically inherit ALL permissions that **public users** have
- This ensures logged-in users can always do everything that anonymous users can do
- Additional permissions are then granted exclusively to authenticated users

## API Permissions Matrix

### Expression API

| Endpoint                            | Public | Authenticated        | Description             |
| ----------------------------------- | ------ | -------------------- | ----------------------- |
| `GET /expressions`                  | ✅     | ✅ (inherited + own) | List expressions        |
| `GET /expressions/:id`              | ✅     | ✅ (inherited)       | Get single expression   |
| `GET /expressions/public`           | ✅     | ✅ (inherited)       | Public expressions list |
| `GET /expressions/stats`            | ✅     | ✅ (inherited)       | Global statistics       |
| `POST /expressions`                 | ❌     | ✅                   | Create expression       |
| `PUT /expressions/:id`              | ❌     | ✅                   | Update expression       |
| `DELETE /expressions/:id`           | ❌     | ✅                   | Delete expression       |
| `POST /expressions/:id/submit`      | ❌     | ✅                   | Submit for validation   |
| `GET /expressions/moderation/queue` | ❌     | ✅                   | Moderation queue        |
| `POST /expressions/:id/validate`    | ❌     | ✅                   | Validate expression     |
| `GET /expressions/analytics`        | ❌     | ✅                   | Analytics data          |
| `GET /expressions/user-stats`       | ❌     | ✅                   | User statistics         |

### Profile API

| Endpoint                    | Public | Authenticated  | Description          |
| --------------------------- | ------ | -------------- | -------------------- |
| `GET /profiles`             | ✅     | ✅ (inherited) | List profiles        |
| `GET /profiles/:id`         | ✅     | ✅ (inherited) | Get profile          |
| `GET /profiles/leaderboard` | ✅     | ✅ (inherited) | Top contributors     |
| `POST /profiles`            | ❌     | ✅             | Create profile       |
| `PUT /profiles/:id`         | ❌     | ✅             | Update profile       |
| `DELETE /profiles/:id`      | ❌     | ✅             | Delete profile       |
| `GET /profiles/me`          | ❌     | ✅             | Current user profile |
| `PUT /profiles/me`          | ❌     | ✅             | Update own profile   |
| `GET /profiles/stats`       | ❌     | ✅             | Profile statistics   |
| `PUT /profiles/:id/role`    | ❌     | ✅             | Update user role     |

### Other APIs

All other APIs (Pilier, Sous-Pilier, Lieu, Entite, Action) follow the same inheritance pattern:

- **Read operations** (find, findOne): Public access ✅, Authenticated inherits ✅
- **Write operations** (create, update, delete): Public denied ❌, Authenticated allowed ✅

## Manual Permission Management

### Check Current Permissions

```bash
# Run Strapi console
npm run strapi console

# Check permissions for a specific role
const { checkPermissions } = require('./src/utils/setup-permissions');
await checkPermissions(strapi, 'authenticated');
await checkPermissions(strapi, 'public', 'expression');
```

### Manually Run Setup

```bash
# Run Strapi console
npm run strapi console

# Execute setup
const runSetup = require('./src/scripts/setup-permissions').default;
await runSetup();
```

### Reset Permissions

If you need to reset permissions to default:

```bash
# Run Strapi console
npm run strapi console

# Reset and reconfigure
const { setupPermissions } = require('./src/utils/setup-permissions');
await setupPermissions(strapi);
```

## Troubleshooting

### Common Issues

1. **403 Forbidden Errors**

    - Check if the user is authenticated
    - Verify the user's role has the required permissions
    - Run the permissions setup script

2. **Permissions Not Applied**

    - Restart Strapi to trigger bootstrap
    - Manually run the setup script
    - Check console logs for errors

3. **Custom Roles Missing**
    - Run `createCustomRoles` function
    - Verify database connection
    - Check for role name conflicts

### Debug Mode

To see detailed permission logs:

```javascript
// In Strapi console
const { checkPermissions } = require("./src/utils/setup-permissions");

// Check all permissions for a role
await checkPermissions(strapi, "authenticated");

// Check specific controller permissions
await checkPermissions(strapi, "public", "expression");
```

## Advanced Configuration

### Adding New Permissions

To add permissions for a new API:

1. Edit `/src/utils/setup-permissions.ts`
2. Add your API to the `API_PERMISSIONS` object
3. Restart Strapi or run the setup manually

Example:

```typescript
const API_PERMISSIONS = {
    // ... existing permissions

    "my-new-api": {
        "my-new-api": {
            find: { action: "find", roles: ["public"] },
            create: { action: "create", roles: ["authenticated"] },
            // ... other actions
        },
    },
};
```

### Custom Role Permissions

For fine-grained control per custom role, you'll need to:

1. Modify the `updatePermission` function to handle custom roles
2. Add role-specific logic in your controllers
3. Use Strapi's policy system for complex authorization

## Security Best Practices

1. **Principle of Least Privilege**: Users should only have the minimum permissions necessary
2. **Regular Audits**: Periodically review permissions using the check scripts
3. **Role Assignment**: Be careful when assigning elevated roles
4. **API Security**: Always validate user permissions in custom controller methods

## API Response Codes

- **200**: Success
- **401**: Unauthorized (not authenticated)
- **403**: Forbidden (authenticated but lacks permission)
- **404**: Resource not found

## Integration with Frontend

The frontend should:

1. Handle 401/403 responses gracefully
2. Hide UI elements based on user permissions
3. Check user role from the `/users/me` endpoint
4. Not rely solely on frontend checks for security

## Maintenance

### Regular Tasks

1. **Weekly**: Review permission logs for anomalies
2. **Monthly**: Audit role assignments
3. **Quarterly**: Review and update permission matrix
4. **Yearly**: Full security audit

### Monitoring

Monitor these metrics:

- Failed authentication attempts
- 403 error rates by endpoint
- Permission changes
- Role assignment frequency

## Support

For permission-related issues:

1. Check this documentation
2. Review console logs during Strapi startup
3. Use the debug scripts to investigate
4. Contact the development team if issues persist
