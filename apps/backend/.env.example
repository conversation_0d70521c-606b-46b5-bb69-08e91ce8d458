# PillarScan Backend Environment Configuration

# Server Configuration
HOST=0.0.0.0
PORT=1337
APP_KEYS="toBeModified1,toBeModified2,toBeModified3,toBeModified4"
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=tobemodified
JWT_SECRET=tobemodified
ENCRYPTION_KEY=tobemodified

# Database Configuration (PostgreSQL)
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=pillarscan
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_SSL=false
DATABASE_SCHEMA=public
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_CONNECTION_TIMEOUT=60000

# Redis Configuration (for caching and sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Email Configuration (Nodemailer)
EMAIL_PROVIDER=nodemailer
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# File Upload Configuration
UPLOAD_PROVIDER=local
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_FORMATS=image/jpeg,image/png,image/webp,video/mp4,application/pdf

# AI/ML Configuration
AI_CLASSIFICATION_ENABLED=true
AI_API_URL=http://localhost:8000
AI_API_KEY=your-ai-api-key

# Security Configuration
CORS_ENABLED=true
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# Monitoring and Logging
LOG_LEVEL=info
SENTRY_DSN=
DATADOG_API_KEY=

# Feature Flags
FEATURE_REAL_TIME=true
FEATURE_NOTIFICATIONS=true
FEATURE_ANALYTICS=true
FEATURE_GEOLOCATION=true

# External Integrations
MAPBOX_ACCESS_TOKEN=your-mapbox-token
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+***********

# Development/Production Environment
NODE_ENV=development
