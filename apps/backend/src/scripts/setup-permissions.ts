/**
 * Manual script to setup permissions
 * Run with: npm run strapi console
 * Then execute: require('./src/scripts/setup-permissions').default()
 */

import { setupPermissions, createCustomRoles, checkPermissions } from "../utils/setup-permissions";

async function runSetup() {
    console.log("🔧 Running manual permissions setup...\n");

    try {
        // Create custom roles
        await createCustomRoles(strapi);

        // Setup permissions
        await setupPermissions(strapi);

        console.log("\n✅ Manual setup completed!");

        // Check permissions for verification
        console.log("\n📋 Checking permissions for each role:");
        const rolesToCheck = ["public", "authenticated", "validateur", "admin_regional"];

        for (const role of rolesToCheck) {
            await checkPermissions(strapi, role);
        }
    } catch (error) {
        console.error("❌ Setup failed:", error);
    }
}

// Export for use in Strapi console
export default runSetup;

// Also export individual functions for debugging
export { setupPermissions, createCustomRoles, checkPermissions };
