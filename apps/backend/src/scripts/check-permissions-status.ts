/**
 * <PERSON><PERSON>t to check current permission status
 * Useful for debugging permission issues
 */

export async function checkPermissionsStatus(strapi: any) {
    console.log("🔍 Checking PillarScan Permissions Status...\n");

    try {
        // Get all roles
        const roles = await strapi.query("plugin::users-permissions.role").findMany({
            populate: ["permissions"],
        });

        // Separate default and custom roles
        const defaultRoles = roles.filter((r: any) => ["public", "authenticated"].includes(r.type));
        const customRoles = roles.filter((r: any) => !["public", "authenticated"].includes(r.type));

        console.log("📋 Roles Summary:");
        console.log(`- Default roles: ${defaultRoles.map((r: any) => r.type).join(", ")}`);
        console.log(`- Custom roles: ${customRoles.map((r: any) => r.type).join(", ") || "None"}`);
        console.log("");

        // Check critical permissions
        const criticalChecks = [
            { api: "expression", action: "find", shouldBePublic: true },
            { api: "expression", action: "findPublic", shouldBePublic: true },
            { api: "expression", action: "getGlobalStats", shouldBePublic: true },
            { api: "expression", action: "create", shouldBePublic: false },
            { api: "profile", action: "leaderboard", shouldBePublic: true },
            { api: "profile", action: "me", shouldBePublic: false },
            { api: "pilier", action: "find", shouldBePublic: true },
            { api: "lieu", action: "find", shouldBePublic: true },
        ];

        console.log("🔒 Critical Permissions Check:");
        console.log("=".repeat(60));

        for (const check of criticalChecks) {
            const publicRole = roles.find((r: any) => r.type === "public");
            const authRole = roles.find((r: any) => r.type === "authenticated");

            const publicHasPermission = publicRole.permissions.some(
                (p: any) =>
                    p.action === `api::${check.api}.${check.api}.${check.action}` && p.enabled,
            );

            const authHasPermission = authRole.permissions.some(
                (p: any) =>
                    p.action === `api::${check.api}.${check.api}.${check.action}` && p.enabled,
            );

            const status = check.shouldBePublic
                ? publicHasPermission && authHasPermission
                    ? "✅"
                    : "❌"
                : !publicHasPermission && authHasPermission
                  ? "✅"
                  : "❌";

            console.log(
                `${status} ${check.api}.${check.action}: ` +
                    `Public=${publicHasPermission ? "Yes" : "No"}, ` +
                    `Auth=${authHasPermission ? "Yes" : "No"} ` +
                    `(Expected: Public=${check.shouldBePublic ? "Yes" : "No"}, Auth=Yes)`,
            );
        }

        console.log("");

        // Check for permission inheritance issues
        console.log("🔄 Permission Inheritance Check:");
        const publicPerms = roles
            .find((r: any) => r.type === "public")
            .permissions.filter((p: any) => p.enabled && p.action.startsWith("api::"))
            .map((p: any) => p.action);

        const authPerms = roles
            .find((r: any) => r.type === "authenticated")
            .permissions.filter((p: any) => p.enabled && p.action.startsWith("api::"))
            .map((p: any) => p.action);

        const missingInAuth = publicPerms.filter((p: string) => !authPerms.includes(p));

        if (missingInAuth.length > 0) {
            console.log("❌ Public permissions missing in authenticated role:");
            missingInAuth.forEach((p: string) => console.log(`  - ${p}`));
        } else {
            console.log("✅ All public permissions are included in authenticated role");
        }

        console.log("");

        // Summary
        console.log("📊 Summary:");
        console.log(`- Total roles: ${roles.length}`);
        console.log(`- Public permissions: ${publicPerms.length}`);
        console.log(`- Authenticated permissions: ${authPerms.length}`);

        const issues =
            criticalChecks.filter((check) => {
                const publicRole = roles.find((r: any) => r.type === "public");
                const authRole = roles.find((r: any) => r.type === "authenticated");

                const publicHas = publicRole.permissions.some(
                    (p: any) =>
                        p.action === `api::${check.api}.${check.api}.${check.action}` && p.enabled,
                );

                const authHas = authRole.permissions.some(
                    (p: any) =>
                        p.action === `api::${check.api}.${check.api}.${check.action}` && p.enabled,
                );

                if (check.shouldBePublic) {
                    return !publicHas || !authHas;
                } else {
                    return publicHas || !authHas;
                }
            }).length + missingInAuth.length;

        if (issues > 0) {
            console.log(`\n⚠️  ${issues} permission issues detected!`);
            console.log("Run the setup script to fix:");
            console.log("  const { setupPermissions } = require('./src/utils/setup-permissions');");
            console.log("  await setupPermissions(strapi);");
        } else {
            console.log("\n✅ All permissions are correctly configured!");
        }
    } catch (error) {
        console.error("❌ Error checking permissions:", error);
    }
}

// Export for use in Strapi console
export default checkPermissionsStatus;
