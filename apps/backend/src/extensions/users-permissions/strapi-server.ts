export default (plugin: any) => {
    // Add lifecycle hooks for user model
    const userContentType = plugin.contentTypes.user;

    if (!userContentType.lifecycles) {
        userContentType.lifecycles = {};
    }

    // Hook to create profile after user registration
    userContentType.lifecycles.afterCreate = async (event: any) => {
        const { result } = event;

        try {
            // Use the profile service to create user profile
            const profileService = strapi.service("api::profile.profile");
            await profileService.createUserProfile({
                id: result.id,
                username: result.username,
                email: result.email,
                confirmed: result.confirmed,
            });
        } catch (error) {
            console.error(`Error creating profile for user ${result.id}:`, error);
            // Don't throw error to prevent user registration from failing
        }
    };

    // Override the me controller to manually populate profile
    plugin.controllers.user.me = async (ctx: any) => {
        const user = ctx.state.user;

        if (!user) {
            return ctx.unauthorized();
        }

        const { populate } = ctx.query;

        try {
            // First, get the basic user data without any custom populate
            let userData: any = await strapi.documents("plugin::users-permissions.user").findOne({
                documentId: user.documentId,
                populate: {
                    role: true,
                },
            });

            // If profile is requested, fetch it separately
            if (
                populate &&
                (populate === "profile" || populate.includes("profile") || populate === "*")
            ) {
                // Find the profile linked to this user
                const profiles = await strapi.documents("api::profile.profile").findMany({
                    filters: {
                        user: {
                            id: user.id,
                        },
                    },
                    populate: {
                        lieu_residence: true,
                        validateur_assigne: true,
                        expressions: true,
                        user: false, // Don't populate user to avoid circular reference
                    },
                    pagination: { pageSize: 1 },
                });

                if (profiles && profiles.length > 0) {
                    // Manually add the profile to the user data
                    userData.profile = profiles[0];
                } else {
                    // If no profile exists, create one using the profile service
                    try {
                        const profileService = strapi.service("api::profile.profile");
                        const newProfile = await profileService.createUserProfile({
                            id: user.id,
                            username: userData.username,
                            email: userData.email,
                            confirmed: userData.confirmed,
                        });
                        userData.profile = newProfile;
                    } catch (error) {
                        console.error(`Error creating profile for user ${user.id}:`, error);
                        userData.profile = null;
                    }
                }
            }

            // Return the user data directly
            // In Strapi 5, sanitization is handled differently
            ctx.body = userData;
        } catch (error) {
            console.error("[Extension] Error in me controller:", error);
            return ctx.badRequest("Error fetching user data");
        }
    };

    // Override the register controller to ensure profile creation
    const originalRegister = plugin.controllers.auth.register;

    plugin.controllers.auth.register = async (ctx: any) => {
        // Call the original register method
        await originalRegister(ctx);

        // If registration was successful and we have a user in the response
        if (ctx.response.status === 200 && ctx.response.body?.user) {
            const user = ctx.response.body.user;

            try {
                // Check if profile was already created by lifecycle
                const profile = await strapi.documents("api::profile.profile").findFirst({
                    filters: { user: user.id },
                    populate: {
                        lieu_residence: true,
                        validateur_assigne: true,
                    },
                });

                // Add profile to response if it exists
                if (profile) {
                    ctx.response.body.user.profile = profile;
                }
            } catch (error) {
                console.error("Error fetching profile after registration:", error);
            }
        }
    };

    return plugin;
};
