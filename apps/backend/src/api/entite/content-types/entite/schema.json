{"kind": "collectionType", "collectionName": "entites", "info": {"singularName": "entite", "pluralName": "entites", "displayName": "Entité", "description": "Organizations, people, and groups mentioned in expressions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"nom": {"type": "string", "required": true, "maxLength": 200}, "type": {"type": "enumeration", "enum": ["personne", "organisation", "groupe", "service"], "required": true}, "sous_type": {"type": "string", "maxLength": 100}, "description": {"type": "text"}, "identifiants": {"type": "json", "default": {}}, "contacts": {"type": "json", "default": {}}, "lieu_principal": {"type": "relation", "relation": "manyToOne", "target": "api::lieu.lieu", "inversedBy": "entites"}, "responsable": {"type": "relation", "relation": "manyToOne", "target": "api::profile.profile"}, "expressions": {"type": "relation", "relation": "manyToMany", "target": "api::expression.expression", "mappedBy": "entites"}, "verifie": {"type": "boolean", "default": false}, "actif": {"type": "boolean", "default": true, "required": true}, "reputation_score": {"type": "decimal", "default": 0.0}, "nb_mentions": {"type": "integer", "default": 0}, "derniere_mention": {"type": "datetime"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "site_web": {"type": "string", "maxLength": 255}, "reseaux_sociaux": {"type": "json", "default": {}}, "metadata": {"type": "json", "default": {}}}}