/**
 * Pilier router
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreRouter("api::pilier.pilier", {
    config: {
        create: {
            policies: [],
            middlewares: [],
        },
        update: {
            policies: [],
            middlewares: [],
        },
        delete: {
            policies: [],
            middlewares: [],
        },
    },
});
