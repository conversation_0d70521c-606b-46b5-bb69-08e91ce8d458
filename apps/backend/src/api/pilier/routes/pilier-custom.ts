/**
 * Pilier router
 */

export default {
    routes: [
        // Standard CRUD routes
        {
            method: "GET",
            path: "/piliers",
            handler: "pilier.find",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/piliers/:id",
            handler: "pilier.findOne",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },

        // Custom routes for analytics
        {
            method: "GET",
            path: "/piliers/:id/stats",
            handler: "pilier.getStats",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/piliers/:id/geo-distribution",
            handler: "pilier.getGeoDistribution",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/piliers/:id/trending-topics",
            handler: "pilier.getTrendingTopics",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
    ],
};
