/**
 * Custom notification routes
 */

export default {
    routes: [
        {
            method: "POST",
            path: "/notifications/create",
            handler: "notification.create",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/notifications/:id/read",
            handler: "notification.markAsRead",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/notifications/mark-all-read",
            handler: "notification.markAllAsRead",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/notifications/stats",
            handler: "notification.getStats",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "DELETE",
            path: "/notifications/clear-all",
            handler: "notification.clearAll",
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
