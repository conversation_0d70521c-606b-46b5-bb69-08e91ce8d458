/**
 * Notification controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController("api::notification.notification", ({ strapi }) => ({
    /**
     * Get notifications for current user with filters
     */
    async find(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile associated with the current user
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return {
                    data: [],
                    meta: { pagination: { page: 1, pageSize: 25, pageCount: 0, total: 0 } },
                };
            }

            // Extract query parameters
            const {
                page = 1,
                pageSize = 25,
                type,
                category,
                read,
                timeRange,
                startDate,
                endDate,
            } = ctx.query;

            // Build where clause
            let where: any = {
                recipient: { documentId: profile.documentId },
            };

            // Add filters
            if (type) {
                where.type = type;
            }

            if (category) {
                where.category = category;
            }

            if (read !== undefined) {
                where.read = read === "true";
            }

            // Handle time range filter
            if (timeRange || (startDate && endDate)) {
                where.createdAt = {};

                if (timeRange) {
                    const now = new Date();
                    const start = new Date();

                    switch (timeRange) {
                        case "today":
                            start.setHours(0, 0, 0, 0);
                            break;
                        case "week":
                            start.setDate(now.getDate() - 7);
                            break;
                        case "month":
                            start.setMonth(now.getMonth() - 1);
                            break;
                    }

                    where.createdAt.$gte = start;
                    where.createdAt.$lte = now;
                } else {
                    if (startDate) where.createdAt.$gte = new Date(startDate as string);
                    if (endDate) where.createdAt.$lte = new Date(endDate as string);
                }
            }

            // Get total count
            const total = await strapi.documents("api::notification.notification").count({
                filters: where,
            });

            // Fetch notifications with pagination
            const notifications = await strapi
                .documents("api::notification.notification")
                .findMany({
                    filters: where,
                    populate: ["expression"],
                    sort: { createdAt: "desc" },
                    pagination: {
                        page: Number(page),
                        pageSize: Number(pageSize),
                    },
                });

            return {
                data: notifications,
                meta: {
                    pagination: {
                        page: Number(page),
                        pageSize: Number(pageSize),
                        pageCount: Math.ceil(total / Number(pageSize)),
                        total,
                    },
                },
            };
        } catch (error) {
            console.error("Error in notification find method:", error);
            return ctx.badRequest("Failed to fetch notifications");
        }
    },

    /**
     * Mark notification as read
     */
    async markAsRead(ctx) {
        try {
            const { id } = ctx.params;

            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Check if notification belongs to user
            const notification = await strapi.documents("api::notification.notification").findOne({
                documentId: id,
                populate: ["recipient"],
            });

            if (!notification || notification.recipient?.documentId !== profile.documentId) {
                return ctx.notFound("Notification not found");
            }

            // Update notification
            const updated = await strapi.documents("api::notification.notification").update({
                documentId: id,
                data: { read: true },
            });

            return { data: updated };
        } catch (error) {
            console.error("Error marking notification as read:", error);
            return ctx.badRequest("Failed to update notification");
        }
    },

    /**
     * Mark all notifications as read
     */
    async markAllAsRead(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Get all unread notifications for this user
            const unreadNotifications = await strapi
                .documents("api::notification.notification")
                .findMany({
                    filters: {
                        recipient: { documentId: profile.documentId },
                        read: false,
                    },
                    fields: ["id"],
                    pagination: { pageSize: 1000 }, // Large page size to handle bulk updates
                });

            // Update each notification individually (Strapi doesn't support bulk updates directly)
            const updatePromises = unreadNotifications.map((notification: any) =>
                strapi.documents("api::notification.notification").update({
                    documentId: notification.documentId || notification.id,
                    data: { read: true },
                }),
            );

            await Promise.all(updatePromises);

            return { data: { message: "All notifications marked as read" } };
        } catch (error) {
            console.error("Error marking all notifications as read:", error);
            return ctx.badRequest("Failed to update notifications");
        }
    },

    /**
     * Get notification statistics
     */
    async getStats(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return {
                    data: {
                        total: 0,
                        unread: 0,
                        byCategory: {},
                        byType: {},
                    },
                };
            }

            // Get total and unread count
            const [total, unread] = await Promise.all([
                strapi.documents("api::notification.notification").count({
                    filters: { recipient: { documentId: profile.documentId } },
                }),
                strapi.documents("api::notification.notification").count({
                    filters: { recipient: { documentId: profile.documentId }, read: false },
                }),
            ]);

            // Get all notifications for this user to calculate statistics
            const allNotifications = await strapi
                .documents("api::notification.notification")
                .findMany({
                    filters: { recipient: { documentId: profile.documentId } },
                    fields: ["category", "type"],
                    pagination: { pageSize: 10000 }, // Large page size for statistics
                });

            // Calculate counts by category
            const byCategoryMap = allNotifications.reduce((acc: any, notification: any) => {
                const category = notification.category;
                acc[category] = (acc[category] || 0) + 1;
                return acc;
            }, {});

            // Calculate counts by type
            const byTypeMap = allNotifications.reduce((acc: any, notification: any) => {
                const type = notification.type;
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {});

            return {
                data: {
                    total,
                    unread,
                    byCategory: byCategoryMap,
                    byType: byTypeMap,
                },
            };
        } catch (error) {
            console.error("Error getting notification stats:", error);
            return ctx.badRequest("Failed to get notification statistics");
        }
    },

    /**
     * Delete a notification
     */
    async delete(ctx) {
        try {
            const { id } = ctx.params;

            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Check if notification belongs to user
            const notification = await strapi.documents("api::notification.notification").findOne({
                documentId: id,
                populate: ["recipient"],
            });

            if (!notification || notification.recipient?.documentId !== profile.documentId) {
                return ctx.notFound("Notification not found");
            }

            // Delete notification
            await strapi.documents("api::notification.notification").delete({
                documentId: id,
            });

            return { data: { message: "Notification deleted successfully" } };
        } catch (error) {
            console.error("Error deleting notification:", error);
            return ctx.badRequest("Failed to delete notification");
        }
    },

    /**
     * Clear all notifications for current user
     */
    async clearAll(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.forbidden("Profile not found");
            }

            // Get all notifications for this user
            const allNotifications = await strapi
                .documents("api::notification.notification")
                .findMany({
                    filters: {
                        recipient: { documentId: profile.documentId },
                    },
                    fields: ["id"],
                    pagination: { pageSize: 10000 }, // Large page size to handle bulk delete
                });

            // Delete each notification individually
            const deletePromises = allNotifications.map((notification: any) =>
                strapi.documents("api::notification.notification").delete({
                    documentId: notification.documentId || notification.id,
                }),
            );

            await Promise.all(deletePromises);

            return {
                data: {
                    message: "All notifications cleared successfully",
                    count: allNotifications.length,
                },
            };
        } catch (error) {
            console.error("Error clearing all notifications:", error);
            return ctx.badRequest("Failed to clear notifications");
        }
    },

    /**
     * Create a notification (for internal use)
     */
    async create(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            const {
                recipientId,
                title,
                message,
                type,
                category,
                expressionId,
                actionUrl,
                actionLabel,
                metadata,
            } = ctx.request.body.data;

            // Create notification using the service
            const notification = await strapi
                .service("api::notification.notification")
                .createNotification({
                    title,
                    message,
                    type,
                    category,
                    recipientId,
                    expressionId,
                    actionUrl,
                    actionLabel,
                    metadata,
                });

            return { data: notification };
        } catch (error) {
            console.error("Error creating notification:", error);
            return ctx.badRequest("Failed to create notification");
        }
    },
}));
