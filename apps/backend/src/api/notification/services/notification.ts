/**
 * Notification service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService("api::notification.notification", ({ strapi }) => ({
    /**
     * Create a notification for a user
     */
    async createNotification(data: {
        title: string;
        message: string;
        type?: "info" | "success" | "warning" | "error";
        category?: "expression" | "moderation" | "system" | "social" | "update";
        recipientId: string;
        expressionId?: string;
        actionUrl?: string;
        actionLabel?: string;
        metadata?: any;
    }) {
        try {
            const notification = await strapi.documents("api::notification.notification").create({
                data: {
                    title: data.title,
                    message: data.message,
                    type: data.type || "info",
                    category: data.category || "system",
                    read: false,
                    recipient: data.recipientId, // This should be a documentId
                    expression: data.expressionId, // This should be a documentId
                    action_url: data.actionUrl,
                    action_label: data.actionLabel,
                    metadata: data.metadata,
                },
            });

            // TODO: Send real-time notification via Socket.io
            // strapi.io.to(`user_${data.recipientId}`).emit('notification', notification);

            return notification;
        } catch (error) {
            console.error("Error creating notification:", error);
            throw error;
        }
    },

    /**
     * Create notifications for multiple users
     */
    async createBulkNotifications(
        recipientIds: string[],
        notificationData: {
            title: string;
            message: string;
            type?: "info" | "success" | "warning" | "error";
            category?: "expression" | "moderation" | "system" | "social" | "update";
            expressionId?: string;
            actionUrl?: string;
            actionLabel?: string;
            metadata?: any;
        },
    ) {
        const notifications = [];

        for (const recipientId of recipientIds) {
            try {
                const notification = await this.createNotification({
                    ...notificationData,
                    recipientId,
                });
                notifications.push(notification);
            } catch (error) {
                console.error(`Error creating notification for recipient ${recipientId}:`, error);
            }
        }

        return notifications;
    },

    /**
     * Get unread count for a user
     */
    async getUnreadCount(profileId: string) {
        try {
            return await strapi.documents("api::notification.notification").count({
                filters: {
                    recipient: { id: profileId },
                    read: false,
                },
            });
        } catch (error) {
            console.error("Error getting unread count:", error);
            return 0;
        }
    },
}));
