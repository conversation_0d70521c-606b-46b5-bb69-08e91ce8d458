/**
 * Profile service
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";

interface UserData {
    id: number | string;
    username?: string;
    email?: string;
    confirmed?: boolean;
}

interface ProfilePreferences {
    notifications_email: boolean;
    notifications_push: boolean;
    langue: string;
    theme: string;
}

interface ProfileData {
    user: number | string;
    nom: string;
    role: string;
    statut: string;
    date_inscription: Date;
    compte_verifie: boolean;
    preferences: ProfilePreferences;
    nb_expressions: number;
    score_reputation: number;
    metadata: Record<string, any>;
}

export default factories.createCoreService(
    "api::profile.profile",
    ({ strapi }: { strapi: Core.Strapi }) => ({
        /**
         * Create a user profile for a given user
         * @param user - The user object containing id, username, email, and confirmed status
         * @returns The created profile or null on error
         */
        async createUserProfile(user: UserData) {
            try {
                // Check if a profile already exists for this user
                const existingProfile = await strapi.documents("api::profile.profile").findFirst({
                    filters: { user: user.id as any },
                    populate: {
                        lieu_residence: true,
                        validateur_assigne: true,
                        expressions: true,
                    },
                });

                if (existingProfile) {
                    strapi.log.info(`Profile already exists for user ${user.id} (${user.email})`);
                    return existingProfile;
                }

                // Create profile data
                const profileData: ProfileData = {
                    user: user.id,
                    nom: user.username || `User ${user.id}`,
                    role: "contributeur", // Default role
                    statut: "actif",
                    date_inscription: new Date(),
                    compte_verifie: user.confirmed || false,
                    preferences: {
                        notifications_email: true,
                        notifications_push: false,
                        langue: "fr",
                        theme: "auto",
                    },
                    nb_expressions: 0,
                    score_reputation: 0,
                    metadata: {},
                };

                // Create a new profile for the user
                const newProfile = await strapi.documents("api::profile.profile").create({
                    data: profileData as any,
                    populate: {
                        lieu_residence: true,
                        validateur_assigne: true,
                        expressions: true,
                    },
                });

                strapi.log.info(`Profile created for user ${user.id} (${user.email})`);
                return newProfile;
            } catch (error) {
                strapi.log.error(`Error creating profile for user ${user.id}:`, error);
                // Return null instead of throwing to prevent cascading failures
                return null;
            }
        },
    }),
);
