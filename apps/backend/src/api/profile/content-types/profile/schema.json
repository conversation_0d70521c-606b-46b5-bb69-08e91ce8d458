{"kind": "collectionType", "collectionName": "profiles", "info": {"singularName": "profile", "pluralName": "profiles", "displayName": "Profile", "description": "Citizen profile with extended information for PillarScan"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"nom": {"type": "string", "required": true, "maxLength": 100, "description": "Nom complet du citoyen"}, "telephone": {"type": "string", "maxLength": 20, "description": "Numéro de téléphone"}, "date_naissance": {"type": "date", "description": "Date de naissance"}, "genre": {"type": "enumeration", "enum": ["M", "F", "<PERSON><PERSON>"], "default": "<PERSON><PERSON>", "description": "Genre (M: <PERSON><PERSON><PERSON><PERSON>, F: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)"}, "lieu_residence": {"type": "relation", "relation": "manyToOne", "target": "api::lieu.lieu", "inversedBy": "residents", "description": "<PERSON><PERSON> <PERSON> résidence principal"}, "validateur_assigne": {"type": "relation", "relation": "manyToOne", "target": "api::validateur.validateur", "inversedBy": "profiles_suivis", "description": "Validateur assigné pour le suivi"}, "role": {"type": "enumeration", "enum": ["observateur", "<PERSON>ur", "contributeur_verifie", "validateur", "validateur_senior", "admin_regional", "admin_national", "super_admin"], "default": "<PERSON>ur", "required": true, "description": "Rôle et niveau d'autorisation dans la plateforme"}, "statut": {"type": "enumeration", "enum": ["actif", "suspendu", "inactif"], "default": "actif", "required": true, "description": "Statut du compte utilisateur"}, "preferences": {"type": "json", "default": {"notifications_email": true, "notifications_push": true, "langue": "fr", "theme": "auto"}, "description": "Préférences utilisateur (notifications, thème, langue)"}, "date_inscription": {"type": "datetime", "description": "Date d'inscription sur la plateforme"}, "derniere_activite": {"type": "datetime", "description": "Date de dernière activité sur la plateforme"}, "nb_expressions": {"type": "integer", "default": 0, "description": "Nombre total d'expressions soumises"}, "score_reputation": {"type": "decimal", "default": 0.0, "description": "Score de réputation basé sur la qualité des contributions"}, "compte_verifie": {"type": "boolean", "default": false, "description": "Indique si le compte a été vérifié"}, "expressions": {"type": "relation", "relation": "oneToMany", "target": "api::expression.expression", "mappedBy": "auteur", "description": "Expressions créées par ce profil"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "description": "Compte utilisateur Strapi associé"}, "metadata": {"type": "json", "default": {}, "description": "Métadonnées supplémentaires du profil"}}}