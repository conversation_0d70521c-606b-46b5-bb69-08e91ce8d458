/**
 * Custom profile routes
 */

export default {
    routes: [
        {
            method: "GET",
            path: "/profiles/me",
            handler: "profile.me",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "PUT",
            path: "/profiles/me",
            handler: "profile.updateMe",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/profiles/:id/stats",
            handler: "profile.getStats",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/profiles/leaderboard",
            handler: "profile.leaderboard",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "PUT",
            path: "/profiles/:id/role",
            handler: "profile.updateRole",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/profiles/create-for-user",
            handler: "profile.createForUser",
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
