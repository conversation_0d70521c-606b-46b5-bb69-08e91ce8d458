{"kind": "collectionType", "collectionName": "validateurs", "info": {"singularName": "validateur", "pluralName": "validateurs", "displayName": "Validateur", "description": "Validators who moderate and approve citizen expressions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"profile": {"type": "relation", "relation": "oneToOne", "target": "api::profile.profile", "required": true, "description": "Profil utilisateur associé au validateur"}, "perimetres": {"type": "relation", "relation": "manyToMany", "target": "api::perimetre.perimetre", "inversedBy": "validateurs", "description": "Périmètres de validation assignés"}, "specialites": {"type": "relation", "relation": "manyToMany", "target": "api::pilier.pilier", "inversedBy": "validateurs_specialistes", "description": "Piliers de spécialisation du validateur"}, "niveau": {"type": "enumeration", "enum": ["junior", "senior", "expert", "formateur"], "default": "junior", "required": true, "description": "Niveau d'expérience du validateur"}, "quota_jour": {"type": "integer", "default": 50, "min": 1, "max": 200, "description": "Quota d'expressions à valider par jour"}, "quota_utilise_jour": {"type": "integer", "default": 0, "description": "Nombre d'expressions déjà validées aujourd'hui"}, "actif": {"type": "boolean", "default": true, "required": true, "description": "Indique si le validateur est actif"}, "date_nomination": {"type": "datetime", "description": "Date de nomination en tant que validateur"}, "date_derniere_activite": {"type": "datetime", "description": "Date de dernière activité de validation"}, "profiles_suivis": {"type": "relation", "relation": "oneToMany", "target": "api::profile.profile", "mappedBy": "validateur_assigne"}, "expressions_validees": {"type": "relation", "relation": "oneToMany", "target": "api::expression.expression", "mappedBy": "validateur"}, "statistiques": {"type": "json", "default": {"nb_validations_total": 0, "nb_validations_mois": 0, "taux_approbation": 0.0, "temps_moyen_validation": 0, "score_qualite": 0.0}}, "formations_completees": {"type": "json", "default": []}, "certifications": {"type": "json", "default": []}, "notes_performance": {"type": "text"}, "disponibilite": {"type": "json", "default": {"horaires": "9h-18h", "jours": ["lundi", "mardi", "merc<PERSON>i", "jeudi", "vend<PERSON>i"], "vacances": false}}, "metadata": {"type": "json", "default": {}}}}