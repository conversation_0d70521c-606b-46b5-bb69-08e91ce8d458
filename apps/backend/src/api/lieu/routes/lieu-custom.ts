/**
 * Custom lieu routes
 */

export default {
    routes: [
        {
            method: "GET",
            path: "/lieux/search",
            handler: "lieu.search",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/lieux/by-level",
            handler: "lieu.getByLevel",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/lieux/:id/hierarchy",
            handler: "lieu.getHierarchy",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/lieux/:id/expression-stats",
            handler: "lieu.getExpressionStats",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },

        // protected routes
        {
            method: "POST",
            path: "/lieux/create-from-coordinates",
            handler: "lieu.createFromCoordinates",
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
