/**
 * Lieu controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController("api::lieu.lieu", ({ strapi }) => ({
    /**
     * Search locations by name or coordinates
     */
    async search(ctx) {
        const { q, lat, lng, radius = 10, type, niveau } = ctx.query;

        let filters: any = { actif: true };

        // Text search
        if (q) {
            filters.$or = [
                { nom: { $containsi: q } },
                { adresse_complete: { $containsi: q } },
                { ville: { $containsi: q } },
            ];
        }

        // Filter by type and level
        if (type) {
            filters.type = type;
        }

        if (niveau) {
            filters.niveau = niveau;
        }

        let locations = await strapi.documents("api::lieu.lieu").findMany({
            filters,
            populate: ["parent"],
            pagination: { pageSize: 50 },
        });

        // If coordinates provided, filter by distance
        if (lat && lng) {
            const userLat = parseFloat(lat as string);
            const userLng = parseFloat(lng as string);
            const maxRadius = parseFloat(radius as string);

            locations = locations.filter((location) => {
                if (!location.coordonnees) {
                    return false;
                }

                const coords = location.coordonnees as any;
                if (!coords.lat || !coords.lng) {
                    return false;
                }

                const distance = calculateDistance(userLat, userLng, coords.lat, coords.lng);

                return distance <= maxRadius;
            });

            // Sort by distance
            locations.sort((a, b) => {
                const coordsA = a.coordonnees as any;
                const coordsB = b.coordonnees as any;
                const distA = calculateDistance(userLat, userLng, coordsA.lat, coordsA.lng);
                const distB = calculateDistance(userLat, userLng, coordsB.lat, coordsB.lng);
                return distA - distB;
            });
        }

        return { data: locations };
    },

    /**
     * Get location hierarchy (parent and children)
     */
    async getHierarchy(ctx) {
        const { id } = ctx.params;

        const location = await strapi.documents("api::lieu.lieu").findOne({
            documentId: id,
            populate: ["parent", "enfants"],
        });

        if (!location) {
            return ctx.notFound("Location not found");
        }

        // Get full hierarchy path
        const hierarchy = [];
        let current = location;

        // Go up the hierarchy
        while (current) {
            hierarchy.unshift({
                id: current.id,
                documentId: current.documentId,
                nom: current.nom,
                niveau: current.niveau,
                type: current.type,
            });

            if (current.parent) {
                current = await strapi.documents("api::lieu.lieu").findOne({
                    documentId: current.parent.documentId,
                    populate: ["parent"],
                });
            } else {
                current = null;
            }
        }

        return {
            data: {
                location,
                hierarchy,
                children: location.enfants || [],
            },
        };
    },

    /**
     * Get locations by administrative level
     */
    async getByLevel(ctx) {
        const { niveau, parent } = ctx.query;

        let filters: any = {
            actif: true,
            niveau: niveau,
        };

        if (parent) {
            filters.parent = parent;
        }

        const locations = await strapi.documents("api::lieu.lieu").findMany({
            filters,
            populate: ["parent"],
            sort: { nom: "asc" },
        });

        return { data: locations };
    },

    /**
     * Get expression statistics for a location
     */
    async getExpressionStats(ctx) {
        const { id } = ctx.params;
        const { period = "all" } = ctx.query;

        const location = await strapi.documents("api::lieu.lieu").findOne({
            documentId: id,
        });

        if (!location) {
            return ctx.notFound("Location not found");
        }

        // Build date filter
        let dateFilter = {};
        if (period === "month") {
            const oneMonthAgo = new Date();
            oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
            dateFilter = { date_creation: { $gte: oneMonthAgo } };
        } else if (period === "week") {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            dateFilter = { date_creation: { $gte: oneWeekAgo } };
        }

        // Get expressions for this location
        const expressions = await strapi.documents("api::expression.expression").findMany({
            filters: {
                lieu: { documentId: { $eq: id } },
                statut: { $in: ["publie", "resolu"] },
                ...dateFilter,
            },
            populate: ["piliers"],
            fields: ["statut", "type_expression", "urgence", "date_creation"],
        });

        // Calculate statistics
        const stats = {
            total_expressions: expressions.length,
            by_status: {},
            by_type: {},
            by_urgency: {},
            by_pillar: {},
            monthly_trend: {},
        };

        expressions.forEach((expr) => {
            stats.by_status[expr.statut] = (stats.by_status[expr.statut] || 0) + 1;
            stats.by_type[expr.type_expression] = (stats.by_type[expr.type_expression] || 0) + 1;
            stats.by_urgency[expr.urgence] = (stats.by_urgency[expr.urgence] || 0) + 1;

            // Monthly trend
            const month = new Date(expr.date_creation).toISOString().slice(0, 7);
            stats.monthly_trend[month] = (stats.monthly_trend[month] || 0) + 1;

            // By pillar
            if (expr.piliers && expr.piliers.length > 0) {
                expr.piliers.forEach((pillar) => {
                    stats.by_pillar[pillar.code] = (stats.by_pillar[pillar.code] || 0) + 1;
                });
            }
        });

        return { data: stats };
    },

    /**
     * Find the best existing location or create appropriate one based on geocoded data
     */
    async createFromCoordinates(ctx) {
        const { data } = ctx.request.body;

        if (!data || !data.coordonnees) {
            return ctx.badRequest("Missing required fields: coordonnees");
        }

        try {
            const pays = (data.pays || "FR").toUpperCase().slice(0, 2);

            // Try to find existing locations from most specific to least specific
            // Priority order: rue (if exists) > ville > departement > region > pays

            // 1. Try to find exact location with rue (street)
            if (data.rue && data.ville) {
                const rueLocation = await strapi.documents("api::lieu.lieu").findFirst({
                    filters: {
                        rue: data.rue,
                        ville: data.ville,
                        departement: data.departement,
                        region: data.region,
                        pays: pays,
                    },
                });

                if (rueLocation) {
                    return {
                        data: rueLocation,
                        meta: { existing: true, level: "rue" },
                    };
                }
            }

            // 2. Try to find exact ville (city) without rue
            if (data.ville) {
                const villeLocation = await strapi.documents("api::lieu.lieu").findFirst({
                    filters: {
                        nom: data.ville,
                        type: "zone",
                        departement: data.departement,
                        region: data.region,
                        pays: pays,
                    },
                });

                if (villeLocation) {
                    return {
                        data: villeLocation,
                        meta: { existing: true, level: "ville" },
                    };
                }
            }

            // // 3. Try to find departement
            // if (data.departement) {
            //     const deptLocation = await strapi.documents("api::lieu.lieu").findOne({
            //         where: {
            //             nom: data.departement,
            //             niveau: "departement",
            //             region: data.region,
            //             pays: pays,
            //         },
            //     });

            //     if (deptLocation) {
            //         return {
            //             data: deptLocation,
            //             meta: { existing: true, level: "departement" },
            //         };
            //     }
            // }

            // 4. Create new location if we have enough specific data
            // Create locations at ville (city) level or with street (rue) information
            if (data.ville) {
                const newLocation = await strapi.documents("api::lieu.lieu").create({
                    data: {
                        nom: data.rue ? `${data.rue}, ${data.ville}` : data.ville,
                        type: data.rue ? "adresse" : "zone",
                        niveau: data.rue ? "rue" : "ville",
                        coordonnees: data.coordonnees,
                        adresse_complete: data.adresse_complete,
                        code_postal: data.code_postal,
                        rue: data.rue,
                        ville: data.ville,
                        departement: data.departement,
                        region: data.region,
                        pays: pays,
                        actif: true,
                        verifie: false,
                        metadata: {
                            source: "user_geolocation",
                            created_from_coordinates: true,
                        },
                    },
                });

                return {
                    data: newLocation,
                    meta: { existing: false, level: data.rue ? "rue" : "ville" },
                };
            }

            // If we don't have enough specific data (at least city level), return null
            return {
                data: null,
                meta: {
                    existing: false,
                    message:
                        "Location data is not specific enough. Please provide at least a city name.",
                },
            };
        } catch (error) {
            console.error("Error creating location from coordinates:", error);
            return ctx.badRequest("Failed to create location");
        }
    },
}));

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
            Math.cos((lat2 * Math.PI) / 180) *
            Math.sin(dLng / 2) *
            Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
}
