{"kind": "collectionType", "collectionName": "actions", "info": {"singularName": "action", "pluralName": "actions", "displayName": "Action", "description": "Actions taken in response to citizen expressions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"titre": {"type": "string", "required": true, "maxLength": 200}, "description": {"type": "text", "required": true}, "type_action": {"type": "enumeration", "enum": ["investigation", "reparation", "amelioration", "communication", "formation", "politique", "autre"], "required": true}, "statut": {"type": "enumeration", "enum": ["planifiee", "en_cours", "terminee", "suspendue", "annulee"], "default": "planifiee", "required": true}, "priorite": {"type": "enumeration", "enum": ["basse", "normale", "haute", "critique"], "default": "normale", "required": true}, "expression": {"type": "relation", "relation": "manyToOne", "target": "api::expression.expression", "inversedBy": "actions_prises", "required": true}, "responsable": {"type": "relation", "relation": "manyToOne", "target": "api::profile.profile"}, "entite_responsable": {"type": "relation", "relation": "manyToOne", "target": "api::entite.entite"}, "date_creation": {"type": "datetime"}, "date_debut_prevue": {"type": "datetime"}, "date_fin_prevue": {"type": "datetime"}, "date_debut_reelle": {"type": "datetime"}, "date_fin_reelle": {"type": "datetime"}, "budget_estime": {"type": "decimal"}, "budget_reel": {"type": "decimal"}, "progression": {"type": "integer", "default": 0, "min": 0, "max": 100}, "resultats": {"type": "text"}, "preuves": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "videos", "files"]}, "impact_mesure": {"type": "json", "default": {}}, "commentaires_publics": {"type": "text"}, "visible_public": {"type": "boolean", "default": true}, "metadata": {"type": "json", "default": {}}}}