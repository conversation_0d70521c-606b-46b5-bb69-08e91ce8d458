/**
 * Expression controller
 */

import { factories } from "@strapi/strapi";

const adminRoles = [
    "validateur",
    "validateur_senior",
    "admin_regional",
    "admin_national",
    "super_admin",
];

const superAdminRoles = ["admin_regional", "admin_national", "super_admin"];

export default factories.createCoreController("api::expression.expression", ({ strapi }) => ({
    /**
     * Default find method override
     */
    async find(ctx) {
        try {
            // Ensure ctx and ctx.query exist
            if (!ctx) {
                console.error("Expression find: ctx is undefined");
                return {
                    data: [],
                    meta: { pagination: { page: 1, pageSize: 25, pageCount: 0, total: 0 } },
                };
            }

            if (!ctx.query) {
                console.log("Expression find: ctx.query is undefined, initializing empty object");
                ctx.query = {};
            }

            // Extract query parameters
            const {
                page = 1,
                pageSize = 25,
                filters = {} as any,
                populate = ["auteur", "lieu", "piliers", "sous_piliers", "entites", "medias"],
                sort = { date_creation: "desc" },
            } = ctx.query;

            // Build where clause from filters
            let where: any = {};

            // Handle different filter formats
            if (typeof filters === "object" && filters !== null) {
                where = { ...filters };
            }

            // Handle pilier filter (frontend sends 'pilier', but DB field is 'piliers')
            if (ctx.query.pilier) {
                where.piliers = { documentId: { $eq: ctx.query.pilier } };
                delete where.pilier; // Remove the singular form
            }

            // Handle other filter mappings
            if (ctx.query.lieu) {
                where.lieu = { documentId: { $eq: ctx.query.lieu } };
            }

            if (ctx.query.type_expression) {
                where.type_expression = ctx.query.type_expression;
            }

            if (ctx.query.urgence_min) {
                where.urgence = { $gte: parseInt(ctx.query.urgence_min as string) };
                delete where.urgence_min;
            }

            if (ctx.query.statut) {
                where.statut = ctx.query.statut;
            }

            // Handle date range filters
            if (ctx.query.date_debut || ctx.query.date_fin) {
                where.date_creation = {};
                if (ctx.query.date_debut) {
                    where.date_creation.$gte = new Date(ctx.query.date_debut as string);
                }
                if (ctx.query.date_fin) {
                    where.date_creation.$lte = new Date(ctx.query.date_fin as string);
                }
                delete where.date_debut;
                delete where.date_fin;
            }

            // Special handling for author filter - support filtering by current user
            if (ctx.query.myExpressions === "true" && ctx.state.user) {
                // Find the profile associated with the current user
                const profile = await strapi.documents("api::profile.profile").findFirst({
                    filters: { user: ctx.state.user.id },
                });

                if (profile) {
                    where.auteur = { documentId: { $eq: profile.documentId } };
                }
            } else if (filters.auteur) {
                // Support direct author ID filtering
                where.auteur = { documentId: { $eq: filters.auteur } };
            }

            // Get total count
            const total = await strapi.documents("api::expression.expression").count({
                filters: where,
            });

            // Fetch expressions with pagination
            const expressions = await strapi.documents("api::expression.expression").findMany({
                filters: where,
                populate: Array.isArray(populate) ? populate : [populate],
                sort,
                pagination: {
                    page: Number(page),
                    pageSize: Number(pageSize),
                },
            });

            // Return formatted response
            return {
                data: expressions || [],
                meta: {
                    pagination: {
                        page: Number(page),
                        pageSize: Number(pageSize),
                        pageCount: Math.ceil(total / Number(pageSize)),
                        total,
                    },
                },
            };
        } catch (error) {
            console.error("Error in expression find method:", error);

            // Return empty result set on error
            return {
                data: [],
                meta: {
                    pagination: {
                        page: 1,
                        pageSize: 25,
                        pageCount: 0,
                        total: 0,
                    },
                },
            };
        }
    },

    /**
     * Get public expressions with filters
     */
    async findPublic(ctx) {
        // Ensure ctx and ctx.query exist
        if (!ctx) {
            console.error("Expression findPublic: ctx is undefined");
            return {
                data: [],
                meta: { pagination: { page: 1, pageSize: 25, pageCount: 0, total: 0 } },
            };
        }

        if (!ctx.query) {
            console.log("Expression findPublic: ctx.query is undefined, initializing empty object");
            ctx.query = {};
        }

        const {
            page = 1,
            pageSize = 25,
            pilier,
            lieu,
            type_expression,
            urgence_min,
            date_debut,
            date_fin,
        } = ctx.query;

        let filters: any = {
            statut: "publie",
        };

        // Add filters
        if (pilier) {
            filters.piliers = { documentId: { $eq: pilier } };
        }

        if (lieu) {
            filters.lieu = { documentId: { $eq: lieu } };
        }

        if (type_expression) {
            filters.type_expression = type_expression;
        }

        if (urgence_min) {
            filters.urgence = { $gte: parseInt(urgence_min as string) };
        }

        if (date_debut || date_fin) {
            filters.date_publication = {};
            if (date_debut) {
                filters.date_publication.$gte = new Date(date_debut as string);
            }
            if (date_fin) {
                filters.date_publication.$lte = new Date(date_fin as string);
            }
        }

        // Get total count
        const total = await strapi.documents("api::expression.expression").count({
            filters,
        });

        // Fetch expressions with pagination
        const expressions = await strapi.documents("api::expression.expression").findMany({
            filters,
            populate: ["auteur", "lieu", "piliers", "sous_piliers", "entites", "medias"],
            sort: { date_publication: "desc" },
            pagination: {
                page: Number(page),
                pageSize: Number(pageSize),
            },
        });

        return {
            data: expressions || [],
            meta: {
                pagination: {
                    page: Number(page),
                    pageSize: Number(pageSize),
                    pageCount: Math.ceil(total / Number(pageSize)),
                    total,
                },
            },
        };
    },

    /**
     * Default findOne method override
     */
    async findOne(ctx) {
        try {
            const { id } = ctx.params;

            // Fetch expression with all relations
            const expression = await strapi.documents("api::expression.expression").findOne({
                documentId: id,
                populate: ["auteur", "lieu", "piliers", "sous_piliers", "entites", "medias"],
            });

            if (!expression) {
                return ctx.notFound("Expression not found");
            }

            // Check if the expression is in draft mode (brouillon)
            if (expression.statut !== "publie") {
                // Check if the current user is the author
                if (!ctx.state.user) {
                    return ctx.forbidden("Draft expressions can only be viewed by their authors");
                }

                // Find the profile associated with the current user
                const profile = await strapi.documents("api::profile.profile").findFirst({
                    filters: { user: ctx.state.user.id },
                });

                if (
                    !profile ||
                    expression.auteur.documentId !== profile.documentId ||
                    !adminRoles.includes(profile.role)
                ) {
                    return ctx.forbidden("Draft expressions can only be viewed by their authors");
                }
            }

            // Return formatted response
            return {
                data: expression,
                meta: {},
            };
        } catch (error) {
            console.error("Error in expression findOne method:", error);
            return ctx.notFound("Expression not found");
        }
    },
    /**
     * Create a new expression
     */
    async create(ctx) {
        try {
            const { data } = ctx.request.body;

            if (!data) {
                return ctx.badRequest("No data provided");
            }

            // Add the current user as the author
            if (!ctx.state.user) {
                return ctx.unauthorized("You must be logged in to create an expression");
            }

            // Find the profile associated with the user
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return ctx.badRequest(
                    "User profile not found. Please complete your profile first.",
                );
            }

            // Clean and prepare data for Strapi 5 validation
            const cleanedData: any = {
                // Required fields
                titre: data.titre,
                contenu: data.contenu,
                type_expression: data.type_expression,
                urgence: data.urgence || 3,
                date_evenement: data.date_evenement,

                // Relations - ensure they are document IDs
                auteur: profile.documentId,
                lieu: data.lieu,

                // Optional fields
                etat_emotionnel: data.etat_emotionnel || "neutre",
                geolocation: data.geolocation || {},
                tags: data.tags || [],

                // System fields (not from user input)
                statut: "brouillon",
                source: ctx.request.headers["user-agent"]?.includes("Mobile") ? "mobile" : "web",
                date_creation: new Date(),
                langue: data.langue || "fr",
                version: 1,

                // Initialize JSON fields with defaults
                analyse_ia: {
                    score_confiance: 0,
                    mots_cles: [],
                    entites_extraites: [],
                    sentiment_score: 0,
                    categorie_suggeree: "",
                    urgence_calculee: data.urgence || 3,
                },
                impact: {
                    vues: 0,
                    soutiens: 0,
                    partages: 0,
                    commentaires: 0,
                },
                score_ia: {},
                metadata: {},
            };

            // Handle many-to-many relations (piliers, sous_piliers, entites)
            // These fields should be arrays of IDs
            if (data.piliers && Array.isArray(data.piliers)) {
                cleanedData.piliers = data.piliers;
            }

            if (data.sous_piliers && Array.isArray(data.sous_piliers)) {
                cleanedData.sous_piliers = data.sous_piliers;
            }

            if (data.entites && Array.isArray(data.entites)) {
                cleanedData.entites = data.entites;
            }

            // Handle media files
            if (data.medias && Array.isArray(data.medias)) {
                cleanedData.medias = data.medias;
            }

            // Validate required fields
            const requiredFields = [
                "titre",
                "contenu",
                "type_expression",
                "date_evenement",
                "lieu",
            ];
            const missingFields = requiredFields.filter((field) => !cleanedData[field]);

            if (missingFields.length > 0) {
                return ctx.badRequest({
                    message: "Missing required fields",
                    details: {
                        fields: missingFields,
                    },
                });
            }

            // Validate field constraints
            if (cleanedData.titre.length > 200) {
                return ctx.badRequest("Title must not exceed 200 characters");
            }

            if (cleanedData.contenu.length < 10) {
                return ctx.badRequest("Content must be at least 10 characters long");
            }

            if (
                !["probleme", "satisfaction", "idee", "question"].includes(
                    cleanedData.type_expression,
                )
            ) {
                return ctx.badRequest("Invalid expression type");
            }

            if (cleanedData.urgence < 1 || cleanedData.urgence > 5) {
                return ctx.badRequest("Urgency must be between 1 and 5");
            }

            // Ensure lieu exists
            const lieuExists = await strapi.documents("api::lieu.lieu").findOne({
                documentId: cleanedData.lieu,
            });

            if (!lieuExists) {
                return ctx.badRequest("Selected location does not exist");
            }

            // Create the expression using the entity service
            // This will properly handle draft/publish and other Strapi features
            const createdExpression = await strapi.documents("api::expression.expression").create({
                data: cleanedData,
                populate: ["auteur", "lieu", "piliers", "sous_piliers", "entites", "medias"],
            });

            // Update profile statistics if creation was successful
            if (createdExpression) {
                const expressionCount = await strapi.documents("api::expression.expression").count({
                    filters: { auteur: { documentId: { $eq: profile.documentId } } },
                });
                await strapi.documents("api::profile.profile").update({
                    documentId: profile.documentId,
                    data: {
                        nb_expressions: expressionCount,
                        derniere_activite: new Date(),
                    },
                });
            }

            // Return the created expression in the expected format
            return {
                data: createdExpression,
                meta: {},
            };
        } catch (error: any) {
            console.error("Error creating expression:", error);
            console.error("Error name:", error.name);
            console.error("Error details:", JSON.stringify(error.details, null, 2));

            // Handle Strapi validation errors
            if (error.name === "ValidationError" || error.name === "YupValidationError") {
                return ctx.badRequest({
                    message: "Validation error",
                    details: error.details,
                });
            }

            return ctx.badRequest(error.message || "Failed to create expression");
        }
    },

    /**
     * Default update method override
     */
    async update(ctx) {
        try {
            const { id } = ctx.params;
            const { data } = ctx.request.body;

            if (!data) {
                return ctx.badRequest("No data provided");
            }

            // Check if user owns this expression
            const expression = await strapi.documents("api::expression.expression").findOne({
                documentId: id,
                populate: ["auteur"],
            });

            if (!expression) {
                return ctx.notFound("Expression not found");
            }

            if (!ctx.state.user) {
                return ctx.unauthorized("You must be logged in to update an expression");
            }

            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile || expression.auteur.documentId !== profile.documentId) {
                return ctx.forbidden("You can only update your own expressions");
            }

            // Only allow updates if expression is in draft status
            if (expression.statut !== "brouillon") {
                return ctx.badRequest("Can only update draft expressions");
            }

            // Prepare update data - only include allowed fields
            const allowedFields = [
                "titre",
                "contenu",
                "type_expression",
                "urgence",
                "etat_emotionnel",
                "date_evenement",
                "lieu",
                "geolocation",
                "tags",
            ];

            const cleanedData: any = {};

            // Only include fields that are present in the request and allowed
            for (const field of allowedFields) {
                if (data.hasOwnProperty(field) && data[field] !== undefined) {
                    cleanedData[field] = data[field];
                }
            }

            // Handle relations separately
            if (data.piliers && Array.isArray(data.piliers)) {
                cleanedData.piliers = data.piliers;
            }

            if (data.sous_piliers && Array.isArray(data.sous_piliers)) {
                cleanedData.sous_piliers = data.sous_piliers;
            }

            if (data.entites && Array.isArray(data.entites)) {
                cleanedData.entites = data.entites;
            }

            if (data.medias && Array.isArray(data.medias)) {
                cleanedData.medias = data.medias;
            }

            // Validate updated fields
            if (cleanedData.titre && cleanedData.titre.length > 200) {
                return ctx.badRequest("Title must not exceed 200 characters");
            }

            if (cleanedData.contenu && cleanedData.contenu.length < 10) {
                return ctx.badRequest("Content must be at least 10 characters long");
            }

            if (
                cleanedData.type_expression &&
                !["probleme", "satisfaction", "idee", "question"].includes(
                    cleanedData.type_expression,
                )
            ) {
                return ctx.badRequest("Invalid expression type");
            }

            if (cleanedData.urgence && (cleanedData.urgence < 1 || cleanedData.urgence > 5)) {
                return ctx.badRequest("Urgency must be between 1 and 5");
            }

            if (cleanedData.lieu) {
                const lieuExists = await strapi.documents("api::lieu.lieu").findOne({
                    documentId: cleanedData.lieu,
                });

                if (!lieuExists) {
                    return ctx.badRequest("Selected location does not exist");
                }
            }

            // Add metadata
            cleanedData.date_modification = new Date();
            cleanedData.version = (expression.version || 1) + 1;

            // Update using document service
            const updatedExpression = await strapi.documents("api::expression.expression").update({
                documentId: id,
                data: cleanedData,
                populate: ["auteur", "lieu", "piliers", "sous_piliers", "entites", "medias"],
            });

            return {
                data: updatedExpression,
                meta: {},
            };
        } catch (error) {
            console.error("Error in expression update method:", error);

            // Handle Strapi validation errors
            if (error.name === "ValidationError") {
                return ctx.badRequest({
                    message: "Validation error",
                    details: error.details,
                });
            }

            return ctx.badRequest(error.message || "Failed to update expression");
        }
    },

    /**
     * Default delete method override
     */
    async delete(ctx) {
        try {
            const { id } = ctx.params;

            // Check if user owns this expression
            const expression = await strapi.documents("api::expression.expression").findOne({
                documentId: id,
                populate: ["auteur"],
            });

            if (!expression) {
                return ctx.notFound("Expression not found");
            }

            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile || expression.auteur.documentId !== profile.documentId) {
                return ctx.forbidden("You can only delete your own expressions");
            }

            // Only allow deletion if expression is in draft status
            if (expression.statut !== "brouillon") {
                return ctx.badRequest("Can only delete draft expressions");
            }

            // Delete the expression
            const deletedExpression = await strapi.documents("api::expression.expression").delete({
                documentId: id,
            });

            return {
                data: deletedExpression,
                meta: {},
            };
        } catch (error) {
            console.error("Error in expression delete method:", error);
            return ctx.badRequest("Failed to delete expression");
        }
    },

    /**
     * Submit expression for moderation
     */
    async submit(ctx) {
        const { id } = ctx.params;

        const expression = await strapi.documents("api::expression.expression").findOne({
            documentId: id,
            populate: ["auteur", "lieu", "piliers"],
        });

        if (!expression) {
            return ctx.notFound("Expression not found");
        }

        // Check if user owns this expression
        const profile = await strapi.documents("api::profile.profile").findFirst({
            filters: { user: ctx.state.user.id },
        });

        if (!profile || expression.auteur.documentId !== profile.documentId) {
            return ctx.forbidden("You can only submit your own expressions");
        }

        if (expression.statut !== "brouillon") {
            return ctx.badRequest("Expression is not in draft status");
        }

        // Update status and submission date
        const updatedExpression = await strapi.documents("api::expression.expression").update({
            documentId: id,
            data: {
                statut: "en_moderation",
                date_soumission: new Date(),
            },
            populate: ["auteur", "lieu", "piliers", "sous_piliers", "entites"],
        });

        // TODO: Trigger AI classification
        // TODO: Assign to validator
        // TODO: Send notification

        return { data: updatedExpression };
    },

    /**
     * Get expressions for moderation queue
     */
    async moderationQueue(ctx) {
        const { page = 1, pageSize = 25, pilier, urgence, anciennete } = ctx.query;

        // Check if user is a validator
        const profile = await strapi.documents("api::profile.profile").findFirst({
            filters: { user: ctx.state.user.id },
        });

        if (!profile || !adminRoles.includes(profile.role)) {
            return ctx.forbidden("Access denied");
        }

        let filters: any = {
            statut: "en_moderation",
        };

        // Add filters
        if (pilier) {
            filters.piliers = { documentId: { $eq: pilier } };
        }

        if (urgence) {
            filters.urgence = urgence;
        }

        // Build sort criteria
        let sort = [];
        if (urgence === "desc") {
            sort.push("urgence:desc");
        }
        if (anciennete === "asc") {
            sort.push("date_soumission:asc");
        } else {
            sort.push("date_soumission:desc");
        }

        // Get total count
        const total = await strapi.documents("api::expression.expression").count({
            filters,
        });

        // Fetch expressions with pagination
        const expressions = await strapi.documents("api::expression.expression").findMany({
            filters,
            populate: ["auteur", "lieu", "piliers", "sous_piliers", "entites", "medias"],
            sort,
            pagination: {
                page: Number(page),
                pageSize: Number(pageSize),
            },
        });

        return {
            data: expressions || [],
            meta: {
                pagination: {
                    page: Number(page),
                    pageSize: Number(pageSize),
                    pageCount: Math.ceil(total / Number(pageSize)),
                    total,
                },
            },
        };
    },

    /**
     * Validate an expression (approve/reject)
     */
    async validate(ctx) {
        try {
            const { id } = ctx.params;
            const { decision, raison_rejet, piliers, sous_piliers } = ctx.request.body?.data;

            console.log(
                "Validation request received for expression",
                id,
                "with decision",
                decision,
            );

            if (!decision || !["approuve", "rejete"].includes(decision)) {
                return ctx.badRequest('Decision must be "approuve" or "rejete"');
            }

            if (!ctx.state.user) {
                return ctx.unauthorized("You must be logged in to validate expressions");
            }

            const expression = await strapi.documents("api::expression.expression").findOne({
                documentId: id,
                populate: ["auteur", "validateur"],
            });

            if (!expression) {
                return ctx.notFound("Expression not found");
            }

            if (expression.statut !== "en_moderation") {
                return ctx.badRequest("Expression is not in moderation");
            }

            // Check validator permissions
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile || !adminRoles.includes(profile.role)) {
                return ctx.forbidden("Access denied - you must be a validator");
            }

            // Find or create validator record
            let validateur = await strapi.documents("api::validateur.validateur").findFirst({
                filters: { profile: { documentId: { $eq: profile.documentId } } },
            });

            // Prepare update data
            const updateData: any = {};

            // Only add validateur if exists
            if (validateur) {
                updateData.validateur = validateur.documentId;
            }

            if (decision === "approuve") {
                updateData.statut = "publie";
                updateData.date_publication = new Date();

                // Only update piliers/sous_piliers if provided and valid
                if (piliers && Array.isArray(piliers) && piliers.length > 0) {
                    // Verify piliers exist
                    const validPiliers = await strapi.documents("api::pilier.pilier").findMany({
                        filters: { documentId: { $in: piliers } },
                    });

                    if (validPiliers.length === piliers.length) {
                        updateData.piliers = piliers;
                    }
                }

                if (sous_piliers && Array.isArray(sous_piliers) && sous_piliers.length > 0) {
                    // Verify sous_piliers exist
                    const validSousPiliers = await strapi
                        .documents("api::sous-pilier.sous-pilier")
                        .findMany({
                            filters: { documentId: { $in: sous_piliers } },
                        });

                    if (validSousPiliers.length === sous_piliers.length) {
                        updateData.sous_piliers = sous_piliers;
                    }
                }
            } else {
                updateData.statut = "rejete";

                if (!raison_rejet || raison_rejet.trim().length === 0) {
                    return ctx.badRequest(
                        "Rejection reason is required when rejecting an expression",
                    );
                }

                updateData.raison_rejet = raison_rejet.trim();
            }

            // Update the expression using document service
            const validatedExpression = await strapi
                .documents("api::expression.expression")
                .update({
                    documentId: id,
                    data: updateData,
                    populate: [
                        "auteur",
                        "lieu",
                        "piliers",
                        "sous_piliers",
                        "entites",
                        "medias",
                        "validateur",
                    ],
                });

            // Update validator statistics if validator exists
            if (validateur && validatedExpression) {
                const stats: any = validateur.statistiques || {
                    nb_validations_total: 0,
                    nb_validations_mois: 0,
                    taux_approbation: 0,
                    temps_moyen_validation: 0,
                    score_qualite: 0,
                };

                stats.nb_validations_total = (stats.nb_validations_total || 0) + 1;
                stats.nb_validations_mois = (stats.nb_validations_mois || 0) + 1;

                // Calculate approval rate
                const totalValidations = await strapi
                    .documents("api::expression.expression")
                    .count({
                        filters: {
                            validateur: { documentId: { $eq: validateur.documentId } },
                            statut: { $in: ["publie", "rejete"] },
                        },
                    });

                const approvedValidations = await strapi
                    .documents("api::expression.expression")
                    .count({
                        filters: {
                            validateur: { documentId: { $eq: validateur.documentId } },
                            statut: "publie",
                        },
                    });

                stats.taux_approbation =
                    totalValidations > 0
                        ? Math.round((approvedValidations / totalValidations) * 100)
                        : 0;

                await strapi.documents("api::validateur.validateur").update({
                    documentId: validateur.documentId,
                    data: {
                        statistiques: stats,
                        date_derniere_activite: new Date(),
                    },
                });
            }

            // Send notification to author
            if (validatedExpression && validatedExpression.auteur) {
                try {
                    await strapi.service("api::notification.notification").createNotification({
                        title:
                            decision === "approuve" ? "Expression approuvée" : "Expression rejetée",
                        message:
                            decision === "approuve"
                                ? `Votre expression "${validatedExpression.titre}" a été approuvée et est maintenant publique.`
                                : `Votre expression "${validatedExpression.titre}" a été rejetée. ${updateData.raison_rejet ? `Raison: ${updateData.raison_rejet}` : ""}`,
                        type: decision === "approuve" ? "success" : "warning",
                        category: "moderation",
                        recipientId: validatedExpression.auteur.documentId,
                        expressionId: validatedExpression.documentId,
                        actionUrl: `/expressions/${validatedExpression.documentId || id}`,
                        actionLabel: "Voir l'expression",
                    });
                } catch (notificationError) {
                    console.error("Failed to create notification:", notificationError);
                    // Don't fail the validation if notification fails
                }
            }

            // TODO: Update pillar statistics

            return {
                data: validatedExpression,
                meta: {},
            };
        } catch (error) {
            console.error("Error validating expression:", error);

            // Handle Strapi validation errors
            if (error.name === "ValidationError") {
                return ctx.badRequest({
                    message: "Validation error",
                    details: error.details,
                });
            }

            return ctx.badRequest(error.message || "Failed to validate expression");
        }
    },

    /**
     * Get aggregated statistics for analytics
     */
    async getAnalytics(ctx) {
        try {
            const { timeRange = "month" } = ctx.query;

            // Calculate date range
            const endDate = new Date();
            const startDate = new Date();

            switch (timeRange) {
                case "week":
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case "month":
                    startDate.setMonth(endDate.getMonth() - 1);
                    break;
                case "year":
                    startDate.setFullYear(endDate.getFullYear() - 1);
                    break;
                default:
                    startDate.setMonth(endDate.getMonth() - 1);
            }

            // Get total expressions count
            const totalExpressions = await strapi.documents("api::expression.expression").count({
                filters: {
                    date_creation: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                },
            });

            // Get all expressions in date range for aggregation
            const expressionsInRange = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_creation: {
                            $gte: startDate,
                            $lte: endDate,
                        },
                    },
                    fields: ["statut", "type_expression", "urgence", "date_creation"],
                    pagination: { pageSize: 10000 }, // Large page size for analytics
                });

            // Group expressions by status
            const byStatus = expressionsInRange.reduce((acc: any, expr: any) => {
                const status = expr.statut;
                acc[status] = (acc[status] || 0) + 1;
                return acc;
            }, {});

            // Group expressions by type
            const byType = expressionsInRange.reduce((acc: any, expr: any) => {
                const type = expr.type_expression;
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {});

            // Group expressions by urgency
            const byUrgency = expressionsInRange.reduce((acc: any, expr: any) => {
                const urgency = expr.urgence;
                acc[urgency] = (acc[urgency] || 0) + 1;
                return acc;
            }, {});

            // Get expressions with piliers for pilier distribution
            const expressionsWithPiliers = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_creation: {
                            $gte: startDate,
                            $lte: endDate,
                        },
                    },
                    populate: ["piliers"],
                    fields: ["id"],
                    pagination: { pageSize: 10000 }, // Large page size for analytics
                });

            // Group expressions by pilier
            const pilierCounts: Record<string, { nom: string; couleur: string; count: number }> =
                {};

            expressionsWithPiliers.forEach((expr: any) => {
                if (expr.piliers && Array.isArray(expr.piliers)) {
                    expr.piliers.forEach((pilier: any) => {
                        const key = pilier.documentId || pilier.id;
                        if (!pilierCounts[key]) {
                            pilierCounts[key] = {
                                nom: pilier.nom,
                                couleur: pilier.couleur,
                                count: 0,
                            };
                        }
                        pilierCounts[key].count += 1;
                    });
                }
            });

            // Convert to array and sort by count
            const byPilier = Object.values(pilierCounts).sort((a, b) => b.count - a.count);

            // Get expressions with resolution data for metrics calculation
            const expressionsForResolution = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_creation: {
                            $gte: startDate,
                            $lte: endDate,
                        },
                    },
                    fields: ["statut", "date_creation", "date_resolution"],
                    pagination: { pageSize: 10000 }, // Large page size for analytics
                });

            // Calculate resolution metrics
            let resolved = 0;
            let inProgress = 0;
            let rejected = 0;
            let totalResolutionTime = 0;
            let resolvedWithTime = 0;

            expressionsForResolution.forEach((expr: any) => {
                if (expr.statut === "resolu") {
                    resolved += 1;
                    if (expr.date_resolution) {
                        const resolutionTime =
                            new Date(expr.date_resolution).getTime() -
                            new Date(expr.date_creation).getTime();
                        totalResolutionTime += resolutionTime;
                        resolvedWithTime += 1;
                    }
                } else if (["en_cours", "en_moderation"].includes(expr.statut)) {
                    inProgress += 1;
                } else if (expr.statut === "rejete") {
                    rejected += 1;
                }
            });

            const avgResolutionDays =
                resolvedWithTime > 0
                    ? totalResolutionTime / resolvedWithTime / (1000 * 60 * 60 * 24)
                    : 0;

            const resolutionMetrics = [
                {
                    resolved,
                    in_progress: inProgress,
                    rejected,
                    avg_resolution_days: avgResolutionDays,
                },
            ];

            // Calculate daily trends from expressions data
            const dailyTrendsMap: Record<string, number> = {};

            expressionsInRange.forEach((expr: any) => {
                const date = new Date(expr.date_creation).toISOString().split("T")[0]; // Get YYYY-MM-DD format
                dailyTrendsMap[date] = (dailyTrendsMap[date] || 0) + 1;
            });

            // Convert to array and sort by date
            const dailyTrends = Object.entries(dailyTrendsMap)
                .map(([date, count]) => ({ date, count }))
                .sort((a, b) => a.date.localeCompare(b.date));

            // Get expressions with author information for active users count
            const expressionsWithAuthors = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_creation: {
                            $gte: startDate,
                            $lte: endDate,
                        },
                    },
                    populate: ["auteur"],
                    fields: ["id"],
                    pagination: { pageSize: 10000 }, // Large page size for analytics
                });

            // Calculate active users count
            const uniqueAuthors = new Set();
            expressionsWithAuthors.forEach((expr: any) => {
                if (expr.auteur) {
                    const authorId =
                        typeof expr.auteur === "object"
                            ? expr.auteur.documentId || expr.auteur.id
                            : expr.auteur;
                    uniqueAuthors.add(authorId);
                }
            });

            const activeUsers = [{ count: uniqueAuthors.size }];

            // Get expressions with lieu for geographic distribution
            const expressionsWithLieu = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_creation: {
                            $gte: startDate,
                            $lte: endDate,
                        },
                    },
                    populate: ["lieu"],
                    fields: ["id"],
                    pagination: { pageSize: 10000 }, // Large page size for analytics
                });

            // Group expressions by lieu
            const lieuCounts: Record<string, { nom: string; type: string; count: number }> = {};

            expressionsWithLieu.forEach((expr: any) => {
                if (expr.lieu) {
                    const lieu = expr.lieu;
                    const key = lieu.documentId || lieu.id;
                    if (!lieuCounts[key]) {
                        lieuCounts[key] = {
                            nom: lieu.nom,
                            type: lieu.type,
                            count: 0,
                        };
                    }
                    lieuCounts[key].count += 1;
                }
            });

            // Convert to array, sort by count, and take top 10
            const geographicDistribution = Object.values(lieuCounts)
                .sort((a, b) => b.count - a.count)
                .slice(0, 10);

            return {
                data: {
                    overview: {
                        totalExpressions,
                        activeUsers: activeUsers[0]?.count || 0,
                        resolutionRate:
                            totalExpressions > 0
                                ? (
                                      ((resolutionMetrics[0]?.resolved || 0) / totalExpressions) *
                                      100
                                  ).toFixed(1)
                                : 0,
                        avgResolutionTime: resolutionMetrics[0]?.avg_resolution_days
                            ? Math.round(resolutionMetrics[0].avg_resolution_days)
                            : 0,
                    },
                    distributions: {
                        byStatus,
                        byType,
                        byUrgency,
                        byPilier: byPilier.map((item: any) => ({
                            name: item.nom,
                            color: item.couleur,
                            count: item.count,
                        })),
                    },
                    trends: {
                        daily: dailyTrends,
                        resolutionMetrics: {
                            resolved: resolutionMetrics[0]?.resolved || 0,
                            inProgress: resolutionMetrics[0]?.in_progress || 0,
                            rejected: resolutionMetrics[0]?.rejected || 0,
                        },
                    },
                    geographic: {
                        topLocations: geographicDistribution.map((item: any) => ({
                            name: item.nom,
                            type: item.type,
                            count: item.count,
                        })),
                    },
                    timeRange: {
                        start: startDate.toISOString(),
                        end: endDate.toISOString(),
                    },
                },
                meta: {},
            };
        } catch (error) {
            console.error("Error in analytics endpoint:", error);
            return ctx.badRequest("Failed to generate analytics");
        }
    },

    /**
     * Get user-specific statistics
     */
    async getUserStats(ctx) {
        try {
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            // Find the profile associated with the current user
            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile) {
                return {
                    data: {
                        total_expressions: 0,
                        by_status: {},
                        by_type: {},
                        by_urgency: {},
                        resolution_rate: 0,
                    },
                };
            }

            // Get total expressions count for user
            const totalExpressions = await strapi.documents("api::expression.expression").count({
                filters: { auteur: { documentId: { $eq: profile.documentId } } },
            });

            // Get expressions by status
            const expressions = await strapi.documents("api::expression.expression").findMany({
                filters: { auteur: { documentId: { $eq: profile.documentId } } },
                fields: ["statut", "type_expression", "urgence"],
            });

            // Calculate distributions
            const byStatus = expressions.reduce((acc: any, expr: any) => {
                acc[expr.statut] = (acc[expr.statut] || 0) + 1;
                return acc;
            }, {});

            const byType = expressions.reduce((acc: any, expr: any) => {
                acc[expr.type_expression] = (acc[expr.type_expression] || 0) + 1;
                return acc;
            }, {});

            const byUrgency = expressions.reduce((acc: any, expr: any) => {
                acc[expr.urgence] = (acc[expr.urgence] || 0) + 1;
                return acc;
            }, {});

            const resolvedCount = byStatus["resolu"] || 0;
            const resolutionRate =
                totalExpressions > 0 ? ((resolvedCount / totalExpressions) * 100).toFixed(1) : "0";

            return {
                data: {
                    total_expressions: totalExpressions,
                    by_status: byStatus,
                    by_type: byType,
                    by_urgency: byUrgency,
                    resolution_rate: parseFloat(resolutionRate),
                    resolved_count: resolvedCount,
                    pending_count: byStatus["en_cours"] || 0,
                    draft_count: byStatus["brouillon"] || 0,
                },
                meta: {},
            };
        } catch (error) {
            console.error("Error in user stats endpoint:", error);
            return ctx.badRequest("Failed to get user statistics");
        }
    },

    /**
     * Get global expression statistics
     */
    async getGlobalStats(ctx) {
        const { timeRange = "month" } = ctx.query;

        // Build date filter
        let dateFilter = {};
        const now = new Date();

        if (timeRange === "week") {
            const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            dateFilter = { date_creation: { $gte: oneWeekAgo } };
        } else if (timeRange === "month") {
            const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            dateFilter = { date_creation: { $gte: oneMonthAgo } };
        } else if (timeRange === "year") {
            const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            dateFilter = { date_creation: { $gte: oneYearAgo } };
        }

        // Get total expressions
        const totalExpressions = await strapi.documents("api::expression.expression").count({});

        // Get expressions in time range
        const timeRangeExpressions = await strapi.documents("api::expression.expression").count({
            filters: dateFilter,
        });

        // Get expressions by status
        const allExpressions = await strapi.documents("api::expression.expression").findMany({
            fields: [
                "statut",
                "type_expression",
                "urgence",
                "etat_emotionnel",
                "date_creation",
                "date_resolution",
            ],
            filters: dateFilter,
        });

        // Calculate statistics
        const stats = {
            by_status: {},
            by_type: {},
            by_urgency: {},
            by_emotion: {},
            resolution_metrics: {
                total_resolved: 0,
                avg_resolution_time: 0,
                resolution_rate: 0,
            },
        };

        let totalResolutionTime = 0;
        let resolvedCount = 0;

        allExpressions.forEach((expr) => {
            stats.by_status[expr.statut] = (stats.by_status[expr.statut] || 0) + 1;
            stats.by_type[expr.type_expression] = (stats.by_type[expr.type_expression] || 0) + 1;
            stats.by_urgency[expr.urgence] = (stats.by_urgency[expr.urgence] || 0) + 1;
            stats.by_emotion[expr.etat_emotionnel] =
                (stats.by_emotion[expr.etat_emotionnel] || 0) + 1;

            // Resolution time calculation
            if (expr.statut === "resolu" && expr.date_resolution) {
                const resolutionTime =
                    new Date(expr.date_resolution).getTime() -
                    new Date(expr.date_creation).getTime();
                totalResolutionTime += resolutionTime;
                resolvedCount++;
            }
        });

        // Calculate resolution metrics
        stats.resolution_metrics.total_resolved = resolvedCount;
        stats.resolution_metrics.avg_resolution_time =
            resolvedCount > 0
                ? Math.round(totalResolutionTime / resolvedCount / (1000 * 60 * 60 * 24))
                : 0; // in days

        const publishedCount = (stats.by_status["publie"] || 0) + resolvedCount;
        stats.resolution_metrics.resolution_rate =
            publishedCount > 0 ? (resolvedCount / publishedCount) * 100 : 0;

        // Get pillar distribution
        const pillarDistribution = await strapi.documents("api::expression.expression").findMany({
            filters: dateFilter,
            populate: ["piliers"],
            pagination: { pageSize: 1000 }, // Sample for performance
        });

        const pillarStats = {};
        pillarDistribution.forEach((expr) => {
            if (expr.piliers && Array.isArray(expr.piliers)) {
                expr.piliers.forEach((pillar) => {
                    pillarStats[pillar.code] = (pillarStats[pillar.code] || 0) + 1;
                });
            }
        });

        // Get geographic distribution
        const geoDistribution = await strapi.documents("api::expression.expression").findMany({
            filters: dateFilter,
            populate: ["lieu"],
            pagination: { pageSize: 1000 }, // Sample for performance
        });

        const regionStats = {};
        geoDistribution.forEach((expr) => {
            if (expr.lieu?.region) {
                regionStats[expr.lieu.region] = (regionStats[expr.lieu.region] || 0) + 1;
            }
        });

        // Sort and get top regions
        const topRegions = Object.entries(regionStats)
            .map(([region, count]) => ({ region, count: count as number }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        // Get daily trend for past 30 days
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const recentExpressions = await strapi.documents("api::expression.expression").findMany({
            filters: {
                date_creation: { $gte: thirtyDaysAgo },
            },
            fields: ["date_creation", "statut"],
        });

        const dailyTrend = {};
        recentExpressions.forEach((expr) => {
            const date = new Date(expr.date_creation).toISOString().split("T")[0];
            if (!dailyTrend[date]) {
                dailyTrend[date] = {
                    total: 0,
                    published: 0,
                    resolved: 0,
                };
            }
            dailyTrend[date].total++;
            if (expr.statut === "publie") dailyTrend[date].published++;
            if (expr.statut === "resolu") dailyTrend[date].resolved++;
        });

        return {
            data: {
                summary: {
                    total_expressions: totalExpressions,
                    period_expressions: timeRangeExpressions,
                    growth_rate:
                        totalExpressions > 0
                            ? ((timeRangeExpressions / totalExpressions) * 100).toFixed(2)
                            : 0,
                },
                distribution: {
                    by_status: stats.by_status,
                    by_type: stats.by_type,
                    by_urgency: stats.by_urgency,
                    by_emotion: stats.by_emotion,
                    by_pillar: pillarStats,
                    by_region: topRegions,
                },
                resolution: stats.resolution_metrics,
                trends: {
                    daily: dailyTrend,
                },
                time_range: timeRange,
            },
        };
    },

    /**
     * Get moderation statistics
     */
    async getModerationStats(ctx) {
        try {
            // Check if user is a validator or admin
            if (!ctx.state.user) {
                return ctx.unauthorized("Authentication required");
            }

            const profile = await strapi.documents("api::profile.profile").findFirst({
                filters: { user: ctx.state.user.id },
            });

            if (!profile || !adminRoles.includes(profile.role)) {
                return ctx.forbidden("Access denied - validator or admin role required");
            }

            // Get pending expressions count
            const pending = await strapi.documents("api::expression.expression").count({
                filters: { statut: "en_moderation" },
            });

            // Get approved expressions count (published)
            const approved = await strapi.documents("api::expression.expression").count({
                filters: { statut: "publie" },
            });

            // Get rejected expressions count
            const rejected = await strapi.documents("api::expression.expression").count({
                filters: { statut: "rejete" },
            });

            // Get today's processed expressions
            const todayStart = new Date();
            todayStart.setHours(0, 0, 0, 0);

            const todayProcessed = await strapi.documents("api::expression.expression").count({
                filters: {
                    date_publication: {
                        $gte: todayStart,
                    },
                    statut: {
                        $in: ["publie", "rejete"],
                    },
                },
            });

            // Get expressions by urgency level for pending
            const urgencyDistribution = {};
            for (let urgence = 1; urgence <= 5; urgence++) {
                const count = await strapi.documents("api::expression.expression").count({
                    filters: {
                        statut: "en_moderation",
                        urgence: urgence,
                    },
                });
                urgencyDistribution[urgence] = count;
            }

            // Get average processing time
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const processedExpressions = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_publication: { $gte: thirtyDaysAgo },
                        date_soumission: { $ne: null },
                        statut: { $in: ["publie", "rejete"] },
                    },
                    fields: ["date_publication", "date_soumission"],
                    pagination: { pageSize: 1000 }, // Reasonable limit for performance
                });

            // Calculate processing times
            let totalHours = 0;
            let minHours = Infinity;
            let maxHours = 0;
            let validCount = 0;

            processedExpressions.forEach((expr) => {
                if (expr.date_publication && expr.date_soumission) {
                    const pubDate = new Date(expr.date_publication);
                    const subDate = new Date(expr.date_soumission);
                    const hours = (pubDate.getTime() - subDate.getTime()) / (1000 * 60 * 60);

                    if (hours >= 0) {
                        totalHours += hours;
                        minHours = Math.min(minHours, hours);
                        maxHours = Math.max(maxHours, hours);
                        validCount++;
                    }
                }
            });

            const processingTimeStats = {
                avg_hours: validCount > 0 ? totalHours / validCount : 0,
                min_hours: minHours === Infinity ? 0 : minHours,
                max_hours: maxHours,
            };

            // Get weekly trend
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

            const weeklyExpressions = await strapi
                .documents("api::expression.expression")
                .findMany({
                    filters: {
                        date_publication: { $gte: sevenDaysAgo },
                        statut: { $in: ["publie", "rejete"] },
                    },
                    fields: ["date_publication", "statut"],
                });

            // Group by date
            const dailyStats = {};
            weeklyExpressions.forEach((expr) => {
                if (expr.date_publication) {
                    const dateKey = new Date(expr.date_publication).toISOString().split("T")[0];
                    if (!dailyStats[dateKey]) {
                        dailyStats[dateKey] = { approved: 0, rejected: 0 };
                    }
                    if (expr.statut === "publie") {
                        dailyStats[dateKey].approved++;
                    } else if (expr.statut === "rejete") {
                        dailyStats[dateKey].rejected++;
                    }
                }
            });

            const weeklyTrend = Object.entries(dailyStats)
                .map(([date, stats]: [string, any]) => ({
                    date,
                    approved: stats.approved,
                    rejected: stats.rejected,
                }))
                .sort((a, b) => b.date.localeCompare(a.date));

            // Get moderator activity (if current user is admin)
            let moderatorActivity = [];
            if (superAdminRoles.includes(profile.role)) {
                // Get active validators with their profiles
                const validators = await strapi.documents("api::validateur.validateur").findMany({
                    filters: { actif: true },
                    populate: ["profile"],
                    pagination: { pageSize: 10 },
                });

                // Get today's processed expressions for each validator
                const todayStart = new Date();
                todayStart.setHours(0, 0, 0, 0);

                moderatorActivity = await Promise.all(
                    validators.map(async (validator) => {
                        // Count expressions validated today by this validator
                        const processedToday = await strapi
                            .documents("api::expression.expression")
                            .count({
                                filters: {
                                    validateur: { documentId: { $eq: validator.documentId } },
                                    date_publication: { $gte: todayStart },
                                    statut: { $in: ["publie", "rejete"] },
                                },
                            });

                        // Parse approval rate from validator statistics
                        const stats = validator.statistiques as any;
                        const approvalRate = stats?.taux_approbation || 0;
                        const validatorName = validator.profile?.nom || "Unknown Validator";

                        return {
                            name: validatorName,
                            processedToday,
                            approvalRate:
                                typeof approvalRate === "string"
                                    ? parseInt(approvalRate)
                                    : approvalRate,
                        };
                    }),
                );

                // Sort by most active today
                moderatorActivity = moderatorActivity
                    .sort((a, b) => b.processedToday - a.processedToday)
                    .slice(0, 10);
            }

            return {
                data: {
                    pending,
                    approved,
                    rejected,
                    todayProcessed,
                    urgencyDistribution,
                    processingTime: {
                        average: Math.round(processingTimeStats.avg_hours || 0),
                        min: Math.round(processingTimeStats.min_hours || 0),
                        max: Math.round(processingTimeStats.max_hours || 0),
                    },
                    weeklyTrend,
                    moderatorActivity,
                },
                meta: {},
            };
        } catch (error) {
            console.error("Error in getModerationStats:", error);
            return ctx.badRequest("Failed to get moderation statistics");
        }
    },
}));
