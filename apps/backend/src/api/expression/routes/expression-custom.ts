/**
 * Custom expression routes
 */

export default {
    routes: [
        // Public routes
        {
            method: "GET",
            path: "/expressions/public",
            handler: "expression.findPublic",
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },

        // Protected routes
        {
            method: "GET",
            path: "/expressions",
            handler: "expression.find",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/expressions",
            handler: "expression.create",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "PUT",
            path: "/expressions/:id",
            handler: "expression.update",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "DELETE",
            path: "/expressions/:id",
            handler: "expression.delete",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Custom routes
        {
            method: "POST",
            path: "/expressions/:id/submit",
            handler: "expression.submit",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/expressions/moderation/queue",
            handler: "expression.moderationQueue",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "POST",
            path: "/expressions/:id/validate",
            handler: "expression.validate",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Analytics routes (before :id route to avoid conflicts)
        {
            method: "GET",
            path: "/expressions/analytics",
            handler: "expression.getAnalytics",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: "GET",
            path: "/expressions/user-stats",
            handler: "expression.getUserStats",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Global expressions statistics endpoint
        {
            method: "GET",
            path: "/expressions/stats",
            handler: "expression.getGlobalStats",
            config: {
                // auth: false,
                policies: [],
                middlewares: [],
            },
        },
        // Moderation statistics endpoint
        {
            method: "GET",
            path: "/expressions/moderation/stats",
            handler: "expression.getModerationStats",
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Dynamic :id route - must come after all static routes
        {
            method: "GET",
            path: "/expressions/:id",
            handler: "expression.findOne",
            config: {
                // auth: false,
                policies: [],
                middlewares: [],
            },
        },
    ],
};
