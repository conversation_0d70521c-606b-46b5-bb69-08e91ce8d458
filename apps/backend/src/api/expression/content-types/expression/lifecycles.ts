/**
 * Expression lifecycle hooks
 */

module.exports = {
    async beforeCreate(event: any) {
        const { data } = event.params;

        // Set creation date
        if (!data.date_creation) {
            data.date_creation = new Date();
        }

        // Set default values
        data.statut = data.statut || "brouillon";
        data.source = data.source || "web";
    },

    async afterCreate(event: any) {
        const { result } = event;

        // Update profile statistics if auteur is set
        if (result.auteur) {
            try {
                // Extract the auteur ID (it might be an object or just an ID)
                const auteurId =
                    typeof result.auteur === "object" ? result.auteur.id : result.auteur;

                const expressionCount = await strapi.documents("api::expression.expression").count({
                    filters: { auteur: auteurId },
                });

                // Find the profile to get its documentId
                const profile = await strapi.documents("api::profile.profile").findFirst({
                    filters: { id: auteurId },
                });

                if (profile) {
                    await strapi.documents("api::profile.profile").update({
                        documentId: profile.documentId,
                        data: {
                            nb_expressions: expressionCount,
                            derniere_activite: new Date(),
                        },
                    });
                }
            } catch (error) {
                strapi.log.error("Error updating profile statistics:", error);
            }
        }
    },

    async beforeUpdate(event: any) {
        const { data } = event.params;

        // Update status-related dates
        if (data.statut) {
            const now = new Date();

            switch (data.statut) {
                case "en_moderation":
                    if (!data.date_soumission) {
                        data.date_soumission = now;
                    }
                    break;
                case "publie":
                    if (!data.date_publication) {
                        data.date_publication = now;
                    }
                    break;
                case "resolu":
                    if (!data.date_resolution) {
                        data.date_resolution = now;
                    }
                    break;
            }
        }
    },

    async afterUpdate(event: any) {
        const { result } = event;

        // Update profile statistics if auteur is set
        if (result.auteur) {
            try {
                // Extract the auteur ID (it might be an object or just an ID)
                const auteurId =
                    typeof result.auteur === "object" ? result.auteur.id : result.auteur;

                const expressionCount = await strapi.documents("api::expression.expression").count({
                    filters: { auteur: auteurId },
                });

                // Find the profile to get its documentId
                const profile = await strapi.documents("api::profile.profile").findFirst({
                    filters: { id: auteurId },
                });

                if (profile) {
                    await strapi.documents("api::profile.profile").update({
                        documentId: profile.documentId,
                        data: {
                            nb_expressions: expressionCount,
                            derniere_activite: new Date(),
                        },
                    });
                }
            } catch (error) {
                strapi.log.error("Error updating profile statistics after update:", error);
            }
        }
    },
};
