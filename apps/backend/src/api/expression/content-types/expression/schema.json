{"kind": "collectionType", "collectionName": "expressions", "info": {"singularName": "expression", "pluralName": "expressions", "displayName": "Expression", "description": "Citizen expressions - the core content of PillarScan"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"titre": {"type": "string", "required": true, "maxLength": 200, "description": "Titre de l'expression citoyenne"}, "contenu": {"type": "text", "required": true, "minLength": 10, "description": "Description détaillée de l'expression"}, "type_expression": {"type": "enumeration", "enum": ["probleme", "satisfaction", "idee", "question"], "required": true, "description": "Type d'expression citoyenne"}, "urgence": {"type": "integer", "required": true, "min": 1, "max": 5, "default": 3, "description": "Niveau d'urgence (1: <PERSON><PERSON><PERSON> fai<PERSON>, 5: <PERSON><PERSON><PERSON>)"}, "etat_emotionnel": {"type": "enumeration", "enum": ["colere", "joie", "tristesse", "espoir", "neutre", "frustration"], "default": "neutre", "description": "État émotionnel associé à l'expression"}, "statut": {"type": "enumeration", "enum": ["brouillon", "en_moderation", "publie", "en_traitement", "resolu", "rejete", "archive"], "default": "brouillon", "required": true, "description": "Statut de l'expression dans le processus de validation"}, "raison_rejet": {"type": "text", "description": "Raison du rejet (si applicable)"}, "auteur": {"type": "relation", "relation": "manyToOne", "target": "api::profile.profile", "inversedBy": "expressions", "required": true, "description": "Auteur de l'expression"}, "validateur": {"type": "relation", "relation": "manyToOne", "target": "api::validateur.validateur", "inversedBy": "expressions_validees", "description": "Validateur assigné à l'expression"}, "date_evenement": {"type": "date", "required": true, "description": "Date de l'événement décrit dans l'expression"}, "date_creation": {"type": "datetime", "description": "Date de création de l'expression"}, "date_soumission": {"type": "datetime", "description": "Date de soumission pour modération"}, "date_publication": {"type": "datetime", "description": "Date de publication de l'expression"}, "date_resolution": {"type": "datetime", "description": "Date de résolution de l'expression"}, "lieu": {"type": "relation", "relation": "manyToOne", "target": "api::lieu.lieu", "inversedBy": "expressions", "required": true, "description": "Lieu concerné par l'expression"}, "piliers": {"type": "relation", "relation": "manyToMany", "target": "api::pilier.pilier", "inversedBy": "expressions", "description": "Piliers de la société concernés"}, "sous_piliers": {"type": "relation", "relation": "manyToMany", "target": "api::sous-pilier.sous-pilier", "inversedBy": "expressions", "description": "Sous-piliers spécifiques concernés"}, "entites": {"type": "relation", "relation": "manyToMany", "target": "api::entite.entite", "inversedBy": "expressions", "description": "Entités responsables concernées"}, "medias": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "videos", "files"], "description": "Pièces justificatives (photos, vidéos, documents)"}, "geolocation": {"type": "json", "default": {}, "description": "Coordonnées géographiques précises"}, "score_ia": {"type": "json", "default": {}, "description": "Scores d'analyse par intelligence artificielle"}, "analyse_ia": {"type": "json", "default": {"score_confiance": 0, "mots_cles": [], "entites_extraites": [], "sentiment_score": 0, "categorie_suggeree": "", "urgence_calculee": 3}, "description": "Analyse d<PERSON> par intelligence artificielle"}, "impact": {"type": "json", "default": {"vues": 0, "soutiens": 0, "partages": 0, "commentaires": 0}, "description": "Métriques d'impact et d'engagement"}, "actions_prises": {"type": "relation", "relation": "oneToMany", "target": "api::action.action", "mappedBy": "expression", "description": "Actions entreprises en réponse à l'expression"}, "satisfaction_resolution": {"type": "integer", "min": 1, "max": 5, "description": "Niveau de satisfaction de l'auteur avec la résolution (1-5)"}, "tags": {"type": "json", "default": [], "description": "Mots-clés et étiquettes"}, "source": {"type": "enumeration", "enum": ["web", "mobile", "api", "import"], "default": "web", "description": "Source de création de l'expression"}, "langue": {"type": "string", "default": "fr", "maxLength": 5, "description": "Langue de l'expression (code ISO)"}, "version": {"type": "integer", "default": 1, "description": "Version de l'expression"}, "metadata": {"type": "json", "default": {}, "description": "Métadonnées supplémentaires"}}}