{"kind": "collectionType", "collectionName": "sous_piliers", "info": {"singularName": "sous-pilier", "pluralName": "sous-piliers", "displayName": "Sous-Pilier", "description": "Sub-categories of the main pillars for detailed classification"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"code": {"type": "string", "required": true, "unique": true, "maxLength": 30}, "nom": {"type": "string", "required": true, "maxLength": 100}, "nom_en": {"type": "string", "maxLength": 100}, "description": {"type": "text"}, "ordre": {"type": "integer", "required": true, "min": 1}, "actif": {"type": "boolean", "default": true, "required": true}, "pilier": {"type": "relation", "relation": "manyToOne", "target": "api::pilier.pilier", "inversedBy": "sous_piliers", "required": true}, "expressions": {"type": "relation", "relation": "manyToMany", "target": "api::expression.expression", "mappedBy": "sous_piliers"}, "mots_cles": {"type": "json", "default": []}, "statistiques": {"type": "json", "default": {"nb_expressions_total": 0, "nb_expressions_mois": 0, "taux_resolution": 0.0}}, "metadata": {"type": "json", "default": {}}}}