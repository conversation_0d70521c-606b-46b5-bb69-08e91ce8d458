{"kind": "collectionType", "collectionName": "perimetres", "info": {"singularName": "perimetre", "pluralName": "perimetres", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Geographic or thematic perimeters for validator assignments"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"nom": {"type": "string", "required": true, "maxLength": 200}, "type": {"type": "enumeration", "enum": ["global", "national", "regional", "departemental", "local", "thematique", "linguistique"], "required": true}, "description": {"type": "text"}, "zones_geographiques": {"type": "relation", "relation": "manyToMany", "target": "api::lieu.lieu"}, "piliers_concernes": {"type": "relation", "relation": "manyToMany", "target": "api::pilier.pilier"}, "validateurs": {"type": "relation", "relation": "manyToMany", "target": "api::validateur.validateur", "mappedBy": "perimetres"}, "actif": {"type": "boolean", "default": true, "required": true}, "priorite": {"type": "integer", "default": 1, "min": 1, "max": 10}, "coordonnees_geo": {"type": "json", "default": null}, "criteres_inclusion": {"type": "json", "default": {}}, "metadata": {"type": "json", "default": {}}}}