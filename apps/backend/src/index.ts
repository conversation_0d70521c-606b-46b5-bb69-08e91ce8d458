// import type { Core } from '@strapi/strapi';
import { setupPermissions, createCustomRoles } from "./utils/setup-permissions";
import { setupPillars } from "./utils/setup-pillars";
import seedLocations from "./scripts/seed-locations";

export default {
    /**
     * An asynchronous register function that runs before
     * your application is initialized.
     *
     * This gives you an opportunity to extend code.
     */
    register({ strapi }: { strapi: any }) {
        // Register the profile relation on user model during initialization
        const extensionService = strapi.plugin("users-permissions")?.service("content-type");

        if (extensionService) {
            console.log("[Register] Extending user model with profile relation");

            // Get the current user content type
            const userContentType = strapi.contentType("plugin::users-permissions.user");

            if (userContentType && !userContentType.attributes.profile) {
                console.log("[Register] Adding profile attribute to user model");

                // Note: In Strapi 5, relations need to be defined at build time
                // The proper way is to use the schema.json approach
            }
        }
    },

    /**
     * An asynchronous bootstrap function that runs before
     * your application gets started.
     *
     * This gives you an opportunity to set up your data model,
     * run jobs, or perform some special logic.
     */
    async bootstrap({ strapi }: { strapi: any }) {
        console.log("🚀 Starting PillarScan bootstrap process...");

        try {
            // Setup pillars first as they are referenced by other entities
            await setupPillars(strapi);

            // Seed initial data
            await seedLocations(strapi);

            // Create custom roles if they don't exist
            await createCustomRoles(strapi);

            // Setup permissions for all APIs
            await setupPermissions(strapi);

            console.log("✅ Bootstrap completed successfully!");
        } catch (error) {
            console.error("❌ Bootstrap failed:", error);
            // Don't throw the error to allow Strapi to start
            // Permissions can be manually configured later if needed
        }
    },
};
