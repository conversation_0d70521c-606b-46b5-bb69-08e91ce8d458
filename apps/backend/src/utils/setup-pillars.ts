/**
 * PillarS<PERSON> Pillars Setup
 * Creates/updates the 12 pillars of French society on Strapi bootstrap
 */

export async function setupPillars(strapi: any) {
    try {
        // Define the 12 pillars of French society
        const pillars = [
            {
                code: "SANTE",
                nom: "Santé",
                nom_en: "Health",
                description:
                    "Système de santé publique, accès aux soins, prévention, hôpitaux, médecins généralistes et spécialistes.",
                domaine: "services_publics",
                ordre: 1,
                icone: "heart",
                couleur: "#e74c3c",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "EDUCATION",
                nom: "Éducation",
                nom_en: "Education",
                description:
                    "Système éducatif national, écoles, collèges, lycées, universités, formation professionnelle.",
                domaine: "services_publics",
                ordre: 2,
                icone: "academic-cap",
                couleur: "#3498db",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "SECURITE",
                nom: "Sécurité",
                nom_en: "Security",
                description:
                    "Sécurité publique, police nationale, gendarmerie, pompiers, sécurité civile.",
                domaine: "services_publics",
                ordre: 3,
                icone: "shield-check",
                couleur: "#2c3e50",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "TRANSPORT",
                nom: "Transport",
                nom_en: "Transportation",
                description:
                    "Transports en commun, infrastructure routière, SNCF, mobilité urbaine et rurale.",
                domaine: "vie_quotidienne",
                ordre: 4,
                icone: "truck",
                couleur: "#9b59b6",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "LOGEMENT",
                nom: "Logement",
                nom_en: "Housing",
                description:
                    "Politique du logement, logement social, urbanisme, rénovation urbaine.",
                domaine: "vie_quotidienne",
                ordre: 5,
                icone: "home",
                couleur: "#e67e22",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "ENVIRONNEMENT",
                nom: "Environnement",
                nom_en: "Environment",
                description:
                    "Écologie, développement durable, gestion des déchets, qualité de l'air, espaces verts.",
                domaine: "vie_quotidienne",
                ordre: 6,
                icone: "leaf",
                couleur: "#27ae60",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "ECONOMIE",
                nom: "Économie",
                nom_en: "Economy",
                description:
                    "Politique économique, emploi, entreprises, fiscalité, développement économique local.",
                domaine: "economie_social",
                ordre: 7,
                icone: "chart-bar",
                couleur: "#f39c12",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "SOCIAL",
                nom: "Action Sociale",
                nom_en: "Social Action",
                description:
                    "Aide sociale, solidarité, lutte contre la pauvreté, services sociaux, inclusion.",
                domaine: "economie_social",
                ordre: 8,
                icone: "users",
                couleur: "#e91e63",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "CULTURE",
                nom: "Culture",
                nom_en: "Culture",
                description:
                    "Patrimoine culturel, arts, spectacles, bibliothèques, musées, événements culturels.",
                domaine: "economie_social",
                ordre: 9,
                icone: "building-library",
                couleur: "#8e44ad",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "JUSTICE",
                nom: "Justice",
                nom_en: "Justice",
                description:
                    "Système judiciaire, tribunaux, accès au droit, médiation, droits des citoyens.",
                domaine: "gouvernance_democratie",
                ordre: 10,
                icone: "scale",
                couleur: "#34495e",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "ADMINISTRATION",
                nom: "Administration",
                nom_en: "Administration",
                description:
                    "Services publics administratifs, démarches citoyennes, état civil, fiscalité locale.",
                domaine: "gouvernance_democratie",
                ordre: 11,
                icone: "building-office",
                couleur: "#16a085",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
            {
                code: "PARTICIPATION",
                nom: "Participation Citoyenne",
                nom_en: "Citizen Participation",
                description:
                    "Démocratie participative, consultations publiques, conseils citoyens, vie démocratique locale.",
                domaine: "gouvernance_democratie",
                ordre: 12,
                icone: "megaphone",
                couleur: "#c0392b",
                actif: true,
                statistiques: {
                    nb_expressions_total: 0,
                    nb_expressions_mois: 0,
                    taux_resolution: 0.0,
                    temps_moyen_resolution: 0,
                },
            },
        ];

        // Check if pillars already exist
        const existingPillars = await strapi.documents("api::pilier.pilier").findMany();

        if (existingPillars.length > 0) {
            console.log(
                `📋 Found ${existingPillars.length} existing pillars. Checking for updates...`,
            );

            // Update existing pillars and create new ones
            for (const pillar of pillars) {
                const existing = existingPillars.find((p: any) => p.code === pillar.code);
                if (existing) {
                    // Only update if there are changes
                    const needsUpdate = Object.keys(pillar).some((key) => {
                        if (key === "statistiques") {
                            // Don't overwrite statistiques if they already have values
                            return false;
                        }
                        return JSON.stringify(existing[key]) !== JSON.stringify(pillar[key]);
                    });

                    if (needsUpdate) {
                        // Preserve existing statistics
                        const dataToUpdate = {
                            ...pillar,
                            statistiques: existing.statistiques || pillar.statistiques,
                        };

                        await strapi.documents("api::pilier.pilier").update({
                            documentId: existing.documentId,
                            data: dataToUpdate,
                        });
                    }
                } else {
                    await strapi.documents("api::pilier.pilier").create({
                        data: pillar,
                    });
                }
            }
        } else {
            // Create all pillars
            for (const pillar of pillars) {
                await strapi.documents("api::pilier.pilier").create({
                    data: pillar,
                });
            }
        }

        const totalPillars = await strapi.documents("api::pilier.pilier").count();
        console.log(`✅ Pillars setup completed! Total pillars: ${totalPillars}`);
    } catch (error) {
        console.error("❌ Error setting up pillars:", error);
        throw error;
    }
}

// Helper function to get all pillars (useful for other setup scripts)
export async function getAllPillars(strapi: any) {
    const results = await strapi.documents("api::pilier.pilier").findMany({
        sort: { ordre: "asc" },
    });
    return results;
}

// Helper function to get a pillar by code
export async function getPillarByCode(strapi: any, code: string) {
    return await strapi.documents("api::pilier.pilier").findFirst({
        filters: { code },
    });
}
