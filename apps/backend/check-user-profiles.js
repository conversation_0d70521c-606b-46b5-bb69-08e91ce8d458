// Script to check user-profile relations in the database
const sqlite3 = require("better-sqlite3");
const path = require("path");

// Open the database
const dbPath = path.join(__dirname, ".tmp/data.db");
console.log("Opening database:", dbPath);

try {
    const db = sqlite3(dbPath, { readonly: true });

    // Check users
    console.log("\n=== USERS ===");
    const users = db.prepare("SELECT id, username, email FROM up_users LIMIT 5").all();
    console.log("Users found:", users.length);
    users.forEach((user) => {
        console.log(`- User ${user.id}: ${user.username} (${user.email})`);
    });

    // Check profiles
    console.log("\n=== PROFILES ===");
    const profiles = db.prepare("SELECT id, nom FROM profiles LIMIT 5").all();
    console.log("Profiles found:", profiles.length);
    profiles.forEach((profile) => {
        console.log(`- Profile ${profile.id}: ${profile.nom}`);
    });

    // Check if there's a relation table
    console.log("\n=== CHECKING RELATIONS ===");

    // List all tables
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
    console.log("\nAll tables in database:");
    tables.forEach((table) => {
        if (table.name.includes("user") || table.name.includes("profile")) {
            console.log(`- ${table.name}`);
        }
    });

    // Check for profile_user_links table (this is how Strapi stores relations)
    const relationTables = tables.filter((t) => t.name.includes("links"));
    console.log("\nRelation tables found:");
    relationTables.forEach((table) => {
        console.log(`- ${table.name}`);

        // Get sample data from relation table
        try {
            const sample = db.prepare(`SELECT * FROM ${table.name} LIMIT 3`).all();
            console.log(`  Sample data:`, sample);
        } catch (e) {
            console.log(`  Error reading table:`, e.message);
        }
    });

    // Check if profiles have user_id column
    console.log("\n=== CHECKING PROFILE COLUMNS ===");
    const profileColumns = db.prepare("PRAGMA table_info(profiles)").all();
    const userRelatedColumns = profileColumns.filter(
        (col) => col.name.includes("user") || col.name === "user_id",
    );
    console.log("User-related columns in profiles table:");
    userRelatedColumns.forEach((col) => {
        console.log(`- ${col.name} (${col.type})`);
    });

    db.close();
} catch (error) {
    console.error("Error:", error.message);
}
