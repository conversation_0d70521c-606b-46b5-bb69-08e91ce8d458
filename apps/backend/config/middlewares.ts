export default [
    "strapi::logger",
    "strapi::errors",
    {
        name: "strapi::security",
        config: {
            contentSecurityPolicy: {
                useDefaults: true,
                directives: {
                    "connect-src": ["'self'", "https:"],
                    "img-src": ["'self'", "data:", "blob:", "https:"],
                    "media-src": ["'self'", "data:", "blob:", "https:"],
                    upgradeInsecureRequests: null,
                },
            },
        },
    },
    {
        name: "strapi::cors",
        config: {
            headers: "*",
            origin: ["http://localhost:3000", "http://localhost:3001", "https://pillarscan.fr"],
        },
    },
    "strapi::poweredBy",
    "strapi::query",
    "strapi::body",
    "strapi::session",
    "strapi::favicon",
    "strapi::public",
    // Custom middleware to format API request data
    {
        name: "global::format-request-data",
    },
];
