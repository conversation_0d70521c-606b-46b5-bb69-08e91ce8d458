import { ServerNotification } from "@/types";
import { create } from "zustand";

interface NotificationState {
    notifications: ServerNotification[];
    unreadCount: number;
    isConnected: boolean;
    addNotification: (
        notification: Pick<ServerNotification, "title" | "message" | "type" | "metadata">,
    ) => void;
    setNotifications: (notifications: ServerNotification[]) => void;
    markAsRead: (id: string) => void;
    markAllAsRead: () => void;
    removeNotification: (id: string) => void;
    clearAll: () => void;
    setConnectionStatus: (connected: boolean) => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
    notifications: [],
    unreadCount: 0,
    isConnected: false,

    addNotification: (notificationData) => {
        const notification: ServerNotification = {
            documentId: Math.random().toString(36).substr(2, 9),
            read: false,
            createdAt: new Date().toISOString(),
            category: "system",
            ...notificationData,
        };

        set((state) => ({
            notifications: [notification, ...state.notifications].slice(0, 50), // Keep only last 50
            unreadCount: state.unreadCount + 1,
        }));

        // Auto-remove success notifications after 5 seconds
        if (notification.type === "success") {
            setTimeout(() => {
                get().removeNotification(notification.documentId);
            }, 5000);
        }
    },

    setNotifications: (notifications) => {
        const unreadCount = notifications.filter((n) => !n.read).length;
        set({
            notifications: notifications.slice(0, 50), // Keep only last 50
            unreadCount,
        });
    },

    markAsRead: (id) => {
        set((state) => ({
            notifications: state.notifications.map((notification) =>
                notification.documentId === id ? { ...notification, read: true } : notification,
            ),
            unreadCount: Math.max(0, state.unreadCount - 1),
        }));
    },

    markAllAsRead: () => {
        set((state) => ({
            notifications: state.notifications.map((notification) => ({
                ...notification,
                read: true,
            })),
            unreadCount: 0,
        }));
    },

    removeNotification: (id) => {
        set((state) => {
            const notification = state.notifications.find((n) => n.documentId === id);
            const wasUnread = notification && !notification.read;

            return {
                notifications: state.notifications.filter(
                    (notification) => notification.documentId !== id,
                ),
                unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
            };
        });
    },

    clearAll: () => {
        set({
            notifications: [],
            unreadCount: 0,
        });
    },

    setConnectionStatus: (connected) => {
        set({ isConnected: connected });
    },
}));
