import { create } from "zustand";
import { Expression, ExpressionFilters, Pilier, Lieu } from "@/types";

interface ExpressionState {
    expressions: Expression[];
    currentExpression: Expression | null;
    pillars: Pilier[];
    locations: Lieu[];
    filters: ExpressionFilters;
    pagination: {
        page: number;
        pageSize: number;
        total: number;
        pageCount: number;
    };
    isLoading: boolean;
    error: string | null;
}

interface ExpressionActions {
    // Update state with server-fetched data
    setExpressions: (expressions: Expression[], pagination?: any) => void;
    setCurrentExpression: (expression: Expression | null) => void;
    setPillars: (pillars: Pilier[]) => void;
    setLocations: (locations: Lieu[]) => void;

    // Client-side state updates after server actions
    addExpression: (expression: Expression) => void;
    updateExpression: (documentId: string, expression: Expression) => void;
    removeExpression: (documentId: string) => void;

    // Filters and pagination
    setFilters: (filters: Partial<ExpressionFilters>) => void;
    clearFilters: () => void;
    setPage: (page: number) => void;

    // UI state
    clearError: () => void;
    setError: (error: string | null) => void;
    setLoading: (loading: boolean) => void;
}

type ExpressionStore = ExpressionState & ExpressionActions;

const initialFilters: ExpressionFilters = {
    page: 1,
    pageSize: 25,
};

export const useExpressionStore = create<ExpressionStore>((set, get) => ({
    // Initial state
    expressions: [],
    currentExpression: null,
    pillars: [],
    locations: [],
    filters: initialFilters,
    pagination: {
        page: 1,
        pageSize: 25,
        total: 0,
        pageCount: 0,
    },
    isLoading: false,
    error: null,

    // Actions
    setExpressions: (expressions: Expression[], pagination?: any) => {
        set({
            expressions,
            pagination: pagination || get().pagination,
        });
    },

    setCurrentExpression: (expression: Expression | null) => {
        set({ currentExpression: expression });
    },

    setPillars: (pillars: Pilier[]) => {
        set({ pillars });
    },

    setLocations: (locations: Lieu[]) => {
        set({ locations });
    },

    addExpression: (expression: Expression) => {
        set({
            expressions: [expression, ...get().expressions],
            currentExpression: expression,
        });
    },

    updateExpression: (documentId: string, expression: Expression) => {
        set({
            expressions: get().expressions.map((expr) =>
                expr.documentId === documentId ? expression : expr,
            ),
            currentExpression:
                get().currentExpression?.documentId === documentId
                    ? expression
                    : get().currentExpression,
        });
    },

    removeExpression: (documentId: string) => {
        set({
            expressions: get().expressions.filter((expr) => expr.documentId !== documentId),
            currentExpression:
                get().currentExpression?.documentId === documentId ? null : get().currentExpression,
        });
    },

    setFilters: (filters: Partial<ExpressionFilters>) => {
        set({
            filters: { ...get().filters, ...filters, page: 1 }, // Reset to page 1 when filters change
        });
    },

    clearFilters: () => {
        set({ filters: initialFilters });
    },

    setPage: (page: number) => {
        set({
            filters: { ...get().filters, page },
        });
    },

    clearError: () => {
        set({ error: null });
    },

    setError: (error: string | null) => {
        set({ error });
    },

    setLoading: (loading: boolean) => {
        set({ isLoading: loading });
    },
}));
