"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Card } from "@/components/ui/Card";

export default function LoginPage() {
    const { login, isLoading } = useAuth();
    const [error, setError] = useState<string | null>(null);

    const [formData, setFormData] = useState({
        identifier: "",
        password: "",
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        try {
            await login(formData.identifier, formData.password);
        } catch (error) {
            setError("Login failed. Please try again.");
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData((prev) => ({
            ...prev,
            [e.target.name]: e.target.value,
        }));
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Bienvenue sur PillarScan
                    </h1>
                    <p className="text-gray-600">
                        Connectez-vous pour exprimer vos préoccupations citoyennes
                    </p>
                </div>

                {/* Login Form */}
                <Card className="p-6">
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <label
                                htmlFor="identifier"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Email ou nom d'utilisateur
                            </label>
                            <Input
                                id="identifier"
                                name="identifier"
                                type="text"
                                required
                                value={formData.identifier}
                                onChange={handleChange}
                                placeholder="<EMAIL>"
                                className="w-full"
                            />
                        </div>

                        <div>
                            <label
                                htmlFor="password"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Mot de passe
                            </label>
                            <Input
                                id="password"
                                name="password"
                                type="password"
                                required
                                value={formData.password}
                                onChange={handleChange}
                                placeholder="••••••••"
                                className="w-full"
                                autoComplete="off"
                            />
                        </div>

                        {error && (
                            <div className="bg-red-50 border border-red-200 rounded-md p-3">
                                <p className="text-sm text-red-600">{error}</p>
                            </div>
                        )}

                        <Button
                            type="submit"
                            loading={isLoading}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            Se connecter
                        </Button>
                    </form>

                    <div className="mt-6 text-center">
                        <Link
                            href="/auth/forgot-password"
                            className="text-sm text-blue-600 hover:text-blue-500"
                        >
                            Mot de passe oublié ?
                        </Link>
                    </div>
                </Card>

                {/* Register Link */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Pas encore de compte ?{" "}
                        <Link
                            href="/auth/register"
                            className="font-medium text-blue-600 hover:text-blue-500"
                        >
                            Créer un compte
                        </Link>
                    </p>
                </div>

                {/* Features Preview */}
                <div className="mt-8 bg-white rounded-lg p-4 shadow-sm">
                    <h3 className="font-medium text-gray-900 mb-3">
                        Avec PillarScan, vous pouvez :
                    </h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                        <li className="flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                            Exprimer vos préoccupations sur les 12 piliers de la société
                        </li>
                        <li className="flex items-center">
                            <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                            Suivre le traitement de vos expressions
                        </li>
                        <li className="flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                            Voir l'impact réel de vos contributions
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    );
}
