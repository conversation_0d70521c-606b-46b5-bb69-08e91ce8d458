@import "tailwindcss";

/* French Civic Design System - PillarScan */
:root {
    /* French Republic Colors */
    --fr-blue: #000091; /* <PERSON> */
    --fr-white: #ffffff; /* Pure White */
    --fr-red: #e1000f; /* <PERSON> Red */

    /* Extended Civic Palette */
    --civic-navy: #1e293b; /* Deep Navy */
    --civic-slate: #334155; /* Slate */
    --civic-gray: #64748b; /* Neutral Gray */
    --civic-light: #f8fafc; /* Light Background */
    --civic-success: #059669; /* Success Green */
    --civic-warning: #d97706; /* Warning Orange */
    --civic-info: #0284c7; /* Info Blue */

    /* Semantic Design Tokens */
    --background: var(--fr-white);
    --foreground: var(--civic-navy);
    --primary: var(--fr-blue);
    --primary-foreground: var(--fr-white);
    --secondary: var(--civic-light);
    --secondary-foreground: var(--civic-navy);
    --muted: #f1f5f9;
    --muted-foreground: var(--civic-gray);
    --accent: var(--civic-light);
    --accent-foreground: var(--civic-navy);
    --destructive: var(--fr-red);
    --destructive-foreground: var(--fr-white);
    --success: var(--civic-success);
    --success-foreground: var(--fr-white);
    --warning: var(--civic-warning);
    --warning-foreground: var(--fr-white);
    --info: var(--civic-info);
    --info-foreground: var(--fr-white);
    --border: #e2e8f0;
    --input: #e2e8f0;
    --ring: var(--fr-blue);
    --radius: 0.75rem;
}

/* Tailwind v4 Theme Configuration */
@theme {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-success: var(--success);
    --color-success-foreground: var(--success-foreground);
    --color-warning: var(--warning);
    --color-warning-foreground: var(--warning-foreground);
    --color-info: var(--info);
    --color-info-foreground: var(--info-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --radius: var(--radius);
}

/* Tailwind CSS integration */
@layer base {
    * {
        border-color: var(--border);
    }

    body {
        @apply bg-background text-foreground;
        font-feature-settings:
            "rlig" 1,
            "calt" 1;
    }
}

/* French Typography System */
body {
    font-family:
        "Inter",
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Roboto,
        "Helvetica Neue",
        Arial,
        sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
}

.font-marianne {
    font-family: "Marianne", "Inter", system-ui, sans-serif;
}

/* French Civic Component Styles */
.civic-gradient {
    background: linear-gradient(
        135deg,
        var(--fr-blue) 0%,
        var(--civic-info) 50%,
        var(--civic-success) 100%
    );
}

.civic-card {
    @apply bg-background border rounded-xl shadow-sm transition-all duration-200;
    border-color: var(--border);
}

.civic-card:hover {
    @apply shadow-lg border-primary/20;
}

.civic-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
}

.civic-badge-primary {
    @apply bg-primary/10 text-primary border border-primary/20;
}

.civic-badge-success {
    background-color: rgb(var(--civic-success) / 0.1);
    color: var(--civic-success);
    border: 1px solid rgb(var(--civic-success) / 0.2);
}

.civic-badge-warning {
    background-color: rgb(var(--civic-warning) / 0.1);
    color: var(--civic-warning);
    border: 1px solid rgb(var(--civic-warning) / 0.2);
}

.civic-badge-info {
    background-color: rgb(var(--civic-info) / 0.1);
    color: var(--civic-info);
    border: 1px solid rgb(var(--civic-info) / 0.2);
}

/* French Pillar Colors */
.pillar-sante {
    background-color: #e74c3c;
}
.pillar-education {
    background-color: #3498db;
}
.pillar-transport {
    background-color: #9b59b6;
}
.pillar-logement {
    background-color: #1abc9c;
}
.pillar-emploi {
    background-color: #f39c12;
}
.pillar-securite {
    background-color: #e67e22;
}
.pillar-environnement {
    background-color: #27ae60;
}
.pillar-justice {
    background-color: #34495e;
}
.pillar-pouvoir-achat {
    background-color: #95a5a6;
}
.pillar-vie-sociale {
    background-color: #ff6b6b;
}
.pillar-democratie {
    background-color: #4ecdc4;
}
.pillar-culture {
    background-color: #ffe66d;
}

/* Accessibility Enhancements */
.focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* French UI Animations */
@keyframes civicFadeIn {
    from {
        opacity: 0;
        transform: translateY(8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes civicSlideIn {
    from {
        opacity: 0;
        transform: translateX(-16px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes civicScaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-civic-fade-in {
    animation: civicFadeIn 0.4s ease-out;
}

.animate-civic-slide-in {
    animation: civicSlideIn 0.3s ease-out;
}

.animate-civic-scale-in {
    animation: civicScaleIn 0.2s ease-out;
}

/* Responsive French Layout */
.civic-container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
}

.civic-section {
    @apply py-12 sm:py-16 lg:py-20;
}

/* Interactive States */
.civic-interactive {
    @apply transition-all duration-200 ease-out;
}

.civic-interactive:hover {
    @apply transform scale-[1.02] shadow-lg;
}

.civic-interactive:active {
    @apply transform scale-[0.98];
}

/* Modern Glass Morphism Effects */
.glass-morphism {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-morphism-dark {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Professional Gradient Backgrounds */
.gradient-primary {
    background: linear-gradient(135deg, var(--fr-blue) 0%, var(--civic-info) 100%);
}

.gradient-success {
    background: linear-gradient(135deg, var(--civic-success) 0%, #10b981 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, var(--civic-warning) 0%, #f59e0b 100%);
}

.gradient-mesh {
    background-color: #ff6b6b;
    background-image:
        radial-gradient(at 40% 20%, var(--fr-blue) 0px, transparent 50%),
        radial-gradient(at 80% 0%, var(--civic-info) 0px, transparent 50%),
        radial-gradient(at 0% 50%, var(--civic-success) 0px, transparent 50%);
}

/* Modern Input Styles */
.input-modern {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-modern:focus {
    background: rgba(255, 255, 255, 1);
    border-color: var(--fr-blue);
    box-shadow: 0 0 0 4px rgba(0, 0, 145, 0.1);
}

/* Professional Button Animations */
.btn-professional {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-professional::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%);
    transition:
        width 0.6s,
        height 0.6s;
}

.btn-professional:hover::before {
    width: 300px;
    height: 300px;
}

/* Smooth Slide Animation */
@keyframes slideInfinite {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(300%);
    }
}

.animate-slide-infinite {
    animation: slideInfinite 1.5s ease-in-out infinite;
}

/* Professional Card Hover Effects */
.card-professional {
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-professional::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(0, 0, 145, 0.05));
    opacity: 0;
    transition: opacity 0.4s;
    pointer-events: none;
}

.card-professional:hover::before {
    opacity: 1;
}

.card-professional:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Smooth Scroll */
html {
    scroll-behavior: smooth;
}

/* Professional Loading Animation */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.animate-shimmer {
    background: linear-gradient(90deg, #f0f0f0 0px, #f8f8f8 40px, #f0f0f0 80px);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite;
}

/* Civic Section Styles */
.civic-section-compact {
    @apply py-6 sm:py-8;
}

/* Focus Styles */
.focus-ring {
    @apply focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary;
}

/* Animation Delays */
.animation-delay-100 {
    animation-delay: 100ms;
}
.animation-delay-200 {
    animation-delay: 200ms;
}
.animation-delay-300 {
    animation-delay: 300ms;
}
.animation-delay-400 {
    animation-delay: 400ms;
}

/* Float Animation */
@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes floatDelayed {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-30px);
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
    animation: floatDelayed 8s ease-in-out infinite;
    animation-delay: 2s;
}

/* Scroll Indicator Animation */
@keyframes scroll {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(10px);
        opacity: 0;
    }
}

.animate-scroll {
    animation: scroll 1.5s ease-in-out infinite;
}

/* French Government Accessibility Standards */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
