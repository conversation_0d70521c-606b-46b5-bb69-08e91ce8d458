"use client";

import React from "react";
import { useLayoutConfig } from "@/components/providers/layout-provider";
import { Pilier, SousPilier, Expression } from "@/types";

interface PilierDetailClientProps {
    pilier: Pilier;
    sousPiliers: SousPilier[];
    expressions: Expression[];
    children: React.ReactNode;
}

export default function PilierDetailClient({ 
    pilier, 
    sousPiliers, 
    expressions, 
    children 
}: PilierDetailClientProps) {
    // Configure layout to hide header for pilier detail pages
    useLayoutConfig({
        showHeader: false,
        showFooter: true,
    });

    return <>{children}</>;
}
