"use client";

import React from "react";

import { <PERSON><PERSON>, SousPilier, Expression } from "@/types";

interface PilierDetailClientProps {
    pilier: Pilier;
    sousPiliers: SousPilier[];
    expressions: Expression[];
    children: React.ReactNode;
}

export default function PilierDetailClient({
    pilier,
    sousPiliers,
    expressions,
    children,
}: PilierDetailClientProps) {
    // Hide header for pilier detail pages (footer is shown by default)
    useHideHeader();

    return <>{children}</>;
}
