import Link from "next/link";
import { notFound } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    fetchPilierById,
    fetchSousPiliers,
    fetchPublicExpressions,
    handleServerError,
} from "@/lib/server-api";
import { Pilier, SousPilier, Expression } from "@/types";
import { MapPinIcon, ChartBarIcon, ArrowLeftIcon, CalendarIcon } from "@heroicons/react/24/outline";

import Logo from "@/components/ui/Logo";
import PilierDetailClient from "./pilier-detail-client";

// Dynamic route with SSR
export const revalidate = 1800; // Revalidate every 30 minutes

const pillarIcons: Record<string, string> = {
    SANTE: "🏥",
    EDUCATION: "🎓",
    TRANSPORT: "🚇",
    LOGEMENT: "🏘️",
    EMPLOI: "💼",
    SECURITE: "🛡️",
    ENVIRONNEMENT: "🌳",
    JUSTICE: "⚖️",
    POUVOIR_ACHAT: "💰",
    VIE_SOCIALE: "🤝",
    DEMOCRATIE: "🏛️",
    CULTURE: "🎭",
};

interface PilierDetailPageProps {
    params: Promise<{ id: string }>;
}

export default async function PilierDetailPage({ params }: PilierDetailPageProps) {
    const { id: pilierId } = await params;

    let pilier: Pilier | null = null;
    let expressions: Expression[] = [];
    let sousPiliers: SousPilier[] = [];

    try {
        // Fetch all data in parallel
        const [pilierData, expressionsResponse, sousPiliersData] = await Promise.all([
            fetchPilierById(pilierId),
            fetchPublicExpressions({ pilier: pilierId, pageSize: 10 }),
            fetchSousPiliers(pilierId),
        ]);

        pilier = pilierData;
        expressions = expressionsResponse.data || [];
        sousPiliers = sousPiliersData;
    } catch (error) {
        handleServerError(error);
        // If pilier not found, show 404
        notFound();
    }

    if (!pilier) {
        notFound();
    }

    // Utility functions for displaying data

    const getTypeLabel = (type: string) => {
        const labels = {
            probleme: "Problème",
            satisfaction: "Satisfaction",
            idee: "Idée",
            question: "Question",
        };
        return labels[type as keyof typeof labels] || type;
    };

    const getTypeColor = (type: string) => {
        const colors = {
            probleme: "bg-red-100 text-red-800",
            satisfaction: "bg-green-100 text-green-800",
            idee: "bg-blue-100 text-blue-800",
            question: "bg-yellow-100 text-yellow-800",
        };
        return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
    };

    const getUrgenceColor = (urgence: number) => {
        if (urgence >= 4) return "bg-red-100 text-red-800";
        if (urgence >= 3) return "bg-yellow-100 text-yellow-800";
        return "bg-green-100 text-green-800";
    };

    const getUrgenceLabel = (urgence: number) => {
        const labels = {
            1: "Très faible",
            2: "Faible",
            3: "Modérée",
            4: "Élevée",
            5: "Critique",
        };
        return labels[urgence as keyof typeof labels] || "Inconnue";
    };

    // Use real statistics from pilier or fallback to mock data
    const pilierStats = {
        totalExpressions:
            pilier.statistiques?.nb_expressions_total || Math.floor(Math.random() * 1000) + 200,
        activeExpressions:
            pilier.statistiques?.nb_expressions_mois || Math.floor(Math.random() * 200) + 50,
        resolvedExpressions: Math.floor(
            (pilier.statistiques?.taux_resolution || 0.7) *
                (pilier.statistiques?.nb_expressions_total || 300),
        ),
        avgResolutionTime:
            pilier.statistiques?.temps_moyen_resolution || Math.floor(Math.random() * 30) + 10,
        topCities: ["Paris", "Lyon", "Marseille", "Toulouse", "Nice"],
        monthlyTrend: Math.floor(Math.random() * 40) - 20, // -20% to +20%
        participationRate: Math.floor(Math.random() * 30) + 60, // 60% to 90%
    };

    return (
        <PilierDetailClient
            pilier={pilier}
            sousPiliers={sousPiliers}
            expressions={expressions}
        >
            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        <div className="flex items-center">
                            <Button
                                variant="outline"
                                size="sm"
                                className="mr-4"
                                as={Link}
                                href="/piliers"
                                icon={<ArrowLeftIcon className="h-4 w-4" />}
                            >
                                Retour
                            </Button>

                            <Logo href="/" showText={false} />

                            <span className="ml-4 text-gray-500">Pilier {pilier.nom}</span>
                        </div>

                        <Button
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                            as={Link}
                            href={`/expressions/new?pilier=${pilier.documentId}`}
                        >
                            Nouvelle expression
                        </Button>
                    </div>
                </div>
            </header>

            {/* Hero Section */}
            <section
                className="py-16 text-white relative overflow-hidden"
                style={{
                    background: `linear-gradient(135deg, ${pilier.couleur} 0%, ${pilier.couleur}dd 100%)`,
                }}
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <div className="mb-6">
                            <div className="w-20 h-20 rounded-full flex items-center justify-center text-4xl mx-auto mb-4 bg-white/20">
                                {pillarIcons[pilier.code] || "📊"}
                            </div>
                            <Badge
                                variant="secondary"
                                className="bg-white/20 text-white border-white/30"
                            >
                                {pilier.code}
                            </Badge>
                        </div>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6">{pilier.nom}</h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90 max-w-3xl mx-auto">
                            {pilier.description ||
                                `Explorez toutes les expressions citoyennes liées au domaine ${pilier.nom.toLowerCase()}.`}
                        </p>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {pilierStats.totalExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Expressions totales</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {pilierStats.activeExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">En traitement</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {pilierStats.resolvedExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Résolues</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {pilierStats.avgResolutionTime}j
                                </div>
                                <div className="text-sm opacity-80">Délai moyen</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Statistics */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* Trends Card */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <ChartBarIcon className="w-5 h-5 mr-2 text-blue-600" />
                                    Tendances
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">
                                        Évolution mensuelle
                                    </span>
                                    <span
                                        className={`text-sm font-medium ${
                                            pilierStats.monthlyTrend >= 0
                                                ? "text-green-600"
                                                : "text-red-600"
                                        }`}
                                    >
                                        {pilierStats.monthlyTrend >= 0 ? "+" : ""}
                                        {pilierStats.monthlyTrend}%
                                    </span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">
                                        Taux de participation
                                    </span>
                                    <span className="text-sm font-medium text-blue-600">
                                        {pilierStats.participationRate}%
                                    </span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">
                                        Délai de résolution
                                    </span>
                                    <span className="text-sm font-medium text-gray-900">
                                        {pilierStats.avgResolutionTime} jours
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Top Cities */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <MapPinIcon className="w-5 h-5 mr-2 text-blue-600" />
                                    Villes les plus actives
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {pilierStats.topCities.slice(0, 5).map((city, index) => (
                                        <div
                                            key={city}
                                            className="flex justify-between items-center"
                                        >
                                            <span className="text-sm">
                                                {index + 1}. {city}
                                            </span>
                                            <span className="text-sm text-gray-500">
                                                {Math.floor(Math.random() * 100) + 20} expressions
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Action Buttons */}
                        <div className="space-y-3">
                            <Button
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                                as={Link}
                                href={`/expressions/new?pilier=${pilier.documentId}`}
                            >
                                Créer une expression
                            </Button>
                            <Button
                                variant="outline"
                                className="w-full"
                                as={Link}
                                href={`/expressions?pilier=${pilier.documentId}`}
                            >
                                Voir toutes les expressions
                            </Button>
                        </div>
                    </div>

                    {/* Right Column - Recent Expressions */}
                    <div className="lg:col-span-2">
                        <div className="mb-6">
                            <h2 className="text-2xl font-bold text-gray-900 mb-2">
                                Expressions récentes
                            </h2>
                            <p className="text-gray-600">
                                Les dernières préoccupations et idées des citoyens sur ce pilier
                            </p>
                        </div>

                        {expressions.length === 0 ? (
                            <Card className="p-8 text-center">
                                <p className="text-gray-500 mb-4">
                                    Aucune expression trouvée pour ce pilier.
                                </p>

                                <Button
                                    className="bg-blue-600 hover:bg-blue-700 text-white"
                                    as={Link}
                                    href={`/expressions/new?pilier=${pilier.documentId}`}
                                >
                                    Créer la première expression
                                </Button>
                            </Card>
                        ) : (
                            <div className="space-y-6">
                                {expressions.map((expression) => (
                                    <Card
                                        key={expression.documentId}
                                        className="hover:shadow-lg transition-shadow"
                                    >
                                        <CardContent className="p-6">
                                            <div className="flex justify-between items-start mb-4">
                                                <div className="flex-1">
                                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                                        {expression.titre}
                                                    </h3>
                                                    <p className="text-gray-600 mb-4 line-clamp-3">
                                                        {expression.contenu}
                                                    </p>
                                                </div>
                                            </div>

                                            {/* Badges */}
                                            <div className="flex flex-wrap gap-2 mb-4">
                                                <Badge
                                                    className={getTypeColor(
                                                        expression.type_expression,
                                                    )}
                                                >
                                                    {getTypeLabel(expression.type_expression)}
                                                </Badge>
                                                <Badge
                                                    className={getUrgenceColor(expression.urgence)}
                                                >
                                                    Urgence: {getUrgenceLabel(expression.urgence)}
                                                </Badge>
                                            </div>

                                            {/* Meta Information */}
                                            <div className="flex justify-between items-center text-sm text-gray-500">
                                                <div className="flex items-center space-x-4">
                                                    <span>
                                                        📍{" "}
                                                        {typeof expression.lieu === "string"
                                                            ? expression.lieu
                                                            : expression.lieu?.nom || "Non défini"}
                                                    </span>
                                                    <span>
                                                        👤{" "}
                                                        {typeof expression.auteur === "string"
                                                            ? expression.auteur
                                                            : expression.auteur?.nom || "Anonyme"}
                                                    </span>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <CalendarIcon className="w-4 h-4" />
                                                    <span>
                                                        {new Date(
                                                            expression.date_creation,
                                                        ).toLocaleDateString("fr-FR")}
                                                    </span>
                                                </div>
                                            </div>

                                            {/* View Details Button */}
                                            <div className="mt-4 pt-4 border-t border-gray-200">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    as={Link}
                                                    href={`/expressions/${expression.documentId}`}
                                                >
                                                    Voir les détails
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}

                                {/* View More Button */}
                                {expressions.length >= 10 && (
                                    <div className="text-center pt-6">
                                        <Button
                                            variant="outline"
                                            as={Link}
                                            href={`/expressions?pilier=${pilier.documentId}`}
                                        >
                                            Voir toutes les expressions de ce pilier
                                        </Button>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </main>
        </PilierDetailClient>
    );
}
