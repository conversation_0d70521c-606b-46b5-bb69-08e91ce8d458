import Link from "next/link";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { fetchPiliers } from "@/lib/server-api";
import { Pilier } from "@/types";
import PilierCard from "./PilierCard";


// Static generation - this page will be pre-rendered at build time
export const revalidate = 3600; // Revalidate every hour

export default async function PiliersPage() {
    // Fetch piliers on the server
    let piliers: Pilier[] = [];

    try {
        piliers = await fetchPiliers();
    } catch (error) {
        console.error("Erreur lors du chargement des piliers:", error);
        // piliers remains empty array - will show error state
    }

    const mockStats = {
        totalExpressions: 12547,
        activeExpressions: 3421,
        resolvedThisMonth: 1834,
        participatingCities: 2847,
    };

    // No loading state needed - server-rendered

    return (
        <>
            {/* Header */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Les 12 Piliers de la Société Française
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            Tous les domaines de votre vie citoyenne
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Explorez les 12 piliers qui structurent notre société et découvrez
                            comment votre voix peut transformer chaque aspect de votre quotidien.
                        </p>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.totalExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Expressions totales</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.activeExpressions.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">En traitement</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.resolvedThisMonth.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Résolues ce mois</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">
                                    {mockStats.participatingCities.toLocaleString()}
                                </div>
                                <div className="text-sm opacity-80">Communes participantes</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Piliers Grid */}
            <section className="civic-section">
                <div className="civic-container">
                    {piliers.length === 0 ? (
                        <div className="text-center py-16">
                            <p className="text-xl text-muted-foreground mb-8">
                                Aucun pilier trouvé. Les données seront disponibles une fois le
                                backend démarré.
                            </p>

                            <Button variant="primary" size="lg" as={Link} href="/app/my-expressions/new">
                                Créer une expression
                            </Button>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {piliers.map((pilier, index) => (
                                <PilierCard key={pilier.documentId} pilier={pilier} index={index} />
                            ))}
                        </div>
                    )}
                </div>
            </section>

            {/* Call to Action */}
            <section className="civic-section bg-background border-t">
                <div className="civic-container text-center">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                        Votre voix compte sur chaque pilier
                    </h2>
                    <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                        Quel que soit le domaine qui vous préoccupe, PillarScan vous donne les
                        outils pour transformer vos frustrations en améliorations concrètes.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary" href="/app/my-expressions/new" as={Link}>
                            Soumettre une expression
                        </Button>

                        <Button size="lg" variant="outline" href="/expressions" as={Link}>
                            Voir toutes les expressions
                        </Button>
                    </div>
                </div>
            </section>
        </>
    );
}
