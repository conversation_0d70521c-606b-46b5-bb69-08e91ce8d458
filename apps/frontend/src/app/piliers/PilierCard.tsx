"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { Pilier } from "@/types";
import { PilierIcon } from "@/components/icons/PilierIcon";
import {
    ChartBarSquareIcon,
    MapPinIcon,
    UsersIcon,
    ChartBarIcon,
} from "@heroicons/react/24/outline";

interface PilierCardProps {
    pilier: Pilier;
    index: number;
}

export default function PilierCard({ pilier, index }: PilierCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
        <Card
            hover
            interactive
            className="group cursor-pointer animate-civic-fade-in"
            style={{ animationDelay: `${index * 100}ms` }}
            onClick={() => setIsExpanded(!isExpanded)}
        >
            <CardHeader>
                <div className="flex items-center mb-4">
                    <div
                        className="w-12 h-12 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform"
                        style={{ backgroundColor: pilier.couleur + "20" }}
                    >
                        <PilierIcon
                            iconName={pilier.icone}
                            className="h-6 w-6"
                            style={{ color: pilier.couleur }}
                        />
                    </div>
                    <div>
                        <CardTitle className="text-lg group-hover:text-primary transition-colors">
                            {pilier.nom}
                        </CardTitle>
                        <Badge variant="outline" size="sm" className="mt-1">
                            {pilier.code}
                        </Badge>
                    </div>
                </div>
            </CardHeader>

            <CardContent>
                <CardDescription className="mb-4 leading-relaxed">
                    {pilier.description ||
                        `Explorez toutes les expressions citoyennes liées au domaine ${pilier.nom.toLowerCase()}.`}
                </CardDescription>

                {/* Statistics from pilier.statistiques or mock data */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-secondary/50 rounded-lg">
                        <div className="text-lg font-bold text-primary">
                            {pilier.statistiques?.nb_expressions_total || 0}
                        </div>
                        <div className="text-xs text-muted-foreground">Expressions</div>
                    </div>
                    <div className="text-center p-3 bg-secondary/50 rounded-lg">
                        <div className="text-lg font-bold text-success">
                            {pilier.statistiques?.taux_resolution || 0}%
                        </div>
                        <div className="text-xs text-muted-foreground">Résolues</div>
                    </div>
                </div>

                <div className="flex gap-2">
                    <Button
                        variant="primary"
                        size="sm"
                        className="flex-1"
                        icon={<ChartBarSquareIcon className="h-4 w-4" />}
                        href={`/piliers/${pilier.documentId}`}
                        as={Link}
                        onClick={(e) => e.stopPropagation()}
                    >
                        Explorer
                    </Button>

                    <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        icon={<MapPinIcon className="h-4 w-4" />}
                        onClick={(e) => e.stopPropagation()}
                        as={Link}
                        href={`/expressions/new?pilier=${pilier.documentId}`}
                    >
                        Exprimer
                    </Button>
                </div>

                {/* Expanded Content */}
                {isExpanded && (
                    <div className="mt-6 pt-6 border-t border-gray-200 animate-civic-fade-in">
                        <h4 className="font-semibold mb-3 text-foreground">
                            Sous-catégories populaires :
                        </h4>
                        <div className="flex flex-wrap gap-2 mb-4">
                            {pilier.sous_piliers?.slice(0, 4).map((sousPilier) => (
                                <Badge key={sousPilier.documentId} variant="secondary" size="sm">
                                    {sousPilier.nom}
                                </Badge>
                            )) ||
                                ["Qualité", "Accessibilité", "Équité", "Innovation"].map((tag) => (
                                    <Badge key={tag} variant="secondary" size="sm">
                                        {tag}
                                    </Badge>
                                ))}
                        </div>

                        <div className="space-y-2">
                            <div className="flex items-center text-sm text-muted-foreground">
                                <ChartBarIcon className="h-4 w-4 mr-2" />
                                Tendance : +{pilier.statistiques?.nb_expressions_mois || 0}% ce mois
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                                <UsersIcon className="h-4 w-4 mr-2" />
                                {10000}+ citoyens concernés
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
