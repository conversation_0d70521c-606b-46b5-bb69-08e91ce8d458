import React from "react";
import Link from "next/link";

import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    QuestionMarkCircleIcon,
    ChatBubbleBottomCenterTextIcon,
    UserIcon,
    CogIcon,
    ShieldCheckIcon,
    ExclamationTriangleIcon,
    ChevronRightIcon,
    MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";

export default function HelpPage() {
    const faqCategories = [
        {
            title: "Premiers pas",
            icon: <UserIcon className="h-6 w-6" />,
            questions: [
                {
                    q: "Comment créer mon compte PillarScan ?",
                    a: "Cliquez sur 'S'inscrire' en haut à droite, renseignez votre email, créez un mot de passe et confirmez votre commune de résidence. Vous recevrez un email de confirmation.",
                },
                {
                    q: "Qui peut utiliser PillarScan ?",
                    a: "Tous les résidents français majeurs (18 ans et plus) peuvent créer un compte et exprimer leurs préoccupations citoyennes.",
                },
                {
                    q: "PillarScan est-il vraiment gratuit ?",
                    a: "Oui, PillarScan est entièrement gratuit pour tous les citoyens français. Nous ne vendons jamais vos données personnelles.",
                },
            ],
        },
        {
            title: "Expressions citoyennes",
            icon: <ChatBubbleBottomCenterTextIcon className="h-6 w-6" />,
            questions: [
                {
                    q: "Comment rédiger une bonne expression ?",
                    a: "Soyez factuel, précis et constructif. Indiquez le lieu exact, la date, et décrivez concrètement le problème. Ajoutez des photos si possible.",
                },
                {
                    q: "Quels sont les 12 piliers disponibles ?",
                    a: "Santé, Éducation, Transport, Logement, Emploi, Sécurité, Environnement, Justice, Pouvoir d'achat, Vie sociale, Démocratie, Culture.",
                },
                {
                    q: "Combien de temps pour voir une action ?",
                    a: "En moyenne 4-7 jours pour une première réponse, selon l'urgence et la complexité. Vous êtes notifié à chaque étape.",
                },
            ],
        },
        {
            title: "Compte et paramètres",
            icon: <CogIcon className="h-6 w-6" />,
            questions: [
                {
                    q: "Comment modifier mes informations personnelles ?",
                    a: "Allez dans 'Mon profil' depuis le menu utilisateur. Vous pouvez modifier votre nom, email et commune de résidence.",
                },
                {
                    q: "Comment supprimer mon compte ?",
                    a: "Dans 'Mon profil', section 'Paramètres avancés', cliquez sur 'Supprimer mon compte'. Cette action est irréversible.",
                },
                {
                    q: "J'ai oublié mon mot de passe",
                    a: "Cliquez sur 'Mot de passe oublié' sur la page de connexion. Vous recevrez un email pour le réinitialiser.",
                },
            ],
        },
        {
            title: "Confidentialité et sécurité",
            icon: <ShieldCheckIcon className="h-6 w-6" />,
            questions: [
                {
                    q: "Mes données sont-elles protégées ?",
                    a: "Oui, nous respectons le RGPD. Vos données sont chiffrées, stockées en France, et ne sont jamais vendues à des tiers.",
                },
                {
                    q: "Qui peut voir mes expressions ?",
                    a: "Le contenu de vos expressions peut être rendu public (sans votre nom) pour la transparence. Votre identité reste confidentielle sauf accord explicite.",
                },
                {
                    q: "Comment signaler un contenu inapproprié ?",
                    a: "Cliquez sur le bouton 'Signaler' sous l'expression concernée. Notre équipe de modération examine tous les signalements sous 24h.",
                },
            ],
        },
    ];

    const quickActions = [
        {
            title: "Créer une expression",
            description: "Partagez votre préoccupation citoyenne",
            href: "/app/my-expressions/new",
            icon: <ChatBubbleBottomCenterTextIcon className="h-8 w-8" />,
            color: "bg-primary",
        },
        {
            title: "Explorer les piliers",
            description: "Découvrez les 12 domaines de la société",
            href: "/piliers",
            icon: <MagnifyingGlassIcon className="h-8 w-8" />,
            color: "bg-info",
        },
        {
            title: "Voir les expressions",
            description: "Consultez les préoccupations des autres citoyens",
            href: "/expressions",
            icon: <QuestionMarkCircleIcon className="h-8 w-8" />,
            color: "bg-success",
        },
        {
            title: "Contacter le support",
            description: "Besoin d'aide personnalisée ?",
            href: "/contact",
            icon: <ExclamationTriangleIcon className="h-8 w-8" />,
            color: "bg-warning",
        },
    ];

    return (
        <>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Centre d'aide
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            Comment pouvons-nous vous aider ?
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Trouvez rapidement les réponses à vos questions sur l'utilisation de
                            PillarScan et la démocratie participative.
                        </p>
                        <div className="max-w-2xl mx-auto">
                            <div className="relative">
                                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Rechercher dans l'aide..."
                                    className="w-full pl-12 pr-4 py-4 rounded-xl text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-white/50"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Quick Actions */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Actions rapides
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Les actions les plus courantes pour bien commencer avec PillarScan
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {quickActions.map((action, index) => (
                            <Link key={index} href={action.href} className="block">
                                <Card
                                    hover
                                    interactive
                                    className="p-6 text-center group cursor-pointer h-full"
                                >
                                    <div
                                        className={`w-16 h-16 ${action.color} rounded-xl mx-auto mb-4 flex items-center justify-center text-white group-hover:scale-110 transition-transform`}
                                    >
                                        {action.icon}
                                    </div>
                                    <h3 className="text-lg font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                                        {action.title}
                                    </h3>
                                    <p className="text-sm text-muted-foreground">
                                        {action.description}
                                    </p>
                                </Card>
                            </Link>
                        ))}
                    </div>
                </div>
            </section>

            {/* FAQ */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="info" size="lg" className="mb-4">
                            Questions fréquentes
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Foire aux questions
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Les réponses aux questions les plus posées par la communauté PillarScan
                        </p>
                    </div>

                    <div className="space-y-12">
                        {faqCategories.map((category, categoryIndex) => (
                            <div key={categoryIndex}>
                                <div className="flex items-center mb-6">
                                    <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center text-white mr-4">
                                        {category.icon}
                                    </div>
                                    <h3 className="text-2xl font-bold text-foreground">
                                        {category.title}
                                    </h3>
                                </div>
                                <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
                                    {category.questions.map((faq, faqIndex) => (
                                        <Card key={faqIndex} variant="elevated" className="p-6">
                                            <div className="flex items-start">
                                                <div className="flex-1">
                                                    <h4 className="text-lg font-semibold text-foreground mb-3">
                                                        {faq.q}
                                                    </h4>
                                                    <p className="text-muted-foreground leading-relaxed">
                                                        {faq.a}
                                                    </p>
                                                </div>
                                                <ChevronRightIcon className="h-5 w-5 text-muted-foreground ml-4 flex-shrink-0" />
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Guides */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="success" size="lg" className="mb-4">
                            Guides détaillés
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Approfondissez vos connaissances
                        </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <Card hover variant="elevated" className="p-8">
                            <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white mb-4">
                                📚
                            </div>
                            <h3 className="text-xl font-bold text-foreground mb-4">
                                Guide du débutant
                            </h3>
                            <p className="text-muted-foreground mb-6">
                                Tout ce qu'il faut savoir pour bien commencer avec PillarScan et
                                maximiser l'impact de vos expressions.
                            </p>
                            <Button variant="outline" size="sm" href="/how-it-works" as={Link}>
                                Lire le guide
                            </Button>
                        </Card>

                        <Card hover variant="elevated" className="p-8">
                            <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center text-white mb-4">
                                ✍️
                            </div>
                            <h3 className="text-xl font-bold text-foreground mb-4">
                                Rédiger efficacement
                            </h3>
                            <p className="text-muted-foreground mb-6">
                                Conseils pour rédiger des expressions claires, constructives et qui
                                obtiennent des résultats rapides.
                            </p>
                            <Button variant="outline" size="sm">
                                Voir les conseils
                            </Button>
                        </Card>

                        <Card hover variant="elevated" className="p-8">
                            <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white mb-4">
                                🏛️
                            </div>
                            <h3 className="text-xl font-bold text-foreground mb-4">
                                Comprendre les piliers
                            </h3>
                            <p className="text-muted-foreground mb-6">
                                Découvrez en détail les 12 piliers de la société française et
                                comment bien classifier vos préoccupations.
                            </p>
                            <Button variant="outline" size="sm" href="/piliers" as={Link}>
                                Explorer les piliers
                            </Button>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Contact Support */}
            <section className="civic-section civic-gradient text-white">
                <div className="civic-container text-center">
                    <QuestionMarkCircleIcon className="h-16 w-16 mx-auto mb-6 opacity-80" />
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Vous ne trouvez pas votre réponse ?
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                        Notre équipe support est là pour vous aider. Nous répondons généralement
                        sous 24h.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary" href="/contact" as={Link}>
                            Contacter le support
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            href="/report"
                            as={Link}
                            className="border-white text-white hover:bg-white hover:text-primary"
                        >
                            Signaler un problème
                        </Button>
                    </div>
                    <div className="mt-8 text-sm opacity-75">
                        <p>
                            Temps de réponse moyen : <strong>18 heures</strong> • Satisfaction :{" "}
                            <strong>94%</strong>
                        </p>
                    </div>
                </div>
            </section>
        </>
    );
}
