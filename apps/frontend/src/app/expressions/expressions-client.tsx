"use client";

import React, { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import SearchBar from "@/components/search/SearchBar";
import AdvancedFilters, { FilterOptions } from "@/components/filters/AdvancedFilters";
import Link from "next/link";
import { Expression, Pilier, ExpressionFilters } from "@/types";

interface ExpressionsClientProps {
    initialExpressions: Expression[];
    piliers: Pilier[];
    initialFilters: ExpressionFilters;
}

export default function ExpressionsClient({
    initialExpressions,
    piliers,
    initialFilters,
}: ExpressionsClientProps) {
    const router = useRouter();
    const [expressions] = useState<Expression[]>(initialExpressions);
    const [isLoading] = useState(false);
    const [showFilters, setShowFilters] = useState(false);

    const handleSearch = useCallback(
        (query: string, filters?: FilterOptions) => {
            // Build new URL with search params
            const params = new URLSearchParams();

            if (query) params.set("q", query);
            if (filters?.typeExpression?.length)
                params.set("types", filters.typeExpression.join(","));
            if (filters?.urgence?.length) params.set("urgence", filters.urgence[0].toString());
            if (filters?.statut?.length) params.set("statut", filters.statut[0]);
            if (initialFilters.pilier) params.set("pilier", initialFilters.pilier);

            router.push(`/expressions?${params.toString()}`);
        },
        [router, initialFilters.pilier],
    );

    const getTypeLabel = (type: string) => {
        const labels = {
            probleme: "Problème",
            satisfaction: "Satisfaction",
            idee: "Idée",
            question: "Question",
        };
        return labels[type as keyof typeof labels] || type;
    };

    const getTypeVariant = (type: string): "destructive" | "success" | "primary" | "warning" => {
        const variants = {
            probleme: "destructive" as const,
            satisfaction: "success" as const,
            idee: "primary" as const,
            question: "warning" as const,
        };
        return variants[type as keyof typeof variants] || "primary";
    };

    const getUrgenceColor = (urgence: number) => {
        if (urgence >= 4) return "text-red-600";
        if (urgence >= 3) return "text-yellow-600";
        return "text-green-600";
    };

    const selectedPilier = initialFilters.pilier
        ? piliers.find((p) => p.documentId === initialFilters.pilier)
        : null;

    return (
        <>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-12">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Expressions Citoyennes
                        </Badge>
                        <h1 className="text-4xl md:text-5xl font-bold mb-6 font-marianne">
                            La voix collective de la France
                        </h1>
                        <p className="text-xl mb-8 opacity-90">
                            Découvrez les préoccupations, idées et satisfactions de vos concitoyens
                            à travers tout le territoire.
                        </p>

                        {/* Search Bar */}
                        <div className="max-w-2xl mx-auto">
                            <SearchBar
                                onSearch={handleSearch}
                                placeholder="Rechercher par mots-clés, lieu, pilier..."
                                showFilters
                                onFilterClick={() => setShowFilters(!showFilters)}
                            />
                        </div>
                    </div>
                </div>
            </section>

            {/* Filters Section */}
            {showFilters && (
                <section className="bg-background border-b civic-section-compact">
                    <div className="civic-container">
                        <AdvancedFilters
                            isOpen={showFilters}
                            onClose={() => setShowFilters(false)}
                            filters={{}}
                            onFiltersChange={() => {}}
                            onApply={() => {}}
                            onReset={() => {}}
                        />
                    </div>
                </section>
            )}

            {/* Results Section */}
            <section className="civic-section">
                <div className="civic-container">
                    {/* Results Header */}
                    <div className="flex justify-between items-center mb-8">
                        <div>
                            <h2 className="text-2xl font-bold text-foreground mb-2">
                                {isLoading
                                    ? "Chargement..."
                                    : `${expressions.length} expressions trouvées`}
                            </h2>
                            {selectedPilier && (
                                <p className="text-muted-foreground">
                                    Filtrées par le pilier :{" "}
                                    <span className="font-semibold">{selectedPilier.nom}</span>
                                </p>
                            )}
                        </div>
                        <Button variant="primary" as={Link} href="/app/my-expressions/new">
                            Nouvelle expression
                        </Button>
                    </div>

                    {/* Loading State */}
                    {isLoading && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {[...Array(6)].map((_, i) => (
                                <div
                                    key={i}
                                    className="bg-card rounded-lg p-6 animate-pulse space-y-4"
                                >
                                    <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                                    <div className="flex gap-2">
                                        <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                                        <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* Expression Cards */}
                    {!isLoading && expressions.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {expressions.map((expression, index) => (
                                <Card
                                    key={expression.documentId}
                                    hover
                                    interactive
                                    className="h-full flex flex-col animate-civic-fade-in"
                                    style={{ animationDelay: `${index * 100}ms` }}
                                    onClick={() => {
                                        router.push(`/expressions/${expression.documentId}`);
                                    }}
                                >
                                    <div className="p-6 flex-1 flex flex-col">
                                        {/* Header */}
                                        <div className="mb-4">
                                            <div className="flex items-start justify-between mb-2">
                                                <h3 className="text-lg font-semibold text-foreground line-clamp-2 flex-1">
                                                    {expression.titre}
                                                </h3>
                                                <div
                                                    className={`ml-2 text-sm font-medium ${getUrgenceColor(
                                                        expression.urgence,
                                                    )}`}
                                                >
                                                    ⚡{expression.urgence}/5
                                                </div>
                                            </div>
                                            <p className="text-sm text-muted-foreground line-clamp-3">
                                                {expression.contenu}
                                            </p>
                                        </div>

                                        {/* Tags */}
                                        <div className="flex flex-wrap gap-2 mb-4">
                                            <Badge
                                                variant={getTypeVariant(expression.type_expression)}
                                                size="sm"
                                            >
                                                {getTypeLabel(expression.type_expression)}
                                            </Badge>
                                            {expression.piliers?.map((pilier) => (
                                                <Badge
                                                    key={pilier.documentId}
                                                    variant="outline"
                                                    size="sm"
                                                    style={{
                                                        borderColor: pilier.couleur,
                                                        color: pilier.couleur,
                                                    }}
                                                >
                                                    {pilier.nom}
                                                </Badge>
                                            ))}
                                        </div>

                                        {/* Footer */}
                                        <div className="mt-auto pt-4 border-t border-border">
                                            <div className="flex justify-between items-center text-sm text-muted-foreground">
                                                <div>
                                                    📍{" "}
                                                    {typeof expression.lieu === "string"
                                                        ? expression.lieu
                                                        : expression.lieu?.nom || "Non spécifié"}
                                                </div>
                                                <div>
                                                    {new Date(
                                                        expression.date_creation,
                                                    ).toLocaleDateString("fr-FR")}
                                                </div>
                                            </div>
                                            <div className="flex justify-between items-center mt-2">
                                                <div className="flex gap-3 text-sm">
                                                    <span>👁 {expression.impact?.vues || 0}</span>
                                                    <span>
                                                        💬 {expression.impact?.commentaires || 0}
                                                    </span>
                                                    <span>
                                                        ❤️ {expression.impact?.soutiens || 0}
                                                    </span>
                                                </div>
                                                <Link
                                                    href={`/expressions/${expression.documentId}`}
                                                    className="text-primary hover:text-primary/80 text-sm font-medium"
                                                >
                                                    Voir plus →
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    )}

                    {/* Empty State */}
                    {!isLoading && expressions.length === 0 && (
                        <Card className="p-16 text-center">
                            <div className="max-w-md mx-auto">
                                <div className="text-6xl mb-4">🔍</div>
                                <h3 className="text-xl font-semibold mb-2">
                                    Aucune expression trouvée
                                </h3>
                                <p className="text-muted-foreground mb-6">
                                    Essayez de modifier vos critères de recherche ou créez la
                                    première expression sur ce sujet.
                                </p>
                                <Button variant="primary" href="/app/my-expressions/new" as={Link}>
                                    Créer une expression
                                </Button>
                            </div>
                        </Card>
                    )}
                </div>
            </section>
        </>
    );
}
