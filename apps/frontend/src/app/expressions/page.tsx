import { fetchPublicExpressions, fetchPiliers } from "@/lib/server-api";
import { Expression, Pilier, ExpressionFilters } from "@/types";
import ExpressionsClient from "./expressions-client";

interface ExpressionsPageProps {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export const revalidate = 300; // Revalidate every 5 minutes

export default async function ExpressionsPage({ searchParams }: ExpressionsPageProps) {
    const params = await searchParams;

    // Parse filters from URL params
    const filters: ExpressionFilters = {
        pilier: params.pilier as string,
        type_expression: params.type as any,
        urgence_min: params.urgence ? parseInt(params.urgence as string) : undefined,
        statut: params.statut as any,
        page: params.page ? parseInt(params.page as string) : 1,
        pageSize: 12,
    };

    // Fetch data on server
    let expressions: Expression[] = [];
    let piliers: Pilier[] = [];

    try {
        const [expressionsResponse, piliersData] = await Promise.all([
            fetchPublicExpressions(filters),
            fetchPiliers(),
        ]);
        expressions = expressionsResponse.data || [];
        piliers = piliersData;
    } catch (error) {
        console.error("Error fetching expressions:", error);
    }

    return (
        <ExpressionsClient
            initialExpressions={expressions}
            piliers={piliers}
            initialFilters={filters}
        />
    );
}
