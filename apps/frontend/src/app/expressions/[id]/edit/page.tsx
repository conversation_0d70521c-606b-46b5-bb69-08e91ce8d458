import { redirect, notFound } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import {
    fetchExpressionById,
    fetchPiliers,
    fetchSousPiliers,
    fetchEntites,
} from "@/lib/server-api";
import { EditExpressionClient } from "./edit-expression-client";

interface EditExpressionPageProps {
    params: Promise<{ id: string }>;
}

export default async function EditExpressionPage({ params }: EditExpressionPageProps) {
    const user = await getCurrentUser();
    const { id } = await params;

    if (!user) {
        redirect("/auth/login");
    }

    // Fetch all data needed for the form
    try {
        const [expression, piliers, sousPiliers, entites] = await Promise.all([
            fetchExpressionById(id),
            fetchPiliers(),
            fetchSousPiliers(),
            fetchEntites(),
        ]);

        // Check if user owns this expression
        const authorId =
            typeof expression.auteur === "string"
                ? expression.auteur
                : expression.auteur?.documentId;

        if (authorId !== user.profile?.documentId) {
            redirect("/expressions/my-expressions");
        }

        // Only allow editing drafts
        if (expression.statut !== "brouillon") {
            redirect(`/expressions/${id}`);
        }

        return (
            <EditExpressionClient
                user={user}
                expression={expression}
                piliers={piliers}
                sousPiliers={sousPiliers}
                entites={entites}
            />
        );
    } catch (error) {
        console.error("Error loading expression for edit:", error);
        notFound();
    }
}
