"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    ArrowLeftIcon,
    MapPinIcon,
    CalendarIcon,
    UserIcon,
    HeartIcon,
    ShareIcon,
    ChatBubbleLeftRightIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    ClockIcon,
    DocumentIcon,
    EyeIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { Expression } from "@/types";
import { assetUrl } from "@/lib/constants";

interface ExpressionDetailClientProps {
    expression: Expression;
}

const getStatusColor = (status: string) => {
    switch (status) {
        case "brouillon":
            return "secondary";
        case "en_moderation":
            return "warning";
        case "publie":
            return "info";
        case "en_traitement":
            return "primary";
        case "resolu":
            return "success";
        case "rejete":
            return "destructive";
        default:
            return "secondary";
    }
};

const getStatusLabel = (status: string) => {
    switch (status) {
        case "brouillon":
            return "Brouillon";
        case "en_moderation":
            return "En modération";
        case "publie":
            return "Publié";
        case "en_traitement":
            return "En traitement";
        case "resolu":
            return "Résolu";
        case "rejete":
            return "Rejeté";
        default:
            return status;
    }
};

const getTypeLabel = (type: string) => {
    const labels = {
        probleme: "Problème",
        satisfaction: "Satisfaction",
        idee: "Idée",
        question: "Question",
    };
    return labels[type as keyof typeof labels] || type;
};

const getEmotionEmoji = (emotion: string) => {
    const emojis = {
        colere: "😠",
        joie: "😊",
        tristesse: "😢",
        espoir: "🤗",
        neutre: "😐",
        frustration: "😤",
    };
    return emojis[emotion as keyof typeof emojis] || "😐";
};

export default function ExpressionDetailClient({ expression }: ExpressionDetailClientProps) {
    const router = useRouter();
    const [isLiked, setIsLiked] = useState(false);
    const [likeCount, setLikeCount] = useState(expression.impact?.soutiens || 0);

    const handleLike = () => {
        setIsLiked(!isLiked);
        setLikeCount(isLiked ? likeCount - 1 : likeCount + 1);
    };

    const handleShare = async () => {
        try {
            await navigator.share({
                title: expression.titre,
                text: expression.contenu.substring(0, 100) + "...",
                url: window.location.href,
            });
        } catch (error) {
            console.log("Share failed:", error);
        }
    };

    const authorName =
        typeof expression.auteur === "string"
            ? expression.auteur
            : expression.auteur?.nom || "Anonyme";

    const locationName =
        typeof expression.lieu === "string"
            ? expression.lieu
            : expression.lieu?.nom || "Non spécifié";

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow-sm">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16">
                        <div className="flex items-center">
                            <Button
                                variant="outline"
                                size="sm"
                                className="mr-4 items-center"
                                as={Link}
                                href="/expressions"
                            >
                                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                                Retour
                            </Button>
                            <h1 className="text-xl font-bold text-gray-900">
                                Expression citoyenne
                            </h1>
                        </div>
                        {expression.statut === "brouillon" && (
                            <Button
                                variant="primary"
                                size="sm"
                                href={`/expressions/${expression.documentId}/edit`}
                                as={Link}
                            >
                                Modifier
                            </Button>
                        )}
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <Card className="mb-8">
                    <CardHeader>
                        {/* Status Badge */}
                        <div className="flex justify-between items-start mb-4">
                            <Badge
                                variant={getStatusColor(expression.statut) as any}
                                size="lg"
                                className="mb-2"
                            >
                                {getStatusLabel(expression.statut)}
                            </Badge>
                            <div className="text-sm text-gray-500">ID: {expression.documentId}</div>
                        </div>

                        {/* Title and Type */}
                        <div className="mb-6">
                            <CardTitle className="text-3xl mb-3">{expression.titre}</CardTitle>
                            <div className="flex flex-wrap gap-2">
                                <Badge variant="primary">
                                    {getTypeLabel(expression.type_expression)}
                                </Badge>
                                <Badge variant="warning" className="flex items-center gap-1">
                                    <ExclamationTriangleIcon className="w-4 h-4" />
                                    Urgence {expression.urgence}/5
                                </Badge>
                                <Badge variant="secondary" className="flex items-center gap-1">
                                    {getEmotionEmoji(expression.etat_emotionnel)}
                                    {expression.etat_emotionnel}
                                </Badge>
                            </div>
                        </div>
                    </CardHeader>

                    <CardContent>
                        {/* Main Content */}
                        <div className="prose max-w-none mb-8">
                            <p className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                                {expression.contenu}
                            </p>
                        </div>

                        {/* Metadata */}
                        <div className="border-t pt-6 space-y-4">
                            {/* Location */}
                            <div className="flex items-center text-gray-600">
                                <MapPinIcon className="w-5 h-5 mr-2" />
                                <span>{locationName}</span>
                            </div>

                            {/* Event Date */}
                            <div className="flex items-center text-gray-600">
                                <CalendarIcon className="w-5 h-5 mr-2" />
                                <span>
                                    Événement le{" "}
                                    {new Date(expression.date_evenement).toLocaleDateString(
                                        "fr-FR",
                                        {
                                            day: "numeric",
                                            month: "long",
                                            year: "numeric",
                                        },
                                    )}
                                </span>
                            </div>

                            {/* Author */}
                            <div className="flex items-center text-gray-600">
                                <UserIcon className="w-5 h-5 mr-2" />
                                <span>Par {authorName}</span>
                            </div>

                            {/* Creation Date */}
                            <div className="flex items-center text-gray-600">
                                <ClockIcon className="w-5 h-5 mr-2" />
                                <span>
                                    Créé le{" "}
                                    {new Date(expression.date_creation).toLocaleDateString(
                                        "fr-FR",
                                        {
                                            day: "numeric",
                                            month: "long",
                                            year: "numeric",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                        },
                                    )}
                                </span>
                            </div>
                        </div>

                        {/* Pillars */}
                        {expression.piliers && expression.piliers.length > 0 && (
                            <div className="border-t pt-6 mt-6">
                                <h3 className="font-semibold mb-3">Piliers concernés</h3>
                                <div className="flex flex-wrap gap-2">
                                    {expression.piliers.map((pilier) => (
                                        <Link
                                            key={pilier.documentId}
                                            href={`/piliers/${pilier.documentId}`}
                                            className="inline-block"
                                        >
                                            <Badge
                                                variant="outline"
                                                style={{
                                                    borderColor: pilier.couleur,
                                                    color: pilier.couleur,
                                                }}
                                                className="hover:bg-opacity-10 transition-colors"
                                            >
                                                {pilier.nom}
                                            </Badge>
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Media/Attachments */}
                        {expression.medias && expression.medias.length > 0 && (
                            <div className="border-t pt-6 mt-6">
                                <h3 className="font-semibold mb-3">Pièces jointes</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    {expression.medias.map((media: any, index: number) => {
                                        const mediaUrl =
                                            typeof media === "object" ? assetUrl(media.url) : null;

                                        const mediaName = `Fichier ${index + 1}`;

                                        const isImage =
                                            typeof media === "object" &&
                                            media.mime?.startsWith("image/");

                                        const fileSize =
                                            typeof media === "object" && media.size
                                                ? `${(media.size / 1024).toFixed(1)} KB`
                                                : "";

                                        return (
                                            <div
                                                key={typeof media === "object" ? media.id : media}
                                                className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
                                            >
                                                {/* Preview */}
                                                {isImage && mediaUrl ? (
                                                    <img
                                                        src={mediaUrl}
                                                        alt={mediaName}
                                                        className="h-16 w-16 object-cover rounded-lg flex-shrink-0"
                                                    />
                                                ) : (
                                                    <div className="h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                                                        <DocumentIcon className="h-8 w-8 text-gray-500" />
                                                    </div>
                                                )}

                                                {/* File Info */}
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-gray-900 truncate">
                                                        {mediaName}
                                                    </p>
                                                    {fileSize && (
                                                        <p className="text-xs text-gray-500 mt-1">
                                                            {fileSize}
                                                        </p>
                                                    )}
                                                </div>

                                                {/* View Button */}
                                                {mediaUrl && (
                                                    <button
                                                        type="button"
                                                        onClick={() =>
                                                            window.open(mediaUrl, "_blank")
                                                        }
                                                        className="p-2 text-gray-500 hover:text-blue-600 transition-colors"
                                                        title="Voir le fichier"
                                                    >
                                                        <EyeIcon className="h-5 w-5" />
                                                    </button>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        )}

                        {/* Engagement Stats */}
                        <div className="border-t pt-6 mt-6">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-6 text-sm">
                                    <button
                                        onClick={handleLike}
                                        className="flex items-center space-x-1 hover:text-red-600 transition-colors"
                                    >
                                        {isLiked ? (
                                            <HeartIconSolid className="w-5 h-5 text-red-600" />
                                        ) : (
                                            <HeartIcon className="w-5 h-5" />
                                        )}
                                        <span>{likeCount} soutiens</span>
                                    </button>

                                    <div className="flex items-center space-x-1 text-gray-600">
                                        <ChatBubbleLeftRightIcon className="w-5 h-5" />
                                        <span>
                                            {expression.impact?.commentaires || 0} commentaires
                                        </span>
                                    </div>

                                    <button
                                        onClick={handleShare}
                                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
                                    >
                                        <ShareIcon className="w-5 h-5" />
                                        <span>Partager</span>
                                    </button>
                                </div>

                                <div className="text-sm text-gray-500">
                                    {expression.impact?.vues || 0} vues
                                </div>
                            </div>
                        </div>

                        {/* Actions */}
                        {expression.actions_prises && expression.actions_prises.length > 0 && (
                            <div className="border-t pt-6 mt-6">
                                <h3 className="font-semibold mb-3">Actions prises</h3>
                                <div className="space-y-3">
                                    {expression.actions_prises.map((action) => (
                                        <Card key={action.documentId} className="p-4">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <h4 className="font-medium mb-1">
                                                        {action.titre}
                                                    </h4>
                                                    <p className="text-sm text-gray-600">
                                                        {action.description}
                                                    </p>
                                                </div>
                                                {action.statut === "terminee" && (
                                                    <CheckCircleIcon className="w-5 h-5 text-green-600 ml-3" />
                                                )}
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Related Expressions Section */}
                <div className="mt-8">
                    <h2 className="text-xl font-bold mb-4">Expressions similaires</h2>
                    <div className="text-gray-600">
                        <p>
                            Fonctionnalité à venir : découvrez d'autres expressions sur le même
                            sujet.
                        </p>
                    </div>
                </div>
            </main>
        </div>
    );
}
