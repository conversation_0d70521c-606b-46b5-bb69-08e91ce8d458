import { fetchExpressionById } from "@/lib/server-api";
import { notFound } from "next/navigation";
import { Expression } from "@/types";
import ExpressionDetailClient from "./expression-detail-client";

interface ExpressionDetailPageProps {
    params: Promise<{ id: string }>;
}

export const revalidate = 600; // Revalidate every 10 minutes

export default async function ExpressionDetailPage({ params }: ExpressionDetailPageProps) {
    const { id } = await params;

    let expression: Expression | null = null;

    try {
        expression = await fetchExpressionById(id);
    } catch (error) {
        console.error("Error fetching expression:", error);
        notFound();
    }

    if (!expression) {
        notFound();
    }

    return <ExpressionDetailClient expression={expression} />;
}
