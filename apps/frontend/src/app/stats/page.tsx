import React from "react";
import Link from "next/link";

import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    ChartBarIcon,
    UsersIcon,
    MapPinIcon,
    ClockIcon,
    CheckBadgeIcon,
    ArrowTrendingUpIcon,
    BuildingOffice2Icon,
    GlobeEuropeAfricaIcon,
} from "@heroicons/react/24/outline";

export default function StatsPage() {
    // Mock data - in real app this would come from API
    const globalStats = {
        totalExpressions: 47832,
        activeUsers: 12547,
        resolvedExpressions: 31245,
        participatingCities: 2847,
        averageResolutionTime: 4.2,
        satisfactionRate: 87,
    };

    const pillarStats = [
        { name: "<PERSON><PERSON>", expressions: 8234, resolved: 6123, percentage: 74, color: "bg-red-500" },
        {
            name: "Transport",
            expressions: 7456,
            resolved: 5892,
            percentage: 79,
            color: "bg-purple-500",
        },
        {
            name: "Éducation",
            expressions: 6789,
            resolved: 4567,
            percentage: 67,
            color: "bg-blue-500",
        },
        {
            name: "Logement",
            expressions: 5432,
            resolved: 4321,
            percentage: 80,
            color: "bg-teal-500",
        },
        {
            name: "Sécurité",
            expressions: 4567,
            resolved: 3456,
            percentage: 76,
            color: "bg-yellow-600",
        },
        {
            name: "Environnement",
            expressions: 3456,
            resolved: 2789,
            percentage: 81,
            color: "bg-green-500",
        },
    ];

    const regionStats = [
        { name: "Île-de-France", expressions: 15234, cities: 1281, satisfaction: 89 },
        { name: "Auvergne-Rhône-Alpes", expressions: 8456, cities: 4032, satisfaction: 91 },
        { name: "Nouvelle-Aquitaine", expressions: 6789, cities: 4514, satisfaction: 88 },
        { name: "Occitanie", expressions: 5432, cities: 4448, satisfaction: 90 },
        { name: "Hauts-de-France", expressions: 4567, cities: 3789, satisfaction: 86 },
        { name: "Grand Est", expressions: 3456, cities: 5133, satisfaction: 87 },
    ];

    return (
        <>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Statistiques PillarScan
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            L'impact en temps réel
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Découvrez comment PillarScan transforme la démocratie française avec des
                            données transparentes et vérifiables.
                        </p>
                        <div className="flex items-center justify-center space-x-8 text-lg">
                            <div className="flex items-center">
                                <ArrowTrendingUpIcon className="h-6 w-6 mr-2" />
                                <span>En croissance</span>
                            </div>
                            <div className="flex items-center">
                                <CheckBadgeIcon className="h-6 w-6 mr-2" />
                                <span>Vérifiées</span>
                            </div>
                            <div className="flex items-center">
                                <GlobeEuropeAfricaIcon className="h-6 w-6 mr-2" />
                                <span>Publiques</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Global Stats */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Vue d'ensemble nationale
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Les chiffres clés de la participation citoyenne en France
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <Card hover variant="elevated" className="p-8 text-center">
                            <ChartBarIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <div className="text-4xl font-bold text-primary mb-2">
                                {globalStats.totalExpressions.toLocaleString()}
                            </div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">
                                Expressions totales
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                Depuis le lancement de la plateforme
                            </p>
                        </Card>

                        <Card hover variant="elevated" className="p-8 text-center">
                            <UsersIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <div className="text-4xl font-bold text-primary mb-2">
                                {globalStats.activeUsers.toLocaleString()}
                            </div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">
                                Citoyens actifs
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                Utilisateurs ayant exprimé ce mois
                            </p>
                        </Card>

                        <Card hover variant="elevated" className="p-8 text-center">
                            <CheckBadgeIcon className="h-12 w-12 text-success mx-auto mb-4" />
                            <div className="text-4xl font-bold text-success mb-2">
                                {globalStats.resolvedExpressions.toLocaleString()}
                            </div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">
                                Expressions résolues
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                Problèmes traités avec succès
                            </p>
                        </Card>

                        <Card hover variant="elevated" className="p-8 text-center">
                            <MapPinIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <div className="text-4xl font-bold text-primary mb-2">
                                {globalStats.participatingCities.toLocaleString()}
                            </div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">
                                Communes participantes
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                Sur 35 000 communes françaises
                            </p>
                        </Card>

                        <Card hover variant="elevated" className="p-8 text-center">
                            <ClockIcon className="h-12 w-12 text-warning mx-auto mb-4" />
                            <div className="text-4xl font-bold text-warning mb-2">
                                {globalStats.averageResolutionTime}j
                            </div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">
                                Temps moyen de résolution
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                Contre 15 jours avant PillarScan
                            </p>
                        </Card>

                        <Card hover variant="elevated" className="p-8 text-center">
                            <ArrowTrendingUpIcon className="h-12 w-12 text-success mx-auto mb-4" />
                            <div className="text-4xl font-bold text-success mb-2">
                                {globalStats.satisfactionRate}%
                            </div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">
                                Taux de satisfaction
                            </h3>
                            <p className="text-sm text-muted-foreground">
                                Citoyens satisfaits du traitement
                            </p>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Pillar Stats */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="info" size="lg" className="mb-4">
                            Par pilier
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Performance par domaine
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Analyse détaillée de l'efficacité sur chacun des 12 piliers
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {pillarStats.map((pillar, index) => (
                            <Card key={index} hover variant="elevated" className="p-6">
                                <div className="flex items-center mb-4">
                                    <div
                                        className={`w-12 h-12 ${pillar.color} rounded-xl flex items-center justify-center text-white text-xl mr-4`}
                                    >
                                        {pillar.name.charAt(0)}
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-bold text-foreground">
                                            {pillar.name}
                                        </h3>
                                        <Badge variant="outline" size="sm">
                                            {pillar.percentage}% résolues
                                        </Badge>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">Expressions</span>
                                        <span className="font-semibold">
                                            {pillar.expressions.toLocaleString()}
                                        </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">Résolues</span>
                                        <span className="font-semibold text-success">
                                            {pillar.resolved.toLocaleString()}
                                        </span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                        <div
                                            className={`h-2 rounded-full ${pillar.color}`}
                                            style={{ width: `${pillar.percentage}%` }}
                                        ></div>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Regional Stats */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="success" size="lg" className="mb-4">
                            Par région
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Participation régionale
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            L'engagement citoyen à travers les régions françaises
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {regionStats.map((region, index) => (
                            <Card key={index} hover variant="elevated" className="p-6">
                                <div className="flex items-center mb-4">
                                    <BuildingOffice2Icon className="h-8 w-8 text-primary mr-3" />
                                    <h3 className="text-lg font-bold text-foreground">
                                        {region.name}
                                    </h3>
                                </div>
                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Expressions</span>
                                        <span className="font-semibold">
                                            {region.expressions.toLocaleString()}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Communes</span>
                                        <span className="font-semibold">
                                            {region.cities.toLocaleString()}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Satisfaction</span>
                                        <Badge variant="success" size="sm">
                                            {region.satisfaction}%
                                        </Badge>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="civic-section civic-gradient text-white">
                <div className="civic-container text-center">
                    <ChartBarIcon className="h-16 w-16 mx-auto mb-6 opacity-80" />
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Contribuez à ces statistiques
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                        Chaque expression compte. Rejoignez le mouvement et aidez à améliorer ces
                        chiffres pour toute la France.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary" href="/app/my-expressions/new" as={Link}>
                            Soumettre une expression
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            href="/piliers"
                            as={Link}
                            className="border-white text-white hover:bg-white hover:text-primary"
                        >
                            Explorer les piliers
                        </Button>
                    </div>
                </div>
            </section>
        </>
    );
}
