import React from "react";
import Link from "next/link";
import Layout from "@/components/layout/Layout";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    CodeBracketIcon,
    KeyIcon,
    DocumentTextIcon,
    ChartBarIcon,
    GlobeAltIcon,
    ShieldCheckIcon,
    ClockIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

export default function ApiDocsPage() {
    const endpoints = [
        {
            method: "GET",
            path: "/api/v1/expressions",
            description: "Récupérer la liste des expressions publiques",
            auth: false,
            rateLimit: "100/hour",
        },
        {
            method: "GET",
            path: "/api/v1/expressions/{id}",
            description: "Récupérer une expression spécifique",
            auth: false,
            rateLimit: "100/hour",
        },
        {
            method: "GET",
            path: "/api/v1/piliers",
            description: "Récupérer la liste des 12 piliers",
            auth: false,
            rateLimit: "100/hour",
        },
        {
            method: "GET",
            path: "/api/v1/stats",
            description: "Récupérer les statistiques publiques",
            auth: false,
            rateLimit: "50/hour",
        },
        {
            method: "POST",
            path: "/api/v1/expressions",
            description: "Créer une nouvelle expression",
            auth: true,
            rateLimit: "10/hour",
        },
        {
            method: "GET",
            path: "/api/v1/me/expressions",
            description: "Récupérer ses propres expressions",
            auth: true,
            rateLimit: "50/hour",
        },
    ];

    const useCases = [
        {
            title: "Applications citoyennes",
            description: "Créez des apps mobiles ou web pour faciliter l'expression citoyenne",
            icon: <GlobeAltIcon className="h-8 w-8" />,
            color: "bg-blue-500",
            examples: ["App mobile communale", "Widget pour site web", "Bot Telegram"],
        },
        {
            title: "Analyse de données",
            description: "Analysez les tendances et patterns des préoccupations citoyennes",
            icon: <ChartBarIcon className="h-8 w-8" />,
            color: "bg-green-500",
            examples: ["Tableaux de bord", "Études sociologiques", "Prédictions"],
        },
        {
            title: "Intégrations",
            description: "Intégrez PillarScan dans vos systèmes existants",
            icon: <CodeBracketIcon className="h-8 w-8" />,
            color: "bg-purple-500",
            examples: ["CRM collectivités", "Systèmes de ticketing", "Portails citoyens"],
        },
        {
            title: "Recherche académique",
            description: "Accédez aux données pour vos recherches en sciences sociales",
            icon: <DocumentTextIcon className="h-8 w-8" />,
            color: "bg-orange-500",
            examples: ["Thèses", "Publications", "Études comparatives"],
        },
    ];

    return (
        <Layout>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            API PillarScan
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            API ouverte et transparente
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Accédez aux données publiques de PillarScan pour créer des applications
                            innovantes au service de la démocratie participative.
                        </p>
                        <div className="flex items-center justify-center space-x-8 text-lg">
                            <div className="flex items-center">
                                <GlobeAltIcon className="h-6 w-6 mr-2" />
                                <span>REST API</span>
                            </div>
                            <div className="flex items-center">
                                <ShieldCheckIcon className="h-6 w-6 mr-2" />
                                <span>Sécurisée</span>
                            </div>
                            <div className="flex items-center">
                                <DocumentTextIcon className="h-6 w-6 mr-2" />
                                <span>Documentée</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Quick Start */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Démarrage rapide
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Commencez à utiliser l'API PillarScan en quelques minutes
                        </p>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div>
                            <div className="space-y-6">
                                <div className="flex items-start">
                                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4 flex-shrink-0">
                                        1
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-foreground mb-2">
                                            Obtenez votre clé API
                                        </h3>
                                        <p className="text-muted-foreground">
                                            Créez un compte développeur gratuit pour accéder aux
                                            endpoints authentifiés.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4 flex-shrink-0">
                                        2
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-foreground mb-2">
                                            Testez les endpoints
                                        </h3>
                                        <p className="text-muted-foreground">
                                            Utilisez notre interface interactive pour explorer
                                            l'API.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4 flex-shrink-0">
                                        3
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-foreground mb-2">
                                            Intégrez dans votre app
                                        </h3>
                                        <p className="text-muted-foreground">
                                            Suivez nos exemples de code pour une intégration rapide.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="mt-8">
                                <Button size="lg" variant="primary" href="/auth/register" as={Link}>
                                    Créer un compte développeur
                                </Button>
                            </div>
                        </div>
                        <div>
                            <Card variant="elevated" className="p-6">
                                <div className="flex items-center mb-4">
                                    <CodeBracketIcon className="h-6 w-6 text-primary mr-2" />
                                    <h3 className="text-lg font-semibold text-foreground">
                                        Exemple d'appel API
                                    </h3>
                                </div>
                                <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100 overflow-x-auto">
                                    <div className="text-green-400">
                                        # Récupérer les expressions publiques
                                    </div>
                                    <div className="text-blue-400">curl</div>{" "}
                                    <span className="text-yellow-300">-X GET</span> \<br />
                                    <span className="text-purple-300">
                                        "https://api.pillarscan.fr/v1/expressions"
                                    </span>{" "}
                                    \<br />
                                    <span className="text-yellow-300">-H</span>{" "}
                                    <span className="text-green-300">
                                        "Accept: application/json"
                                    </span>
                                    <br />
                                    <br />
                                    <div className="text-green-400"># Avec authentification</div>
                                    <div className="text-blue-400">curl</div>{" "}
                                    <span className="text-yellow-300">-X POST</span> \<br />
                                    <span className="text-purple-300">
                                        "https://api.pillarscan.fr/v1/expressions"
                                    </span>{" "}
                                    \<br />
                                    <span className="text-yellow-300">-H</span>{" "}
                                    <span className="text-green-300">
                                        "Authorization: Bearer YOUR_API_KEY"
                                    </span>{" "}
                                    \<br />
                                    <span className="text-yellow-300">-H</span>{" "}
                                    <span className="text-green-300">
                                        "Content-Type: application/json"
                                    </span>
                                </div>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>

            {/* Use Cases */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="info" size="lg" className="mb-4">
                            Cas d'usage
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Que pouvez-vous construire ?
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            L'API PillarScan ouvre de nombreuses possibilités pour améliorer la
                            démocratie participative
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {useCases.map((useCase, index) => (
                            <Card key={index} hover variant="elevated" className="p-6 text-center">
                                <div
                                    className={`w-16 h-16 ${useCase.color} rounded-xl mx-auto mb-4 flex items-center justify-center text-white`}
                                >
                                    {useCase.icon}
                                </div>
                                <h3 className="text-lg font-bold text-foreground mb-3">
                                    {useCase.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    {useCase.description}
                                </p>
                                <div className="text-xs text-muted-foreground">
                                    <strong>Exemples :</strong>
                                    <ul className="mt-2 space-y-1">
                                        {useCase.examples.map((example, exampleIndex) => (
                                            <li key={exampleIndex}>• {example}</li>
                                        ))}
                                    </ul>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* API Endpoints */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="success" size="lg" className="mb-4">
                            Endpoints disponibles
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            API Reference
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Liste complète des endpoints disponibles dans l'API PillarScan v1
                        </p>
                    </div>

                    <div className="space-y-4">
                        {endpoints.map((endpoint, index) => (
                            <Card key={index} variant="elevated" className="p-6">
                                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center mb-2">
                                            <Badge
                                                variant={
                                                    endpoint.method === "GET"
                                                        ? "info"
                                                        : endpoint.method === "POST"
                                                          ? "success"
                                                          : "warning"
                                                }
                                                size="sm"
                                                className="mr-3"
                                            >
                                                {endpoint.method}
                                            </Badge>
                                            <code className="text-lg font-mono text-foreground">
                                                {endpoint.path}
                                            </code>
                                        </div>
                                        <p className="text-muted-foreground">
                                            {endpoint.description}
                                        </p>
                                    </div>
                                    <div className="flex items-center space-x-4 mt-4 lg:mt-0">
                                        <div className="flex items-center">
                                            {endpoint.auth ? (
                                                <KeyIcon className="h-4 w-4 text-warning mr-1" />
                                            ) : (
                                                <GlobeAltIcon className="h-4 w-4 text-success mr-1" />
                                            )}
                                            <span className="text-sm text-muted-foreground">
                                                {endpoint.auth ? "Auth requise" : "Public"}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <ClockIcon className="h-4 w-4 text-info mr-1" />
                                            <span className="text-sm text-muted-foreground">
                                                {endpoint.rateLimit}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Authentication */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        <Card variant="elevated" className="p-8">
                            <div className="flex items-center mb-6">
                                <KeyIcon className="h-8 w-8 text-primary mr-4" />
                                <h2 className="text-2xl font-bold text-foreground">
                                    Authentification
                                </h2>
                            </div>
                            <div className="space-y-4 text-muted-foreground">
                                <p>
                                    L'API PillarScan utilise des clés API pour l'authentification.
                                    Incluez votre clé dans l'en-tête Authorization de vos requêtes.
                                </p>
                                <div className="bg-gray-900 rounded-lg p-4 text-sm font-mono text-gray-100">
                                    <div className="text-green-400">
                                        # En-tête d'authentification
                                    </div>
                                    <div>
                                        <span className="text-yellow-300">Authorization:</span>{" "}
                                        <span className="text-purple-300">Bearer YOUR_API_KEY</span>
                                    </div>
                                </div>
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <p className="text-blue-800 text-sm">
                                        <strong>💡 Conseil :</strong> Gardez votre clé API secrète
                                        et ne l'exposez jamais côté client.
                                    </p>
                                </div>
                            </div>
                        </Card>

                        <Card variant="elevated" className="p-8">
                            <div className="flex items-center mb-6">
                                <ShieldCheckIcon className="h-8 w-8 text-success mr-4" />
                                <h2 className="text-2xl font-bold text-foreground">
                                    Limites et quotas
                                </h2>
                            </div>
                            <div className="space-y-4 text-muted-foreground">
                                <p>
                                    Pour garantir la qualité du service, l'API impose des limites de
                                    débit selon votre plan d'utilisation.
                                </p>
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span className="font-semibold">Plan Gratuit</span>
                                        <Badge variant="info">1000 req/jour</Badge>
                                    </div>
                                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span className="font-semibold">Plan Développeur</span>
                                        <Badge variant="success">10k req/jour</Badge>
                                    </div>
                                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <span className="font-semibold">Plan Entreprise</span>
                                        <Badge variant="warning">100k req/jour</Badge>
                                    </div>
                                </div>
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <p className="text-yellow-800 text-sm">
                                        <strong>⚠️ Important :</strong> Les limites se
                                        réinitialisent chaque jour à minuit (UTC).
                                    </p>
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Support */}
            <section className="civic-section civic-gradient text-white">
                <div className="civic-container text-center">
                    <DocumentTextIcon className="h-16 w-16 mx-auto mb-6 opacity-80" />
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Besoin d'aide avec l'API ?
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                        Notre équipe technique est là pour vous accompagner dans l'intégration de
                        l'API PillarScan.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary" href="/contact" as={Link}>
                            Support technique
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            href="https://github.com/pillarscan/api-examples"
                            as="a"
                            target="_blank"
                            className="border-white text-white hover:bg-white hover:text-primary"
                        >
                            Exemples sur GitHub
                        </Button>
                    </div>
                    <div className="mt-8 text-sm opacity-75">
                        <p>Documentation complète • Exemples de code • Support réactif</p>
                    </div>
                </div>
            </section>
        </Layout>
    );
}
