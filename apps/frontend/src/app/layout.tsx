import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Suspense } from "react";
import { getCurrentUser } from "@/lib/auth/server";
import { AuthProvider } from "@/components/providers/auth-provider";
import { NotificationProvider } from "@/components/providers/NotificationProvider";
import { Toaster } from "sonner";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "PillarScan - La voix citoyenne qui transforme la France",
    description:
        "Plateforme citoyenne pour exprimer et suivre vos préoccupations sur les 12 piliers de la société française.",
    keywords: "citoyen, expression, France, démocratie, participation",
    authors: [{ name: "PillarScan Team" }],
    openGraph: {
        title: "PillarScan - La voix citoyenne",
        description: "Transformez vos préoccupations en actions concrètes",
        type: "website",
        locale: "fr_FR",
    },
};

export default async function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const user = await getCurrentUser();

    return (
        <html lang="fr" className="light" style={{ colorScheme: "light" }}>
            <head>
                <meta name="color-scheme" content="light" />
                <meta name="theme-color" content="#ffffff" />
            </head>
            <body
                className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white text-gray-900`}
            >
                <AuthProvider user={user}>
                    <NotificationProvider>
                        <Suspense
                            fallback={
                                <div
                                    className="flex items-center justify-center min-h-screen text-gray-500"
                                    style={{ fontFamily: "var(--font-geist-sans)" }}
                                >
                                    Loading...
                                </div>
                            }
                        >
                            {children}
                        </Suspense>
                        <Toaster position="top-center" />
                    </NotificationProvider>
                </AuthProvider>
            </body>
        </html>
    );
}
