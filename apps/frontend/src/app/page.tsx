"use client";

import React from "react";
import Link from "next/link";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    ChatBubbleBottomCenterTextIcon,
    MapPinIcon,
    ChartBarIcon,
    CheckBadgeIcon,
    BuildingLibraryIcon,
    GlobeEuropeAfricaIcon,
} from "@heroicons/react/24/outline";
import { useLayoutConfig } from "@/components/providers/layout-provider";

const pillarColors = [
    { name: "<PERSON><PERSON>", color: "bg-red-500", icon: "🏥" },
    { name: "Éducation", color: "bg-blue-500", icon: "🎓" },
    { name: "Transport", color: "bg-purple-500", icon: "🚇" },
    { name: "Logement", color: "bg-teal-500", icon: "🏘️" },
    { name: "Emploi", color: "bg-orange-500", icon: "💼" },
    { name: "Sécurité", color: "bg-yellow-600", icon: "🛡️" },
    { name: "Environnement", color: "bg-green-500", icon: "🌳" },
    { name: "Justice", color: "bg-slate-600", icon: "⚖️" },
    { name: "Pouvoir d'achat", color: "bg-gray-500", icon: "💰" },
    { name: "Vie sociale", color: "bg-pink-500", icon: "🤝" },
    { name: "Démocratie", color: "bg-cyan-500", icon: "🏛️" },
    { name: "Culture", color: "bg-yellow-400", icon: "🎭" },
];

export default function Home() {
    // Configure layout to hide header for homepage
    useLayoutConfig({
        showHeader: false,
        showFooter: true,
    });

    return (
        <>
            {/* Hero Section */}
            <section className="relative overflow-hidden civic-gradient text-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="relative civic-container py-20 lg:py-32">
                    <div className="text-center max-w-4xl mx-auto animate-civic-fade-in">
                        <div className="flex items-center justify-center mb-6">
                            <BuildingLibraryIcon className="h-12 w-12 mr-4" />
                            <span className="text-2xl font-bold">République Française</span>
                        </div>

                        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 font-marianne">
                            Bienvenue sur
                            <span className="block text-yellow-300 mt-2">PillarScan</span>
                        </h1>

                        <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto leading-relaxed">
                            La plateforme citoyenne qui{" "}
                            <strong>redonne le pouvoir au peuple français</strong> en transformant
                            vos expressions en actions concrètes mesurables.
                        </p>

                        <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                            <Button
                                size="xl"
                                variant="primary"
                                className="px-10"
                                as={Link}
                                href="/auth/register"
                                icon={<ChatBubbleBottomCenterTextIcon className="h-5 w-5" />}
                            >
                                Exprimer maintenant
                            </Button>
                            <Button
                                size="xl"
                                variant="outline"
                                as={Link}
                                href="/expressions"
                                className="border-white text-white hover:bg-white hover:text-primary px-10"
                                icon={<MapPinIcon className="h-5 w-5" />}
                            >
                                Explorer les expressions
                            </Button>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
                            <div className="text-center">
                                <div className="text-3xl font-bold">67M</div>
                                <div className="text-sm opacity-90">Citoyens français</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">12</div>
                                <div className="text-sm opacity-90">Piliers de société</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">35K</div>
                                <div className="text-sm opacity-90">Communes</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold">24/7</div>
                                <div className="text-sm opacity-90">Disponible</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* How It Works Section */}
            <section className="civic-section bg-background">
                <div className="civic-container">
                    <div className="text-center mb-16">
                        <Badge variant="primary" size="lg" className="mb-4">
                            Comment ça marche
                        </Badge>
                        <h2 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                            De la frustration à l'action
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Transformez votre culture de plainte en culture d'action collective avec
                            notre processus révolutionnaire.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                        <Card hover className="text-center p-8 animate-civic-fade-in">
                            <div className="w-16 h-16 civic-gradient rounded-full mx-auto mb-6 flex items-center justify-center">
                                <ChatBubbleBottomCenterTextIcon className="h-8 w-8 text-white" />
                            </div>
                            <h3 className="text-2xl font-bold mb-4 text-foreground">
                                1. Exprimez-vous
                            </h3>
                            <p className="text-muted-foreground leading-relaxed">
                                Partagez votre expérience concrète sur l'un des 12 piliers de la
                                société française. Soyez factuel, localisé, daté.
                            </p>
                            <Badge variant="info" className="mt-4">
                                30 secondes
                            </Badge>
                        </Card>

                        <Card hover className="text-center p-8 animate-civic-fade-in">
                            <div className="w-16 h-16 civic-gradient rounded-full mx-auto mb-6 flex items-center justify-center">
                                <ChartBarIcon className="h-8 w-8 text-white" />
                            </div>
                            <h3 className="text-2xl font-bold mb-4 text-foreground">
                                2. IA + Modération
                            </h3>
                            <p className="text-muted-foreground leading-relaxed">
                                Notre IA française analyse et classe automatiquement votre
                                expression. Des validateurs qualifiés vérifient la qualité.
                            </p>
                            <Badge variant="warning" className="mt-4">
                                2-5 minutes
                            </Badge>
                        </Card>

                        <Card hover className="text-center p-8 animate-civic-fade-in">
                            <div className="w-16 h-16 civic-gradient rounded-full mx-auto mb-6 flex items-center justify-center">
                                <CheckBadgeIcon className="h-8 w-8 text-white" />
                            </div>
                            <h3 className="text-2xl font-bold mb-4 text-foreground">
                                3. Action trackée
                            </h3>
                            <p className="text-muted-foreground leading-relaxed">
                                Les bons acteurs sont alertés automatiquement. Vous suivez
                                l'évolution jusqu'à la résolution complète.
                            </p>
                            <Badge variant="success" className="mt-4">
                                Impact mesurable
                            </Badge>
                        </Card>
                    </div>

                    <div className="text-center">
                        <p className="text-lg text-muted-foreground mb-6">
                            <strong>Résultat :</strong> 70% de problèmes résolus plus rapidement,
                            confiance restaurée.
                        </p>
                        <Button as={Link} size="lg" variant="primary" href="/auth/register">
                            Rejoindre le mouvement
                        </Button>
                    </div>
                </div>
            </section>

            {/* 12 Pillars Section */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="text-center mb-16">
                        <Badge variant="primary" size="lg" className="mb-4">
                            Les 12 Piliers
                        </Badge>
                        <h2 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                            Tous les aspects de votre vie
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            PillarScan couvre l'intégralité des domaines qui impactent votre
                            quotidien de citoyen français.
                        </p>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {pillarColors.map((pillar, index) => (
                            <Card
                                key={index}
                                hover
                                interactive
                                className="p-6 text-center group cursor-pointer animate-civic-fade-in"
                                style={{ animationDelay: `${index * 100}ms` }}
                            >
                                <div
                                    className={`w-12 h-12 ${pillar.color} rounded-xl mx-auto mb-4 flex items-center justify-center text-2xl group-hover:scale-110 transition-transform`}
                                >
                                    {pillar.icon}
                                </div>
                                <h3 className="font-bold text-sm text-foreground group-hover:text-primary transition-colors">
                                    {pillar.name}
                                </h3>
                            </Card>
                        ))}
                    </div>

                    <div className="text-center mt-12">
                        <Button variant="outline" size="lg" as={Link} href="/piliers">
                            Explorer tous les piliers
                        </Button>
                    </div>
                </div>
            </section>

            {/* Success Stories ------ HIDDEN */}
            <section className="civic-section bg-background hidden">
                <div className="civic-container">
                    <div className="text-center mb-16">
                        <Badge variant="success" size="lg" className="mb-4">
                            Succès français
                        </Badge>
                        <h2 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                            Ils ont déjà changé leur quotidien
                        </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <Card hover variant="elevated" className="p-8">
                            <div className="mb-6">
                                <Badge variant="success" dot>
                                    Paris 11ème
                                </Badge>
                            </div>
                            <h3 className="text-xl font-bold mb-4">
                                Place de la République sécurisée
                            </h3>
                            <p className="text-muted-foreground mb-6">
                                127 expressions sur l'insécurité nocturne → Éclairage renforcé +
                                patrouilles
                            </p>
                            <div className="text-sm text-success font-semibold">
                                -73% d'incivilités en 2 mois
                            </div>
                        </Card>

                        <Card hover variant="elevated" className="p-8">
                            <div className="mb-6">
                                <Badge variant="info" dot>
                                    Marseille
                                </Badge>
                            </div>
                            <h3 className="text-xl font-bold mb-4">Quartier Noailles transformé</h3>
                            <p className="text-muted-foreground mb-6">
                                89 expressions sur l'insalubrité → Task force propreté déployée
                            </p>
                            <div className="text-sm text-success font-semibold">
                                5,000 habitants soulagés
                            </div>
                        </Card>

                        <Card hover variant="elevated" className="p-8">
                            <div className="mb-6">
                                <Badge variant="warning" dot>
                                    Lille
                                </Badge>
                            </div>
                            <h3 className="text-xl font-bold mb-4">Tramway ponctuel</h3>
                            <p className="text-muted-foreground mb-6">
                                445 expressions sur les retards → Révision complète des horaires
                            </p>
                            <div className="text-sm text-success font-semibold">
                                Ponctualité 87% → 96%
                            </div>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Vision Section */}
            <section className="civic-section civic-gradient text-white">
                <div className="civic-container text-center">
                    <div className="max-w-4xl mx-auto">
                        <GlobeEuropeAfricaIcon className="h-16 w-16 mx-auto mb-8 opacity-80" />
                        <h2 className="text-4xl md:text-5xl font-bold mb-8">
                            La France, laboratoire démocratique du 21ème siècle
                        </h2>
                        <p className="text-xl mb-12 leading-relaxed opacity-90">
                            Aujourd'hui la France, demain l'Europe, après-demain le monde.
                            PillarScan transforme la démocratie française en modèle d'efficacité et
                            de transparence pour l'humanité entière.
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                            <div>
                                <div className="text-3xl font-bold mb-2">2024</div>
                                <div className="text-lg opacity-80">🇫🇷 France</div>
                            </div>
                            <div>
                                <div className="text-3xl font-bold mb-2">2025</div>
                                <div className="text-lg opacity-80">🇪🇺 Europe</div>
                            </div>
                            <div>
                                <div className="text-3xl font-bold mb-2">2027</div>
                                <div className="text-lg opacity-80">🌍 Afrique</div>
                            </div>
                            <div>
                                <div className="text-3xl font-bold mb-2">2030</div>
                                <div className="text-lg opacity-80">🌏 Monde</div>
                            </div>
                        </div>

                        <Button size="xl" variant="primary" href="/auth/register" as={Link}>
                            Faire partie de l'histoire
                        </Button>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="civic-section bg-background border-t">
                <div className="civic-container text-center">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                        Prêt à transformer votre quotidien ?
                    </h2>
                    <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                        Rejoignez des milliers de citoyens français qui ont choisi d'agir plutôt que
                        de subir.
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                            size="xl"
                            variant="primary"
                            fullWidth={false}
                            href="/auth/register"
                            as={Link}
                        >
                            Créer mon compte gratuit
                        </Button>
                        <Button
                            size="xl"
                            variant="outline"
                            fullWidth={false}
                            as={Link}
                            href="/expressions"
                        >
                            Voir les expressions
                        </Button>
                    </div>

                    <p className="text-sm text-muted-foreground mt-6">
                        Gratuit pour tous les citoyens français • Données sécurisées RGPD
                    </p>
                </div>
            </section>
        </>
    );
}
