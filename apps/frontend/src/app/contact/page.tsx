"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";

import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    EnvelopeIcon,
    PhoneIcon,
    MapPinIcon,
    ClockIcon,
    ChatBubbleLeftRightIcon,
    ExclamationTriangleIcon,
    QuestionMarkCircleIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";

export default function ContactPage() {
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        subject: "",
        category: "",
        message: "",
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);

    const contactMethods = [
        {
            title: "Support général",
            description: "Questions sur l'utilisation de PillarScan",
            email: "<EMAIL>",
            icon: <QuestionMarkCircleIcon className="h-8 w-8" />,
            color: "bg-blue-500",
            responseTime: "24h",
        },
        {
            title: "Support technique",
            description: "Problèmes techniques et bugs",
            email: "<EMAIL>",
            icon: <ExclamationTriangleIcon className="h-8 w-8" />,
            color: "bg-orange-500",
            responseTime: "12h",
        },
        {
            title: "Données personnelles",
            description: "Questions RGPD et confidentialité",
            email: "<EMAIL>",
            icon: <UserGroupIcon className="h-8 w-8" />,
            color: "bg-green-500",
            responseTime: "48h",
        },
        {
            title: "Partenariats",
            description: "Collectivités et entreprises",
            email: "<EMAIL>",
            icon: <ChatBubbleLeftRightIcon className="h-8 w-8" />,
            color: "bg-purple-500",
            responseTime: "72h",
        },
    ];

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        await new Promise((resolve) => setTimeout(resolve, 2000));

        setIsSubmitted(true);
        setIsSubmitting(false);
    };

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    ) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        });
    };

    if (isSubmitted) {
        return (
            <>
                <section className="civic-section">
                    <div className="civic-container">
                        <div className="max-w-2xl mx-auto text-center">
                            <Card variant="elevated" className="p-12">
                                <div className="w-16 h-16 bg-success rounded-full mx-auto mb-6 flex items-center justify-center">
                                    <EnvelopeIcon className="h-8 w-8 text-white" />
                                </div>
                                <h1 className="text-3xl font-bold text-foreground mb-4">
                                    Message envoyé !
                                </h1>
                                <p className="text-lg text-muted-foreground mb-8">
                                    Merci pour votre message. Notre équipe vous répondra dans les
                                    meilleurs délais.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <Button variant="primary" href="/" as={Link}>
                                        Retour à l'accueil
                                    </Button>
                                    <Button variant="outline" href="/help" as={Link}>
                                        Centre d'aide
                                    </Button>
                                </div>
                            </Card>
                        </div>
                    </div>
                </section>
            </>
        );
    }

    return (
        <>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Nous contacter
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            Parlons ensemble
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Notre équipe est là pour vous accompagner dans votre expérience
                            PillarScan et répondre à toutes vos questions.
                        </p>
                        <div className="flex items-center justify-center space-x-8 text-lg">
                            <div className="flex items-center">
                                <ClockIcon className="h-6 w-6 mr-2" />
                                <span>Réponse rapide</span>
                            </div>
                            <div className="flex items-center">
                                <UserGroupIcon className="h-6 w-6 mr-2" />
                                <span>Équipe dédiée</span>
                            </div>
                            <div className="flex items-center">
                                <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2" />
                                <span>Support français</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Contact Methods */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Choisissez votre canal de contact
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Selon votre besoin, contactez directement le bon service pour une
                            réponse plus rapide et personnalisée.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                        {contactMethods.map((method, index) => (
                            <Card key={index} hover variant="elevated" className="p-6 text-center">
                                <div
                                    className={`w-16 h-16 ${method.color} rounded-xl mx-auto mb-4 flex items-center justify-center text-white`}
                                >
                                    {method.icon}
                                </div>
                                <h3 className="text-lg font-bold text-foreground mb-2">
                                    {method.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    {method.description}
                                </p>
                                <a
                                    href={`mailto:${method.email}`}
                                    className="text-primary hover:underline text-sm font-semibold"
                                >
                                    {method.email}
                                </a>
                                <div className="mt-3">
                                    <Badge variant="outline" size="sm">
                                        ⏱️ {method.responseTime}
                                    </Badge>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Contact Form */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <Badge variant="info" size="lg" className="mb-4">
                                Formulaire de contact
                            </Badge>
                            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                                Envoyez-nous un message
                            </h2>
                            <p className="text-xl text-muted-foreground">
                                Remplissez le formulaire ci-dessous et nous vous répondrons dans les
                                plus brefs délais.
                            </p>
                        </div>

                        <Card variant="elevated" className="p-8">
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label
                                            htmlFor="name"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            Nom complet *
                                        </label>
                                        <input
                                            type="text"
                                            id="name"
                                            name="name"
                                            required
                                            value={formData.name}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            placeholder="Votre nom et prénom"
                                        />
                                    </div>
                                    <div>
                                        <label
                                            htmlFor="email"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            Email *
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            required
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label
                                            htmlFor="category"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            Catégorie *
                                        </label>
                                        <select
                                            id="category"
                                            name="category"
                                            required
                                            value={formData.category}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                        >
                                            <option value="">Choisissez une catégorie</option>
                                            <option value="support">Support général</option>
                                            <option value="technique">Problème technique</option>
                                            <option value="donnees">Données personnelles</option>
                                            <option value="partenariat">Partenariat</option>
                                            <option value="presse">Presse</option>
                                            <option value="autre">Autre</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label
                                            htmlFor="subject"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            Sujet *
                                        </label>
                                        <input
                                            type="text"
                                            id="subject"
                                            name="subject"
                                            required
                                            value={formData.subject}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            placeholder="Résumé de votre demande"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label
                                        htmlFor="message"
                                        className="block text-sm font-semibold text-foreground mb-2"
                                    >
                                        Message *
                                    </label>
                                    <textarea
                                        id="message"
                                        name="message"
                                        required
                                        rows={6}
                                        value={formData.message}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                        placeholder="Décrivez votre demande en détail..."
                                    />
                                </div>

                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <p className="text-blue-800 text-sm">
                                        <strong>💡 Conseil :</strong> Plus votre message est précis,
                                        plus nous pourrons vous aider efficacement. N'hésitez pas à
                                        inclure des captures d'écran si nécessaire.
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4 justify-end">
                                    <Button type="button" variant="outline" href="/help" as={Link}>
                                        Consulter l'aide
                                    </Button>
                                    <Button
                                        type="submit"
                                        variant="primary"
                                        loading={isSubmitting}
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
                                    </Button>
                                </div>
                            </form>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Other Contact Info */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <Card variant="elevated" className="p-8 text-center">
                            <EnvelopeIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <h3 className="text-xl font-bold text-foreground mb-4">Email</h3>
                            <p className="text-muted-foreground mb-4">
                                Pour toute question générale
                            </p>
                            <a
                                href="mailto:<EMAIL>"
                                className="text-primary hover:underline font-semibold"
                            >
                                <EMAIL>
                            </a>
                        </Card>

                        <Card variant="elevated" className="p-8 text-center">
                            <MapPinIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <h3 className="text-xl font-bold text-foreground mb-4">Adresse</h3>
                            <p className="text-muted-foreground">
                                PillarScan SAS
                                <br />
                                123 Avenue de la République
                                <br />
                                75011 Paris, France
                            </p>
                        </Card>

                        <Card variant="elevated" className="p-8 text-center">
                            <ClockIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                            <h3 className="text-xl font-bold text-foreground mb-4">
                                Horaires de support
                            </h3>
                            <p className="text-muted-foreground">
                                Lundi - Vendredi
                                <br />
                                9h00 - 18h00 (CET)
                                <br />
                                <span className="text-sm">Réponse sous 24h</span>
                            </p>
                        </Card>
                    </div>
                </div>
            </section>
        </>
    );
}
