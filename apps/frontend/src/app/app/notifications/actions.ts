"use server";

import { revalidatePath, revalidateTag } from "next/cache";
import { fetchNotifications, fetchNotificationStats, serverMutation } from "@/lib/server-api";
import { ActionResult } from "@/lib/actions";
import { ServerNotification } from "@/types";
import { z } from "zod";
import { formatZodErrors } from "@/lib/schemas/utils";

// Schema for notification query parameters
const notificationQuerySchema = z.object({
    page: z.number().int().positive().optional(),
    pageSize: z.number().int().positive().max(100).optional(),
    type: z.string().optional(),
    category: z.enum(["system", "expression", "moderation", "profile", "achievement"]).optional(),
    read: z.enum(["true", "false"]).optional(),
    timeRange: z.enum(["today", "week", "month", "all"]).optional(),
});

export async function getNotificationsAction(params?: {
    page?: number;
    pageSize?: number;
    type?: string;
    category?: string;
    read?: string;
    timeRange?: string;
}): Promise<ActionResult<{ notifications: ServerNotification[]; pagination: any }>> {
    try {
        // Validate parameters if provided
        if (params) {
            const validation = notificationQuerySchema.safeParse(params);
            if (!validation.success) {
                return {
                    success: false,
                    error: validation.error.errors[0].message,
                    fieldErrors: formatZodErrors(validation.error),
                    data: { notifications: [], pagination: null },
                };
            }
        }

        const result = await fetchNotifications(params);
        return {
            success: true,
            data: result,
        };
    } catch (error) {
        console.error("Error fetching notifications:", error);
        return {
            success: false,
            error: "Échec de la récupération des notifications",
            data: { notifications: [], pagination: null },
        };
    }
}

export async function getNotificationStatsAction(): Promise<ActionResult<any>> {
    try {
        const stats = await fetchNotificationStats();
        return {
            success: true,
            data: stats,
        };
    } catch (error) {
        console.error("Error fetching notification stats:", error);
        return {
            success: false,
            error: "Échec de la récupération des statistiques de notifications",
        };
    }
}

// Schema for marking notification as read
const markNotificationSchema = z.object({
    notificationId: z.string().min(1, "L'ID de la notification est requis"),
});

export async function markNotificationAsReadAction(
    notificationId: string,
): Promise<ActionResult<ServerNotification>> {
    try {
        // Validate notification ID
        const validation = markNotificationSchema.safeParse({ notificationId });
        if (!validation.success) {
            return {
                success: false,
                error: validation.error.errors[0].message,
            };
        }
        const response = await serverMutation<ServerNotification>(
            `/notifications/${notificationId}/read`,
            {},
            "POST",
        );

        // Revalidate the notifications path to update the UI
        revalidatePath("/app/notifications");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Error marking notification as read:", error);

        // Handle specific API errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error: apiError.message || "Échec du marquage de la notification comme lue",
            };
        }

        return {
            success: false,
            error: "Échec du marquage de la notification comme lue",
        };
    }
}

export async function markAllNotificationsAsReadAction(): Promise<ActionResult<any>> {
    try {
        const response = await serverMutation<any>("/notifications/mark-all-read", {}, "POST");

        // Revalidate the notifications path to update the UI
        revalidatePath("/app/notifications");
        revalidateTag("notifications");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Error marking all notifications as read:", error);

        // Handle specific API errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error:
                    apiError.message || "Échec du marquage de toutes les notifications comme lues",
            };
        }

        return {
            success: false,
            error: "Échec du marquage de toutes les notifications comme lues",
        };
    }
}

export async function deleteNotificationAction(
    notificationId: string,
): Promise<ActionResult<ServerNotification>> {
    try {
        // Validate notification ID
        const validation = markNotificationSchema.safeParse({ notificationId });
        if (!validation.success) {
            return {
                success: false,
                error: validation.error.errors[0].message,
            };
        }
        const response = await serverMutation<ServerNotification>(
            `/notifications/${notificationId}`,
            {},
            "DELETE",
        );

        // Revalidate the notifications path to update the UI
        revalidatePath("/app/notifications");
        revalidateTag("notifications");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Error deleting notification:", error);

        // Handle specific API errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error: apiError.message || "Échec de la suppression de la notification",
            };
        }

        return {
            success: false,
            error: "Échec de la suppression de la notification",
        };
    }
}

export async function clearAllNotificationsAction(): Promise<
    ActionResult<{ message: string; count: number }>
> {
    try {
        const response = await serverMutation<{ message: string; count: number }>(
            "/notifications/clear-all",
            {},
            "DELETE",
        );

        // Revalidate the notifications path to update the UI
        revalidatePath("/app/notifications");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Error clearing all notifications:", error);

        // Handle specific API errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error: apiError.message || "Échec de la suppression de toutes les notifications",
            };
        }

        return {
            success: false,
            error: "Échec de la suppression de toutes les notifications",
        };
    }
}
