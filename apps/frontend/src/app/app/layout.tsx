"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuthContext } from "@/components/providers/auth-provider";
import { useLayoutConfig } from "@/components/providers/layout-provider";
import {
    HomeIcon,
    UserIcon,
    ChatBubbleBottomCenterTextIcon,
    BellIcon,
    ChartBarIcon,
    ShieldCheckIcon,
    Cog6ToothIcon,
} from "@heroicons/react/24/outline";

interface AppLayoutProps {
    children: React.ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
    const pathname = usePathname();
    const { user, profile } = useAuthContext();

    // Configure layout to show header but no footer for protected pages
    useLayoutConfig({
        showHeader: true,
        showFooter: false,
        className: "bg-gray-50",
    });

    const getNavigation = () => {
        const baseNav = [
            {
                name: "<PERSON>au de bord",
                href: "/app",
                icon: HomeIcon,
                current: pathname === "/app",
            },
            {
                name: "Mon profil",
                href: "/app/profile",
                icon: UserIcon,
                current: pathname === "/app/profile",
            },
            {
                name: "Mes expressions",
                href: "/app/my-expressions",
                icon: ChatBubbleBottomCenterTextIcon,
                current: pathname.startsWith("/app/my-expressions"),
            },
            {
                name: "Notifications",
                href: "/app/notifications",
                icon: BellIcon,
                current: pathname === "/app/notifications",
            },
        ];

        // Add admin/validator specific links
        if (profile?.role === "super_admin" || profile?.role === "validateur") {
            baseNav.push(
                {
                    name: "Analytics",
                    href: "/app/analytics",
                    icon: ChartBarIcon,
                    current: pathname === "/app/analytics",
                },
                {
                    name: "Modération",
                    href: "/app/moderation",
                    icon: ShieldCheckIcon,
                    current: pathname === "/app/moderation",
                },
            );
        }

        return baseNav;
    };

    const navigation = getNavigation();

    return (
        <div className="flex h-full">
            {/* Sidebar */}
            <div className="hidden md:flex md:w-64 md:flex-col">
                <div className="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
                    <div className="flex items-center flex-shrink-0 px-4">
                        <div className="flex items-center">
                            <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                <span className="text-white font-bold text-sm">PS</span>
                            </div>
                            <span className="ml-2 text-lg font-semibold text-gray-900">App</span>
                        </div>
                    </div>
                    <div className="mt-5 flex-grow flex flex-col">
                        <nav className="flex-1 px-2 space-y-1">
                            {navigation.map((item) => {
                                const Icon = item.icon;
                                return (
                                    <Link
                                        key={item.name}
                                        href={item.href}
                                        className={`${
                                            item.current
                                                ? "bg-blue-50 border-r-2 border-blue-500 text-blue-700"
                                                : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                        } group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors`}
                                    >
                                        <Icon
                                            className={`${
                                                item.current
                                                    ? "text-blue-500"
                                                    : "text-gray-400 group-hover:text-gray-500"
                                            } mr-3 flex-shrink-0 h-6 w-6`}
                                            aria-hidden="true"
                                        />
                                        {item.name}
                                    </Link>
                                );
                            })}
                        </nav>
                    </div>

                    {/* User info at bottom */}
                    <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
                        <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                <UserIcon className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-700">
                                    {profile?.nom || user?.username}
                                </p>
                                <p className="text-xs text-gray-500">{user?.email}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main content */}
            <div className="flex flex-col flex-1 overflow-hidden">
                <main className="flex-1 relative overflow-y-auto focus:outline-none">
                    <div className="py-6">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">{children}</div>
                    </div>
                </main>
            </div>
        </div>
    );
}
