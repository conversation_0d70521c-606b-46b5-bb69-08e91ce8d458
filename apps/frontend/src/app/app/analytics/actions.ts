"use server";

import { fetchAnalytics } from "@/lib/server-api";
import { ActionResult } from "@/lib/actions";
import { z } from "zod";

// Schema for analytics query parameters
const analyticsQuerySchema = z.object({
    timeRange: z.enum(["week", "month", "year"]).default("month"),
});

export async function getAnalyticsAction(
    timeRange: "week" | "month" | "year" = "month",
): Promise<ActionResult<any>> {
    try {
        // Validate time range
        const validation = analyticsQuerySchema.safeParse({ timeRange });
        if (!validation.success) {
            return {
                success: false,
                error: validation.error.errors[0].message,
            };
        }
        const data = await fetchAnalytics(timeRange);
        return {
            success: true,
            data,
        };
    } catch (error) {
        console.error("Error fetching analytics:", error);
        return {
            success: false,
            error: "Échec de la récupération des statistiques",
        };
    }
}
