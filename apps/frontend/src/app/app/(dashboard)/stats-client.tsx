"use client";

import React from "react";
import { Card } from "@/components/ui/Card";

interface StatsClientProps {
    stats: {
        total_expressions: number;
        by_status: Record<string, number>;
        by_type: Record<string, number>;
        by_urgency: Record<string, number>;
        resolution_rate: number;
    };
}

export function StatsClient({ stats }: StatsClientProps) {
    if (!stats) {
        // Fallback to placeholder stats
        return (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <Card className="p-6">
                    <div className="flex items-center">
                        <div className="p-3 rounded-full bg-blue-100">
                            <svg
                                className="w-6 h-6 text-blue-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">
                                Expressions soumises
                            </p>
                            <p className="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center">
                        <div className="p-3 rounded-full bg-green-100">
                            <svg
                                className="w-6 h-6 text-green-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Taux de résolution</p>
                            <p className="text-2xl font-bold text-gray-900">-</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center">
                        <div className="p-3 rounded-full bg-purple-100">
                            <svg
                                className="w-6 h-6 text-purple-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 10V3L4 14h7v7l9-11h-7z"
                                />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Impact généré</p>
                            <p className="text-2xl font-bold text-gray-900">-</p>
                        </div>
                    </div>
                </Card>
            </div>
        );
    }

    // Show real stats

    const publishedExpressions = stats?.by_status?.publie || 0;
    const resolvedExpressions = stats?.by_status?.resolu || 0;
    const totalExpressions = stats?.total_expressions || 0;
    const resolutionRate = stats?.resolution_rate || 0;

    // Calculate different status counts
    const draftExpressions = stats?.by_status?.brouillon || 0;
    const underReviewExpressions = stats?.by_status?.en_moderation || 0;
    const inProgressExpressions = stats?.by_status?.en_traitement || 0;

    return (
        <div className="space-y-6 mb-8">
            {/* Main Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="p-6">
                    <div className="flex items-center">
                        <div className="p-3 rounded-full bg-blue-100">
                            <svg
                                className="w-6 h-6 text-blue-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Expressions totales</p>
                            <p className="text-2xl font-bold text-gray-900">{totalExpressions}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center">
                        <div className="p-3 rounded-full bg-green-100">
                            <svg
                                className="w-6 h-6 text-green-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Taux de résolution</p>
                            <p className="text-2xl font-bold text-gray-900">
                                {totalExpressions > 0 ? `${Math.round(resolutionRate)}%` : "-"}
                            </p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center">
                        <div className="p-3 rounded-full bg-purple-100">
                            <svg
                                className="w-6 h-6 text-purple-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 10V3L4 14h7v7l9-11h-7z"
                                />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">
                                Expressions résolues
                            </p>
                            <p className="text-2xl font-bold text-gray-900">
                                {resolvedExpressions}
                            </p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Detailed Breakdown */}
            {totalExpressions > 0 && (
                <Card className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Répartition par statut
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-600">
                                {draftExpressions}
                            </div>
                            <div className="text-sm text-gray-500">Brouillons</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-yellow-600">
                                {underReviewExpressions}
                            </div>
                            <div className="text-sm text-gray-500">En modération</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                                {publishedExpressions}
                            </div>
                            <div className="text-sm text-gray-500">Publiées</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                                {inProgressExpressions}
                            </div>
                            <div className="text-sm text-gray-500">En traitement</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-emerald-600">
                                {resolvedExpressions}
                            </div>
                            <div className="text-sm text-gray-500">Résolues</div>
                        </div>
                    </div>
                </Card>
            )}

            {/* Expression Types */}
            {totalExpressions > 0 && Object.keys(stats?.by_type || {}).length > 0 && (
                <Card className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Répartition par type
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Object.entries(stats?.by_type || {}).map(([type, count]) => {
                            const typeLabels: Record<string, string> = {
                                probleme: "Problèmes",
                                satisfaction: "Satisfactions",
                                idee: "Idées",
                                question: "Questions",
                            };
                            const typeColors: Record<string, string> = {
                                probleme: "text-red-600",
                                satisfaction: "text-green-600",
                                idee: "text-blue-600",
                                question: "text-yellow-600",
                            };

                            return (
                                <div key={type} className="text-center">
                                    <div
                                        className={`text-2xl font-bold ${typeColors[type] || "text-gray-600"}`}
                                    >
                                        {count}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {typeLabels[type] || type}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </Card>
            )}
        </div>
    );
}
