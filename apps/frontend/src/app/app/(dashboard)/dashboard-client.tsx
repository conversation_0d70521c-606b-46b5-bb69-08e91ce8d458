"use client";

import { useAuth } from "@/hooks/useAuth";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import Link from "next/link";

function LogoutButton() {
    const { logout } = useAuth();

    return (
        <Button variant="outline" size="sm" onClick={logout}>
            Déconnexion
        </Button>
    );
}

function QuickActions() {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
                <div className="space-y-3">
                    <Button
                        className="w-full justify-start bg-blue-600 hover:bg-blue-700 text-white"
                        as={Link}
                        href="/app/my-expressions/new"
                    >
                        <svg
                            className="w-5 h-5 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 4v16m8-8H4"
                            />
                        </svg>
                        Nouvelle expression
                    </Button>
                    <Button
                        variant="outline"
                        className="w-full justify-start"
                        as={Link}
                        href="/expressions/my-expressions"
                    >
                        <svg
                            className="w-5 h-5 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                            />
                        </svg>
                        Mes expressions
                    </Button>
                    <Button
                        variant="outline"
                        className="w-full justify-start"
                        as={Link}
                        href="/piliers"
                    >
                        <svg
                            className="w-5 h-5 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                            />
                        </svg>
                        Explorer les piliers
                    </Button>
                </div>
            </Card>

            <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité récente</h3>
                <div className="space-y-3">
                    <div className="text-sm text-gray-600">
                        <p>Aucune activité récente</p>
                        <p className="mt-2">
                            Commencez par soumettre votre première expression citoyenne !
                        </p>
                    </div>
                </div>
            </Card>
        </div>
    );
}

function ProfileButton() {
    return (
        <Button variant="outline" as={Link} href="/app/profile">
            Modifier le profil
        </Button>
    );
}

export { LogoutButton, QuickActions, ProfileButton };
