"use client";

import React, { useState, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { validateExpressionAction } from "@/lib/actions";
import { Expression, Pilier, User } from "@/types";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import Layout from "@/components/layout/Layout";
import ExpressionDetailsModal from "@/components/moderation/ExpressionDetailsModal";
import { toast } from "sonner";
import {
    CheckCircleIcon,
    XCircleIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    DocumentTextIcon,
    UserIcon,
    MapPinIcon,
    CalendarIcon,
    ArrowPathIcon,
    ChevronRightIcon,
    ChevronLeftIcon,
} from "@heroicons/react/24/outline";

interface ModerationClientProps {
    user: User;
    initialExpressions: Expression[];
    initialPagination: any;
    initialStats: {
        pending: number;
        approved: number;
        rejected: number;
        todayProcessed: number;
    };
    piliers: Pilier[];
}

export function ModerationClient({
    initialExpressions,
    initialPagination,
    initialStats,
}: ModerationClientProps) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [expressions, setExpressions] = useState<Expression[]>(initialExpressions);
    const [selectedExpression, setSelectedExpression] = useState<Expression | null>(null);
    const [isPending, startTransition] = useTransition();
    const [stats] = useState(initialStats);

    const handleModerateExpression = async (
        expressionId: string,
        decision: "approved" | "rejected",
        notes: string,
    ) => {
        startTransition(async () => {
            const result = await validateExpressionAction(expressionId, decision, notes);

            if (result.success) {
                toast.success(
                    `Expression ${decision === "approved" ? "approuvée" : "rejetée"} avec succès`,
                );

                // Remove the moderated expression from the list
                setExpressions(expressions.filter((exp) => exp.documentId !== expressionId));
                setSelectedExpression(null);

                // Refresh the page to get updated data
                router.refresh();
            } else {
                toast.error(result.error || "Une erreur s'est produite lors de la modération");
            }
        });
    };

    const handlePageChange = (page: number) => {
        const params = new URLSearchParams(searchParams.toString());
        params.set("page", page.toString());
        router.push(`/moderation?${params.toString()}`);
    };

    const getUrgenceColor = (urgence: number) => {
        const colors = {
            1: "bg-gray-100 text-gray-800",
            2: "bg-blue-100 text-blue-800",
            3: "bg-yellow-100 text-yellow-800",
            4: "bg-orange-100 text-orange-800",
            5: "bg-red-100 text-red-800",
        };
        return colors[urgence as keyof typeof colors] || colors[1];
    };

    const getTypeIcon = (type: string) => {
        const icons = {
            probleme: <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />,
            satisfaction: <CheckCircleIcon className="w-5 h-5 text-green-500" />,
            idee: <DocumentTextIcon className="w-5 h-5 text-blue-500" />,
            question: <DocumentTextIcon className="w-5 h-5 text-purple-500" />,
        };
        return icons[type as keyof typeof icons] || <DocumentTextIcon className="w-5 h-5" />;
    };

    const currentPage = parseInt(searchParams.get("page") || "1");
    const totalPages = initialPagination?.pageCount || 1;

    return (
        <Layout showFooter={false}>
            {/* Page Header */}
            <div className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Centre de modération
                            </h1>
                            <p className="mt-1 text-sm text-gray-500">
                                Examinez et validez les expressions citoyennes en attente
                            </p>
                        </div>
                        <Button
                            onClick={() => router.refresh()}
                            variant="outline"
                            disabled={isPending}
                            className="flex items-center"
                        >
                            <ArrowPathIcon
                                className={`w-4 h-4 mr-2 ${isPending ? "animate-spin" : ""}`}
                            />
                            Actualiser
                        </Button>
                    </div>
                </div>
            </div>

            {/* Main Content Container */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">En attente</p>
                                    <p className="text-3xl font-bold text-gray-900 mt-1">
                                        {stats.pending}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">À modérer</p>
                                </div>
                                <div className="p-3 bg-yellow-100 rounded-full">
                                    <ClockIcon className="w-8 h-8 text-yellow-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Approuvées</p>
                                    <p className="text-3xl font-bold text-gray-900 mt-1">
                                        {stats.approved}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">Total validées</p>
                                </div>
                                <div className="p-3 bg-green-100 rounded-full">
                                    <CheckCircleIcon className="w-8 h-8 text-green-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Rejetées</p>
                                    <p className="text-3xl font-bold text-gray-900 mt-1">
                                        {stats.rejected}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">Total refusées</p>
                                </div>
                                <div className="p-3 bg-red-100 rounded-full">
                                    <XCircleIcon className="w-8 h-8 text-red-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Aujourd'hui</p>
                                    <p className="text-3xl font-bold text-gray-900 mt-1">
                                        {stats.todayProcessed}
                                    </p>
                                    <p className="text-xs text-gray-500 mt-1">
                                        Expressions traitées
                                    </p>
                                </div>
                                <div className="p-3 bg-blue-100 rounded-full">
                                    <CalendarIcon className="w-8 h-8 text-blue-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Expressions List */}
                <div>
                    <div className="flex justify-between items-center mb-6">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900">
                                Expressions en attente
                            </h2>
                            <p className="text-sm text-gray-500 mt-1">
                                {expressions.length} expression{expressions.length > 1 ? "s" : ""} à
                                modérer
                            </p>
                        </div>
                        <div className="flex items-center space-x-2">
                            {/* You can add filter buttons here if needed */}
                        </div>
                    </div>

                    {expressions.length === 0 ? (
                        <Card className="shadow-sm">
                            <CardContent className="text-center py-16">
                                <div className="bg-gray-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                                    <DocumentTextIcon className="w-10 h-10 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">
                                    Aucune expression en attente
                                </h3>
                                <p className="text-sm text-gray-500 max-w-sm mx-auto">
                                    Toutes les expressions ont été modérées. Revenez plus tard pour
                                    de nouvelles soumissions.
                                </p>
                            </CardContent>
                        </Card>
                    ) : (
                        <div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {expressions.map((expression) => {
                                    const authorName =
                                        typeof expression.auteur === "string"
                                            ? "Unknown"
                                            : expression.auteur.nom || "Unknown";
                                    const lieu =
                                        typeof expression.lieu === "string"
                                            ? expression.lieu
                                            : expression.lieu?.nom;

                                    return (
                                        <div
                                            key={expression.documentId}
                                            className="group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden"
                                            onClick={() => setSelectedExpression(expression)}
                                        >
                                            {/* Urgency indicator bar */}
                                            <div
                                                className={`absolute top-0 left-0 right-0 h-1 ${
                                                    expression.urgence === 5
                                                        ? "bg-red-500"
                                                        : expression.urgence === 4
                                                          ? "bg-orange-500"
                                                          : expression.urgence === 3
                                                            ? "bg-yellow-500"
                                                            : expression.urgence === 2
                                                              ? "bg-blue-500"
                                                              : "bg-gray-400"
                                                }`}
                                            />

                                            <div className="p-6">
                                                {/* Header with type icon */}
                                                <div className="flex items-start justify-between mb-4">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-gray-200 transition-colors">
                                                            {getTypeIcon(
                                                                expression.type_expression,
                                                            )}
                                                        </div>
                                                        <div>
                                                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                {expression.type_expression}
                                                            </p>
                                                            <p className="text-xs text-gray-400 mt-0.5">
                                                                {new Date(
                                                                    expression.date_creation,
                                                                ).toLocaleDateString("fr-FR")}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <Badge
                                                        className={`${getUrgenceColor(expression.urgence)} text-xs font-medium`}
                                                    >
                                                        Urgence {expression.urgence}
                                                    </Badge>
                                                </div>

                                                {/* Title */}
                                                <h3 className="font-semibold text-gray-900 text-base mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                                                    {expression.titre}
                                                </h3>

                                                {/* Content preview */}
                                                <p className="text-sm text-gray-600 line-clamp-3 mb-4">
                                                    {expression.contenu}
                                                </p>

                                                {/* Footer info */}
                                                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                                                    <div className="flex items-center space-x-3 text-xs text-gray-500">
                                                        <div className="flex items-center">
                                                            <UserIcon className="w-3.5 h-3.5 mr-1 text-gray-400" />
                                                            <span className="truncate max-w-[100px]">
                                                                {authorName}
                                                            </span>
                                                        </div>
                                                        {lieu && (
                                                            <div className="flex items-center">
                                                                <MapPinIcon className="w-3.5 h-3.5 mr-1 text-gray-400" />
                                                                <span className="truncate max-w-[100px]">
                                                                    {lieu}
                                                                </span>
                                                            </div>
                                                        )}
                                                    </div>
                                                    <ChevronRightIcon className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="mt-8 flex justify-center">
                                    <nav className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1 || isPending}
                                            className="flex items-center"
                                        >
                                            <ChevronLeftIcon className="w-4 h-4 mr-1" />
                                            Précédent
                                        </Button>

                                        <div className="flex items-center space-x-1">
                                            {Array.from(
                                                { length: Math.min(5, totalPages) },
                                                (_, i) => {
                                                    let pageNumber;
                                                    if (totalPages <= 5) {
                                                        pageNumber = i + 1;
                                                    } else if (currentPage <= 3) {
                                                        pageNumber = i + 1;
                                                    } else if (currentPage >= totalPages - 2) {
                                                        pageNumber = totalPages - 4 + i;
                                                    } else {
                                                        pageNumber = currentPage - 2 + i;
                                                    }

                                                    return (
                                                        <button
                                                            key={i}
                                                            onClick={() =>
                                                                handlePageChange(pageNumber)
                                                            }
                                                            disabled={isPending}
                                                            className={`px-3 py-1 text-sm rounded-md transition-colors ${
                                                                pageNumber === currentPage
                                                                    ? "bg-blue-600 text-white"
                                                                    : "bg-white text-gray-700 hover:bg-gray-100 border"
                                                            }`}
                                                        >
                                                            {pageNumber}
                                                        </button>
                                                    );
                                                },
                                            )}
                                        </div>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages || isPending}
                                            className="flex items-center"
                                        >
                                            Suivant
                                            <ChevronRightIcon className="w-4 h-4 ml-1" />
                                        </Button>
                                    </nav>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Expression Details Modal */}
            <ExpressionDetailsModal
                expression={selectedExpression}
                isOpen={!!selectedExpression}
                onClose={() => setSelectedExpression(null)}
                onApprove={(expressionId, notes) =>
                    handleModerateExpression(expressionId, "approved", notes)
                }
                onReject={(expressionId, notes) =>
                    handleModerateExpression(expressionId, "rejected", notes)
                }
                isPending={isPending}
            />
        </Layout>
    );
}
