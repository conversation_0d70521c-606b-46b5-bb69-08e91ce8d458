import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchModerationQueue, fetchModerationStats, fetchPiliers } from "@/lib/server-api";
import { ModerationClient } from "./moderation-client";

interface ModerationPageProps {
    searchParams: Promise<{
        page?: string;
        urgence?: string;
        type_expression?: string;
        pilier?: string;
        search?: string;
    }>;
}

export default async function ModerationPage({ searchParams }: ModerationPageProps) {
    const user = await getCurrentUser();
    const params = await searchParams;

    if (!user) {
        redirect("/auth/login");
    }

    // Check if user has moderation access
    if (user.profile?.role !== "super_admin" && user.profile?.role !== "validateur") {
        redirect("/app");
    }

    // Fetch data server-side
    const [moderationData, stats, piliers] = await Promise.all([
        fetchModerationQueue({
            page: params.page ? parseInt(params.page) : 1,
            urgence: params.urgence,
            type_expression: params.type_expression,
            pilier: params.pilier,
            searchQuery: params.search,
        }),
        fetchModerationStats(),
        fetchPiliers(),
    ]);

    return (
        <ModerationClient
            user={user}
            initialExpressions={moderationData.expressions}
            initialPagination={moderationData.pagination}
            initialStats={stats}
            piliers={piliers}
        />
    );
}
