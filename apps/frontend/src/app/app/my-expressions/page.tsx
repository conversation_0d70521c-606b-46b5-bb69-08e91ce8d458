import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchExpressions } from "@/lib/server-api";
import { Expression } from "@/types";
import MyExpressionsClient from "./my-expressions-client";

export const revalidate = 60; // Revalidate every minute

export default async function MyExpressionsPage() {
    const user = await getCurrentUser();

    if (!user) {
        redirect("/auth/login");
    }

    // Fetch user's expressions
    let expressions: Expression[] = [];

    try {
        // Fetch expressions filtered by current user on the server
        expressions = await fetchExpressions({
            myExpressions: "true", // Use server-side filtering for current user
            pageSize: 25, // Use reasonable page size since filtering is done server-side
            page: 1,
        });
    } catch (error) {
        console.error("Error fetching user expressions:", error);
    }

    return <MyExpressionsClient initialExpressions={expressions} user={user} />;
}
