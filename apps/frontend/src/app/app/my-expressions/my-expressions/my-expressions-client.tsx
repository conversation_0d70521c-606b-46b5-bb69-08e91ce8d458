"use client";

import React, { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import Badge from "@/components/ui/Badge";
import Link from "next/link";
import { Expression, User } from "@/types";
import { deleteExpressionAction, submitExpressionAction } from "@/lib/actions";
import { toast } from "react-hot-toast";
import { cn } from "@/utils";
import Logo from "@/components/ui/Logo";

interface MyExpressionsClientProps {
    initialExpressions: Expression[];
    user: User;
}

export default function MyExpressionsClient({ initialExpressions }: MyExpressionsClientProps) {
    const router = useRouter();
    const [expressions, setExpressions] = useState<Expression[]>(initialExpressions);
    const [filter, setFilter] = useState<string>("all");
    const [isPending, startTransition] = useTransition();

    const handleDelete = async (expressionId: string) => {
        if (!confirm("Êtes-vous sûr de vouloir supprimer cette expression ?")) {
            return;
        }

        startTransition(async () => {
            const result = await deleteExpressionAction(expressionId);

            if (result.success) {
                // Remove from local state
                setExpressions((prev) => prev.filter((exp) => exp.documentId !== expressionId));
                toast.success("Expression supprimée avec succès");
                router.refresh();
            } else {
                toast.error(result.error || "Erreur lors de la suppression");
            }
        });
    };

    const handleSubmit = async (expressionId: string) => {
        startTransition(async () => {
            const result = await submitExpressionAction(expressionId);

            if (result.success) {
                // Update local state
                setExpressions((prev) =>
                    prev.map((exp) =>
                        exp.documentId === expressionId
                            ? { ...exp, statut: "en_moderation" as const }
                            : exp,
                    ),
                );
                toast.success("Expression soumise pour modération");
                router.refresh();
            } else {
                toast.error(result.error || "Erreur lors de la soumission");
            }
        });
    };

    const filteredExpressions = expressions.filter((expression) => {
        if (filter === "all") return true;
        return expression.statut === filter;
    });

    const getStatusColor = (status: string) => {
        const colors = {
            brouillon: "secondary",
            en_moderation: "warning",
            publie: "success",
            en_traitement: "info",
            resolu: "primary",
            rejete: "destructive",
        };
        return colors[status as keyof typeof colors] || "secondary";
    };

    const getStatusLabel = (status: string) => {
        const labels = {
            brouillon: "Brouillon",
            en_moderation: "En modération",
            publie: "Publié",
            en_traitement: "En traitement",
            resolu: "Résolu",
            rejete: "Rejeté",
        };
        return labels[status as keyof typeof labels] || status;
    };

    const getTypeLabel = (type: string) => {
        const labels = {
            probleme: "Problème",
            satisfaction: "Satisfaction",
            idee: "Idée",
            question: "Question",
        };
        return labels[type as keyof typeof labels] || type;
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <Logo href="/app" />

                            <span className="ml-4 text-gray-500">Mes Expressions</span>
                        </div>
                        <Button variant="primary" as={Link} href="/expressions/new">
                            Nouvelle expression
                        </Button>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Filter Tabs */}
                <div className="mb-8">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8">
                            {[
                                { value: "all", label: "Toutes" },
                                { value: "brouillon", label: "Brouillons" },
                                { value: "en_moderation", label: "En modération" },
                                { value: "publie", label: "Publiées" },
                                { value: "rejete", label: "Rejetées" },
                            ].map((tab) => (
                                <button
                                    key={tab.value}
                                    onClick={() => setFilter(tab.value)}
                                    className={cn(
                                        `py-2 px-1 border-b-2 font-medium text-sm transition-colors`,
                                        filter === tab.value
                                            ? "border-blue-500 text-blue-600"
                                            : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                                    )}
                                    disabled={isPending}
                                >
                                    {tab.label}
                                    {tab.value !== "all" && (
                                        <span className="ml-2 text-gray-400">
                                            (
                                            {
                                                expressions.filter((e) => e.statut === tab.value)
                                                    .length
                                            }
                                            )
                                        </span>
                                    )}
                                </button>
                            ))}
                        </nav>
                    </div>
                </div>

                {/* Expressions List */}
                {filteredExpressions.length === 0 ? (
                    <Card className="p-8 text-center">
                        <p className="text-gray-500 mb-4">
                            {filter === "all"
                                ? "Vous n'avez pas encore créé d'expression."
                                : `Aucune expression ${getStatusLabel(filter).toLowerCase()}.`}
                        </p>
                        <Button variant="primary" as={Link} href="/expressions/new">
                            Créer ma première expression
                        </Button>
                    </Card>
                ) : (
                    <div className="grid gap-6">
                        {filteredExpressions.map((expression) => (
                            <Card key={expression.documentId} className="overflow-hidden">
                                <div className="p-6">
                                    <div className="flex justify-between items-start mb-4">
                                        <div className="flex-1">
                                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                                {expression.titre}
                                            </h3>
                                            <div className="flex flex-wrap gap-2 mb-3">
                                                <Badge
                                                    variant={
                                                        getStatusColor(expression.statut) as any
                                                    }
                                                >
                                                    {getStatusLabel(expression.statut)}
                                                </Badge>
                                                <Badge variant="outline">
                                                    {getTypeLabel(expression.type_expression)}
                                                </Badge>
                                                <Badge variant="outline">
                                                    Urgence {expression.urgence}/5
                                                </Badge>
                                            </div>
                                            <p className="text-gray-600 line-clamp-2">
                                                {expression.contenu}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center justify-between text-sm text-gray-500">
                                        <div className="space-x-4">
                                            <span>
                                                Créée le{" "}
                                                {new Date(
                                                    expression.date_creation,
                                                ).toLocaleDateString("fr-FR")}
                                            </span>
                                            {expression.impact && (
                                                <>
                                                    <span>👁 {expression.impact.vues}</span>
                                                    <span>❤️ {expression.impact.soutiens}</span>
                                                    <span>💬 {expression.impact.commentaires}</span>
                                                </>
                                            )}
                                        </div>
                                    </div>

                                    <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between items-center">
                                        <div className="space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                href={`/expressions/${expression.documentId}`}
                                                as={Link}
                                            >
                                                Voir
                                            </Button>
                                            {expression.statut === "brouillon" && (
                                                <>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        href={`/expressions/${expression.documentId}/edit`}
                                                        as={Link}
                                                    >
                                                        Modifier
                                                    </Button>
                                                    <Button
                                                        variant="primary"
                                                        size="sm"
                                                        onClick={() =>
                                                            handleSubmit(expression.documentId)
                                                        }
                                                        disabled={isPending}
                                                    >
                                                        Soumettre
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                        {expression.statut === "brouillon" && (
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => handleDelete(expression.documentId)}
                                                disabled={isPending}
                                            >
                                                Supprimer
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                )}
            </main>
        </div>
    );
}
