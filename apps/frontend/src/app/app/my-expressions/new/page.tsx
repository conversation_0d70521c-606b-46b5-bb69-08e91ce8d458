import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth/server";
import { fetchPiliers, fetchSousPiliers, fetchEntites } from "@/lib/server-api";
import { NewExpressionClient } from "./new-expression-client";

interface NewExpressionPageProps {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function NewExpressionPage({ searchParams }: NewExpressionPageProps) {
    const user = await getCurrentUser();

    if (!user) {
        redirect("/auth/login");
    }

    const params = await searchParams;
    const initialPilierId = params.pilier as string | undefined;

    // Fetch all data needed for the form
    const [piliers, sousPiliers, entites] = await Promise.all([
        fetchPiliers(),
        fetchSousPiliers(),
        fetchEntites(),
    ]);

    return (
        <NewExpressionClient
            user={user}
            piliers={piliers}
            sousPiliers={sousPiliers}
            entites={entites}
            initialPilierId={initialPilierId}
        />
    );
}
