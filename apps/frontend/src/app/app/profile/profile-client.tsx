"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";
import LocationPicker from "@/components/maps/LocationPicker";
import { updateProfileAction } from "@/lib/actions";
import { Lieu, User } from "@/types";
import {
    UserIcon,
    PencilIcon,
    CheckIcon,
    XMarkIcon,
    ShieldCheckIcon,
    MapPinIcon,
    CalendarIcon,
    EnvelopeIcon,
} from "@heroicons/react/24/outline";
import { toast } from "sonner";
import Layout from "@/components/layout/Layout";

interface ProfileClientProps {
    user: User;
}

export function ProfileClient({ user }: ProfileClientProps) {
    const router = useRouter();
    const profile = user.profile;
    const [isEditing, setIsEditing] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const [formData, setFormData] = useState({
        nom: profile?.nom || "",
        telephone: profile?.telephone || "",
        date_naissance: profile?.date_naissance || "",
        genre: (profile?.genre || "M") as "M" | "F" | "Autre",
        lieu_residence: profile?.lieu_residence || (null as Lieu | null),
        preferences: {
            notifications_email: profile?.preferences?.notifications_email ?? true,
            notifications_push: profile?.preferences?.notifications_push ?? true,
            langue: profile?.preferences?.langue || "fr",
            theme: profile?.preferences?.theme || "auto",
        },
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        if (name.startsWith("preferences.")) {
            const prefKey = name.split(".")[1];
            setFormData((prev) => ({
                ...prev,
                preferences: {
                    ...prev.preferences,
                    [prefKey]: value,
                },
            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                [name]: value,
            }));
        }
    };

    const handleToggleChange = (name: string) => {
        if (name.startsWith("preferences.")) {
            const prefKey = name.split(".")[1];
            setFormData((prev) => ({
                ...prev,
                preferences: {
                    ...prev.preferences,
                    [prefKey]: !prev.preferences[prefKey as keyof typeof prev.preferences],
                },
            }));
        }
    };

    const handleCancel = () => {
        setIsEditing(false);
        // Reset form data
        setFormData({
            nom: profile?.nom || "",
            telephone: profile?.telephone || "",
            date_naissance: profile?.date_naissance || "",
            genre: (profile?.genre || "M") as "M" | "F" | "Autre",
            lieu_residence: profile?.lieu_residence || null,
            preferences: {
                notifications_email: profile?.preferences?.notifications_email ?? true,
                notifications_push: profile?.preferences?.notifications_push ?? true,
                langue: profile?.preferences?.langue || "fr",
                theme: profile?.preferences?.theme || "auto",
            },
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            // Create FormData for server action
            const formDataToSend = new FormData();
            formDataToSend.set("nom", formData.nom);
            if (formData.telephone) formDataToSend.set("telephone", formData.telephone);
            if (formData.date_naissance)
                formDataToSend.set("date_naissance", formData.date_naissance);
            formDataToSend.set("genre", formData.genre);
            if (formData.lieu_residence?.documentId)
                formDataToSend.set("lieu_residence", formData.lieu_residence.documentId);
            formDataToSend.set(
                "notifications_email",
                String(formData.preferences.notifications_email),
            );
            formDataToSend.set(
                "notifications_push",
                String(formData.preferences.notifications_push),
            );
            formDataToSend.set("langue", formData.preferences.langue);
            formDataToSend.set("theme", formData.preferences.theme);

            const result = await updateProfileAction(formDataToSend);

            if (result.success) {
                toast.success("Profil mis à jour avec succès !");
                setIsEditing(false);
                // Refresh the page to get updated data
                router.refresh();
            } else {
                toast.error(result.error || "Erreur lors de la mise à jour du profil");
            }
        } catch (err: any) {
            toast.error(err.message || "Erreur lors de la mise à jour du profil");
        } finally {
            setIsLoading(false);
        }
    };

    const roleColors = {
        contributeur: "bg-blue-100 text-blue-800",
        validateur: "bg-green-100 text-green-800",
        admin: "bg-purple-100 text-purple-800",
    };

    return (
        <Layout showHeader={true} showFooter={false}>
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <div className="bg-white/20 p-3 rounded-full">
                                <UserIcon className="h-12 w-12" />
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold">
                                    {profile?.nom || user.username}
                                </h1>
                                <p className="text-blue-100">@{user.username}</p>
                            </div>
                        </div>
                        {!isEditing ? (
                            <Button
                                onClick={() => setIsEditing(true)}
                                variant="secondary"
                                className="bg-white text-blue-600 hover:bg-blue-50"
                            >
                                <PencilIcon className="h-4 w-4 mr-2" />
                                Modifier le profil
                            </Button>
                        ) : (
                            <div className="flex space-x-2">
                                <Button
                                    onClick={handleCancel}
                                    variant="outline"
                                    className="border-white text-white hover:bg-white/20"
                                >
                                    <XMarkIcon className="h-4 w-4 mr-2" />
                                    Annuler
                                </Button>
                                <Button
                                    onClick={handleSubmit}
                                    variant="secondary"
                                    className="bg-white text-blue-600 hover:bg-blue-50"
                                    loading={isLoading}
                                >
                                    <CheckIcon className="h-4 w-4 mr-2" />
                                    Enregistrer
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Profile Info */}
                        <div className="lg:col-span-2 space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informations personnelles</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Nom complet
                                            </label>
                                            {isEditing ? (
                                                <Input
                                                    name="nom"
                                                    value={formData.nom}
                                                    onChange={handleInputChange}
                                                    placeholder="Votre nom complet"
                                                    required
                                                />
                                            ) : (
                                                <p className="text-gray-900">
                                                    {profile?.nom || "Non renseigné"}
                                                </p>
                                            )}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Téléphone
                                            </label>
                                            {isEditing ? (
                                                <Input
                                                    name="telephone"
                                                    value={formData.telephone}
                                                    onChange={handleInputChange}
                                                    placeholder="+33 6 00 00 00 00"
                                                />
                                            ) : (
                                                <p className="text-gray-900">
                                                    {profile?.telephone || "Non renseigné"}
                                                </p>
                                            )}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Date de naissance
                                            </label>
                                            {isEditing ? (
                                                <Input
                                                    type="date"
                                                    name="date_naissance"
                                                    value={formData.date_naissance}
                                                    onChange={handleInputChange}
                                                />
                                            ) : (
                                                <p className="text-gray-900">
                                                    {profile?.date_naissance
                                                        ? new Date(
                                                              profile.date_naissance,
                                                          ).toLocaleDateString("fr-FR")
                                                        : "Non renseigné"}
                                                </p>
                                            )}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Genre
                                            </label>
                                            {isEditing ? (
                                                <select
                                                    name="genre"
                                                    value={formData.genre}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                >
                                                    <option value="M">Masculin</option>
                                                    <option value="F">Féminin</option>
                                                    <option value="Autre">Autre</option>
                                                </select>
                                            ) : (
                                                <p className="text-gray-900">
                                                    {profile?.genre === "M"
                                                        ? "Masculin"
                                                        : profile?.genre === "F"
                                                          ? "Féminin"
                                                          : profile?.genre || "Non renseigné"}
                                                </p>
                                            )}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Lieu de résidence
                                            </label>
                                            {isEditing ? (
                                                <LocationPicker
                                                    defaultValue={
                                                        formData.lieu_residence || undefined
                                                    }
                                                    onLocationSelect={(location) => {
                                                        if (location) {
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                lieu_residence: {
                                                                    documentId: location.documentId,
                                                                    nom: location.nom,
                                                                    coordonnees: {
                                                                        lat: location.coordonnees
                                                                            .lat,
                                                                        lng: location.coordonnees
                                                                            .lng,
                                                                    },
                                                                    type: "zone" as const,
                                                                    niveau: "ville" as const,
                                                                    pays: "FR",
                                                                    actif: true,
                                                                    verifie: true,
                                                                },
                                                            }));
                                                        } else {
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                lieu_residence: null,
                                                            }));
                                                        }
                                                    }}
                                                    placeholder="Rechercher une ville..."
                                                />
                                            ) : (
                                                <p className="text-gray-900">
                                                    {profile?.lieu_residence?.nom ||
                                                        "Non renseigné"}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Préférences</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Langue
                                            </label>
                                            {isEditing ? (
                                                <select
                                                    name="preferences.langue"
                                                    value={formData.preferences.langue}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                >
                                                    <option value="fr">Français</option>
                                                    <option value="en">English</option>
                                                </select>
                                            ) : (
                                                <p className="text-gray-900">
                                                    {formData.preferences.langue === "fr"
                                                        ? "Français"
                                                        : "English"}
                                                </p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <label className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-700">
                                                    Notifications par email
                                                </span>
                                                {isEditing ? (
                                                    <input
                                                        type="checkbox"
                                                        checked={
                                                            formData.preferences.notifications_email
                                                        }
                                                        onChange={() =>
                                                            handleToggleChange(
                                                                "preferences.notifications_email",
                                                            )
                                                        }
                                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                    />
                                                ) : (
                                                    <span
                                                        className={`text-sm ${
                                                            formData.preferences.notifications_email
                                                                ? "text-green-600"
                                                                : "text-gray-500"
                                                        }`}
                                                    >
                                                        {formData.preferences.notifications_email
                                                            ? "Activées"
                                                            : "Désactivées"}
                                                    </span>
                                                )}
                                            </label>

                                            <label className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-700">
                                                    Notifications push
                                                </span>
                                                {isEditing ? (
                                                    <input
                                                        type="checkbox"
                                                        checked={
                                                            formData.preferences.notifications_push
                                                        }
                                                        onChange={() =>
                                                            handleToggleChange(
                                                                "preferences.notifications_push",
                                                            )
                                                        }
                                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                    />
                                                ) : (
                                                    <span
                                                        className={`text-sm ${
                                                            formData.preferences.notifications_push
                                                                ? "text-green-600"
                                                                : "text-gray-500"
                                                        }`}
                                                    >
                                                        {formData.preferences.notifications_push
                                                            ? "Activées"
                                                            : "Désactivées"}
                                                    </span>
                                                )}
                                            </label>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informations du compte</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                            <ShieldCheckIcon className="h-5 w-5 text-gray-400" />
                                            <span className="text-sm text-gray-600">Rôle :</span>
                                            <Badge
                                                className={
                                                    roleColors[
                                                        profile?.role as keyof typeof roleColors
                                                    ] || roleColors.contributeur
                                                }
                                            >
                                                {profile?.role || "contributeur"}
                                            </Badge>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                                            <span className="text-sm text-gray-600">Email :</span>
                                            <span className="text-sm text-gray-900">
                                                {user.email}
                                            </span>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <MapPinIcon className="h-5 w-5 text-gray-400" />
                                            <span className="text-sm text-gray-600">
                                                Localisation :
                                            </span>
                                            <span className="text-sm text-gray-900">
                                                {profile?.lieu_residence?.nom || "Non renseigné"}
                                            </span>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <CalendarIcon className="h-5 w-5 text-gray-400" />
                                            <span className="text-sm text-gray-600">
                                                Membre depuis :
                                            </span>
                                            <span className="text-sm text-gray-900">
                                                {new Date(user.createdAt).toLocaleDateString(
                                                    "fr-FR",
                                                )}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Statistiques</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div>
                                            <div className="flex justify-between items-baseline">
                                                <span className="text-sm text-gray-600">
                                                    Expressions soumises
                                                </span>
                                                <span className="text-2xl font-bold text-gray-900">
                                                    {profile?.nb_expressions || 0}
                                                </span>
                                            </div>
                                        </div>

                                        <div>
                                            <div className="flex justify-between items-baseline">
                                                <span className="text-sm text-gray-600">
                                                    Score de confiance
                                                </span>
                                                <span className="text-2xl font-bold text-green-600">
                                                    {profile?.score_reputation || 0}
                                                </span>
                                            </div>
                                        </div>

                                        {profile?.role === "validateur" && (
                                            <div>
                                                <div className="flex justify-between items-baseline">
                                                    <span className="text-sm text-gray-600">
                                                        Validations effectuées
                                                    </span>
                                                    <span className="text-2xl font-bold text-blue-600">
                                                        {0}
                                                    </span>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </form>
            </div>
        </Layout>
    );
}
