import React from "react";
import Link from "next/link";

import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    ChatBubbleBottomCenterTextIcon,
    CpuChipIcon,
    CheckBadgeIcon,
    ClockIcon,
    MapPinIcon,
    BellIcon,
    ChartBarIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";

export default function HowItWorksPage() {
    const steps = [
        {
            number: "1",
            title: "Exprimez-vous",
            description: "Partagez votre expérience concrète sur l'un des 12 piliers",
            details:
                "Soyez factuel, localisé, daté. Plus votre expression est précise, plus l'impact sera rapide.",
            icon: <ChatBubbleBottomCenterTextIcon className="h-8 w-8" />,
            time: "30 secondes",
            color: "bg-blue-500",
        },
        {
            number: "2",
            title: "IA + Modération",
            description: "Notre IA française analyse et classe automatiquement",
            details:
                "Classification par pilier, urgence, localisation. Des validateurs qualifiés vérifient la qualité.",
            icon: <CpuChipIcon className="h-8 w-8" />,
            time: "2-5 minutes",
            color: "bg-purple-500",
        },
        {
            number: "3",
            title: "Transmission automatique",
            description: "Les bons acteurs sont alertés instantanément",
            details:
                "Services publics, élus, entreprises concernées reçoivent une notification avec le contexte.",
            icon: <BellIcon className="h-8 w-8" />,
            time: "Instantané",
            color: "bg-orange-500",
        },
        {
            number: "4",
            title: "Action trackée",
            description: "Vous suivez l'évolution jusqu'à la résolution",
            details: "Timeline publique, notifications de progression, mesure de l'impact final.",
            icon: <CheckBadgeIcon className="h-8 w-8" />,
            time: "Suivi continu",
            color: "bg-green-500",
        },
    ];

    return (
        <>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Comment ça marche
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            De la frustration à l'action
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Découvrez comment PillarScan transforme votre culture de plainte en
                            culture d'action collective avec notre processus révolutionnaire.
                        </p>
                        <div className="flex items-center justify-center space-x-8 text-lg">
                            <div className="flex items-center">
                                <ClockIcon className="h-6 w-6 mr-2" />
                                <span>Rapide</span>
                            </div>
                            <div className="flex items-center">
                                <MapPinIcon className="h-6 w-6 mr-2" />
                                <span>Localisé</span>
                            </div>
                            <div className="flex items-center">
                                <ChartBarIcon className="h-6 w-6 mr-2" />
                                <span>Mesurable</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Process Steps */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Le processus en 4 étapes
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Un système simple, efficace et transparent pour transformer vos
                            préoccupations en améliorations concrètes.
                        </p>
                    </div>

                    <div className="space-y-12">
                        {steps.map((step, index) => (
                            <div
                                key={step.number}
                                className={`flex flex-col lg:flex-row items-center gap-8 ${
                                    index % 2 === 1 ? "lg:flex-row-reverse" : ""
                                }`}
                            >
                                <div className="flex-1">
                                    <Card hover variant="elevated" className="p-8">
                                        <div className="flex items-center mb-6">
                                            <div
                                                className={`w-16 h-16 ${step.color} rounded-full flex items-center justify-center text-white mr-4`}
                                            >
                                                {step.icon}
                                            </div>
                                            <div>
                                                <Badge variant="outline" className="mb-2">
                                                    Étape {step.number}
                                                </Badge>
                                                <h3 className="text-2xl font-bold text-foreground">
                                                    {step.title}
                                                </h3>
                                            </div>
                                        </div>
                                        <p className="text-lg text-muted-foreground mb-4">
                                            {step.description}
                                        </p>
                                        <p className="text-muted-foreground mb-4">{step.details}</p>
                                        <Badge variant="info" size="sm">
                                            ⏱️ {step.time}
                                        </Badge>
                                    </Card>
                                </div>
                                <div className="flex-shrink-0">
                                    <div className="w-24 h-24 bg-gradient-to-br from-primary to-info rounded-full flex items-center justify-center text-white text-3xl font-bold shadow-lg">
                                        {step.number}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Example Section */}
            <section className="civic-section bg-secondary/30">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="success" size="lg" className="mb-4">
                            Exemple concret
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Sophie et les poubelles de Belleville
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Découvrez comment une simple expression a résolu un problème qui durait
                            depuis des semaines.
                        </p>
                    </div>

                    <div className="max-w-4xl mx-auto">
                        <Card variant="elevated" className="p-8">
                            <div className="space-y-6">
                                <div className="border-l-4 border-primary pl-6">
                                    <div className="text-sm text-muted-foreground mb-2">
                                        Lundi 14h32 - Expression initiale
                                    </div>
                                    <p className="text-lg">
                                        "Les poubelles de la rue de Belleville entre le 129 et 145
                                        n'ont pas été ramassées depuis lundi. Ça sent mauvais et
                                        attire les rats. Photo jointe."
                                    </p>
                                </div>

                                <div className="border-l-4 border-purple-500 pl-6">
                                    <div className="text-sm text-muted-foreground mb-2">
                                        Lundi 14h34 - Analyse IA
                                    </div>
                                    <p>
                                        Classification : CADRE DE VIE • Urgence : MOYENNE •
                                        Localisation : Paris 19ème • 23 expressions similaires
                                        détectées
                                    </p>
                                </div>

                                <div className="border-l-4 border-orange-500 pl-6">
                                    <div className="text-sm text-muted-foreground mb-2">
                                        Lundi 14h45 - Transmission
                                    </div>
                                    <p>
                                        Notifications envoyées : Propreté Paris 19ème, Mairie du
                                        19ème, Véolia, Élu référent Belleville
                                    </p>
                                </div>

                                <div className="border-l-4 border-green-500 pl-6">
                                    <div className="text-sm text-muted-foreground mb-2">
                                        Lundi 18h50 - Résolution
                                    </div>
                                    <p>
                                        ✅ Collecte effectuée • 145 habitants impactés • Temps de
                                        résolution : 4h (vs 5 jours moyenne) • Satisfaction : 94%
                                    </p>
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <Badge variant="warning" size="lg" className="mb-4">
                            Résultats
                        </Badge>
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            L'impact de PillarScan
                        </h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <Card hover className="p-8 text-center">
                            <div className="text-4xl font-bold text-primary mb-4">-70%</div>
                            <h3 className="text-xl font-bold mb-4 text-foreground">
                                Temps de résolution
                            </h3>
                            <p className="text-muted-foreground">
                                Les problèmes sont résolus 70% plus rapidement grâce à la
                                transmission automatique aux bons acteurs.
                            </p>
                        </Card>

                        <Card hover className="p-8 text-center">
                            <div className="text-4xl font-bold text-primary mb-4">x10</div>
                            <h3 className="text-xl font-bold mb-4 text-foreground">
                                Participation citoyenne
                            </h3>
                            <p className="text-muted-foreground">
                                Les citoyens s'expriment 10 fois plus quand ils voient que leur voix
                                a un impact réel.
                            </p>
                        </Card>

                        <Card hover className="p-8 text-center">
                            <div className="text-4xl font-bold text-primary mb-4">+40%</div>
                            <h3 className="text-xl font-bold mb-4 text-foreground">
                                Confiance institutionnelle
                            </h3>
                            <p className="text-muted-foreground">
                                La transparence et l'efficacité restaurent la confiance entre
                                citoyens et institutions.
                            </p>
                        </Card>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="civic-section civic-gradient text-white">
                <div className="civic-container text-center">
                    <UserGroupIcon className="h-16 w-16 mx-auto mb-6 opacity-80" />
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Prêt à faire partie du changement ?
                    </h2>
                    <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                        Rejoignez des milliers de citoyens français qui transforment déjà leur
                        quotidien avec PillarScan.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="primary" href="/auth/register" as={Link}>
                            Commencer maintenant
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            href="/expressions"
                            as={Link}
                            className="border-white text-white hover:bg-white hover:text-primary"
                        >
                            Voir les expressions
                        </Button>
                    </div>
                </div>
            </section>
        </>
    );
}
