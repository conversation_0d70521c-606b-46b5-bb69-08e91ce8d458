"use server";

import { redirect } from "next/navigation";
import { set<PERSON><PERSON><PERSON><PERSON><PERSON>, removeAuth<PERSON><PERSON><PERSON> } from "@/lib/auth/cookies";
import { revalidatePath } from "next/cache";
import { API_BASE_URL } from "@/lib/constants";
import {
    loginSchema,
    registerSchema,
    type LoginFormValues,
    type RegisterFormValues,
} from "@/lib/schemas/auth";
import { formatZodErrors } from "@/lib/schemas/expression";

interface LoginData {
    identifier: string;
    password: string;
}

interface RegisterData {
    username: string;
    email: string;
    password: string;
}

interface AuthResponse {
    jwt: string;
    user: {
        id: number;
        username: string;
        email: string;
        blocked: boolean;
        confirmed: boolean;
        createdAt: string;
        updatedAt: string;
        profile?: {
            id: string;
            nom: string;
            role: string;
            statut: string;
            compte_verifie: boolean;
            nb_expressions: number;
            score_reputation: number;
        };
    };
}

export async function login(data: LoginData) {
    try {
        // Validate input data
        const validationResult = loginSchema.safeParse(data);

        if (!validationResult.success) {
            const firstError = validationResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(validationResult.error),
            };
        }

        const response = await fetch(`${API_BASE_URL}/auth/local`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(validationResult.data),
            cache: "no-store",
        });

        if (!response.ok) {
            const error = await response.json();
            return {
                success: false,
                error: error.error?.message || "Invalid credentials",
            };
        }

        const authData: AuthResponse = await response.json();

        // Set the authentication cookie
        await setAuthCookie(authData.jwt);

        // Revalidate all paths to ensure the UI updates
        revalidatePath("/", "layout");

        return {
            success: true,
            user: authData.user,
        };
    } catch (error) {
        console.error("Login error:", error);
        return {
            success: false,
            error: "Failed to connect to server",
        };
    }
}

export async function register(data: RegisterData & { confirmPassword?: string }) {
    try {
        // Validate input data
        const validationResult = registerSchema.safeParse(data);

        if (!validationResult.success) {
            const firstError = validationResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(validationResult.error),
            };
        }

        // Remove confirmPassword before sending to API
        const { confirmPassword, ...apiData } = validationResult.data;

        const response = await fetch(`${API_BASE_URL}/auth/local/register`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(apiData),
            cache: "no-store",
        });

        if (!response.ok) {
            const error = await response.json();

            // Handle specific Strapi errors
            if (error.error?.message?.includes("Email or Username are already taken")) {
                return {
                    success: false,
                    error: "Cet email ou nom d'utilisateur est déjà pris",
                    fieldErrors: {
                        email: "Cet email est peut-être déjà utilisé",
                        username: "Ce nom d'utilisateur est peut-être déjà pris",
                    },
                };
            }

            return {
                success: false,
                error: error.error?.message || "Registration failed",
            };
        }

        const authData: AuthResponse = await response.json();

        // Set the authentication cookie
        await setAuthCookie(authData.jwt);

        // Log profile creation status
        if (authData.user.profile) {
            console.log("User registered with profile:", authData.user.profile);
        } else {
            console.log("User registered but profile creation may be pending");
        }

        // Revalidate all paths
        revalidatePath("/", "layout");

        return {
            success: true,
            user: authData.user,
        };
    } catch (error) {
        console.error("Registration error:", error);
        return {
            success: false,
            error: "Failed to connect to server",
        };
    }
}

export async function logout() {
    await removeAuthCookie();
    revalidatePath("/", "layout");
    redirect("/login");
}

export async function refreshToken() {
    // This would typically call a refresh endpoint
    // For now, we'll just check if the user is still valid
    const { getCurrentUser } = await import("@/lib/auth/server");
    const user = await getCurrentUser();

    if (!user) {
        await removeAuthCookie();
        return { success: false };
    }

    return { success: true, user };
}
