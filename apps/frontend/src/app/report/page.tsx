"use client";

import React, { useState } from "react";
import Link from "next/link";
import Layout from "@/components/layout/Layout";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    ExclamationTriangleIcon,
    ShieldExclamationIcon,
    BugAntIcon,
    ChatBubbleBottomCenterTextIcon,
    UserIcon,
    DocumentTextIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";

export default function ReportPage() {
    const [formData, setFormData] = useState({
        type: "",
        url: "",
        description: "",
        steps: "",
        expected: "",
        actual: "",
        browser: "",
        device: "",
        email: "",
        urgent: false,
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);

    const reportTypes = [
        {
            id: "bug",
            title: "Bug technique",
            description: "Problème de fonctionnement du site",
            icon: <BugAntIcon className="h-8 w-8" />,
            color: "bg-red-500",
            examples: ["Page qui ne charge pas", "Erreur lors de l'envoi", "Affichage incorrect"],
        },
        {
            id: "content",
            title: "Contenu inapproprié",
            description: "Expression ou commentaire problématique",
            icon: <ShieldExclamationIcon className="h-8 w-8" />,
            color: "bg-orange-500",
            examples: ["Contenu offensant", "Spam", "Informations fausses"],
        },
        {
            id: "user",
            title: "Comportement utilisateur",
            description: "Utilisateur ne respectant pas les règles",
            icon: <UserIcon className="h-8 w-8" />,
            color: "bg-yellow-500",
            examples: ["Harcèlement", "Usurpation d'identité", "Abus répétés"],
        },
        {
            id: "security",
            title: "Problème de sécurité",
            description: "Faille de sécurité ou données exposées",
            icon: <ExclamationTriangleIcon className="h-8 w-8" />,
            color: "bg-purple-500",
            examples: ["Faille de sécurité", "Données exposées", "Accès non autorisé"],
        },
    ];

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        await new Promise((resolve) => setTimeout(resolve, 2000));

        setIsSubmitted(true);
        setIsSubmitting(false);
    };

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
    ) => {
        const { name, value, type } = e.target;
        setFormData({
            ...formData,
            [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
        });
    };

    if (isSubmitted) {
        return (
            <Layout>
                <section className="civic-section">
                    <div className="civic-container">
                        <div className="max-w-2xl mx-auto text-center">
                            <Card variant="elevated" className="p-12">
                                <div className="w-16 h-16 bg-success rounded-full mx-auto mb-6 flex items-center justify-center">
                                    <ExclamationTriangleIcon className="h-8 w-8 text-white" />
                                </div>
                                <h1 className="text-3xl font-bold text-foreground mb-4">
                                    Signalement envoyé !
                                </h1>
                                <p className="text-lg text-muted-foreground mb-8">
                                    Merci pour votre signalement. Notre équipe va examiner le
                                    problème et prendre les mesures appropriées.
                                </p>
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
                                    <p className="text-blue-800 text-sm">
                                        <strong>Numéro de signalement :</strong> #
                                        {Math.random().toString(36).substr(2, 9).toUpperCase()}
                                        <br />
                                        Conservez ce numéro pour suivre votre signalement.
                                    </p>
                                </div>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                    <Button variant="primary" href="/" as={Link}>
                                        Retour à l'accueil
                                    </Button>
                                    <Button variant="outline" href="/help" as={Link}>
                                        Centre d'aide
                                    </Button>
                                </div>
                            </Card>
                        </div>
                    </div>
                </section>
            </Layout>
        );
    }

    return (
        <Layout>
            {/* Hero Section */}
            <section className="civic-gradient text-white py-16">
                <div className="civic-container">
                    <div className="text-center max-w-4xl mx-auto">
                        <Badge
                            variant="primary"
                            size="lg"
                            className="mb-6 bg-white/20 text-white border-white/30"
                        >
                            Signaler un problème
                        </Badge>
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 font-marianne">
                            Aidez-nous à améliorer PillarScan
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                            Signalez les bugs, contenus inappropriés ou problèmes de sécurité pour
                            maintenir une plateforme de qualité pour tous.
                        </p>
                        <div className="flex items-center justify-center space-x-8 text-lg">
                            <div className="flex items-center">
                                <ClockIcon className="h-6 w-6 mr-2" />
                                <span>Traitement rapide</span>
                            </div>
                            <div className="flex items-center">
                                <ShieldExclamationIcon className="h-6 w-6 mr-2" />
                                <span>Confidentiel</span>
                            </div>
                            <div className="flex items-center">
                                <ExclamationTriangleIcon className="h-6 w-6 mr-2" />
                                <span>Priorité sécurité</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Report Types */}
            <section className="civic-section">
                <div className="civic-container">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                            Quel type de problème souhaitez-vous signaler ?
                        </h2>
                        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                            Sélectionnez la catégorie qui correspond le mieux à votre signalement
                            pour un traitement plus efficace.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {reportTypes.map((type) => (
                            <Card
                                key={type.id}
                                hover
                                interactive
                                className={`p-6 text-center cursor-pointer transition-all ${
                                    formData.type === type.id
                                        ? "ring-2 ring-primary border-primary"
                                        : ""
                                }`}
                                onClick={() => setFormData({ ...formData, type: type.id })}
                            >
                                <div
                                    className={`w-16 h-16 ${type.color} rounded-xl mx-auto mb-4 flex items-center justify-center text-white`}
                                >
                                    {type.icon}
                                </div>
                                <h3 className="text-lg font-bold text-foreground mb-2">
                                    {type.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    {type.description}
                                </p>
                                <div className="text-xs text-muted-foreground">
                                    <strong>Exemples :</strong>
                                    <ul className="mt-1 space-y-1">
                                        {type.examples.map((example, index) => (
                                            <li key={index}>• {example}</li>
                                        ))}
                                    </ul>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>

            {/* Report Form */}
            {formData.type && (
                <section className="civic-section bg-secondary/30">
                    <div className="civic-container">
                        <div className="max-w-4xl mx-auto">
                            <div className="text-center mb-12">
                                <Badge variant="info" size="lg" className="mb-4">
                                    Détails du signalement
                                </Badge>
                                <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                                    Décrivez le problème
                                </h2>
                                <p className="text-xl text-muted-foreground">
                                    Plus vous fournirez de détails, plus nous pourrons traiter
                                    efficacement votre signalement.
                                </p>
                            </div>

                            <Card variant="elevated" className="p-8">
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    {/* URL */}
                                    <div>
                                        <label
                                            htmlFor="url"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            URL de la page concernée
                                        </label>
                                        <input
                                            type="url"
                                            id="url"
                                            name="url"
                                            value={formData.url}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            placeholder="https://pillarscan.fr/..."
                                        />
                                    </div>

                                    {/* Description */}
                                    <div>
                                        <label
                                            htmlFor="description"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            Description du problème *
                                        </label>
                                        <textarea
                                            id="description"
                                            name="description"
                                            required
                                            rows={4}
                                            value={formData.description}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                            placeholder="Décrivez le problème de manière claire et précise..."
                                        />
                                    </div>

                                    {/* Technical details for bugs */}
                                    {formData.type === "bug" && (
                                        <>
                                            <div>
                                                <label
                                                    htmlFor="steps"
                                                    className="block text-sm font-semibold text-foreground mb-2"
                                                >
                                                    Étapes pour reproduire le problème
                                                </label>
                                                <textarea
                                                    id="steps"
                                                    name="steps"
                                                    rows={3}
                                                    value={formData.steps}
                                                    onChange={handleInputChange}
                                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                                    placeholder="1. Je clique sur...&#10;2. Je remplis...&#10;3. J'observe..."
                                                />
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label
                                                        htmlFor="expected"
                                                        className="block text-sm font-semibold text-foreground mb-2"
                                                    >
                                                        Résultat attendu
                                                    </label>
                                                    <textarea
                                                        id="expected"
                                                        name="expected"
                                                        rows={2}
                                                        value={formData.expected}
                                                        onChange={handleInputChange}
                                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                                        placeholder="Ce qui devrait se passer..."
                                                    />
                                                </div>
                                                <div>
                                                    <label
                                                        htmlFor="actual"
                                                        className="block text-sm font-semibold text-foreground mb-2"
                                                    >
                                                        Résultat observé
                                                    </label>
                                                    <textarea
                                                        id="actual"
                                                        name="actual"
                                                        rows={2}
                                                        value={formData.actual}
                                                        onChange={handleInputChange}
                                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                                        placeholder="Ce qui se passe réellement..."
                                                    />
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label
                                                        htmlFor="browser"
                                                        className="block text-sm font-semibold text-foreground mb-2"
                                                    >
                                                        Navigateur
                                                    </label>
                                                    <select
                                                        id="browser"
                                                        name="browser"
                                                        value={formData.browser}
                                                        onChange={handleInputChange}
                                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                                    >
                                                        <option value="">Sélectionnez</option>
                                                        <option value="chrome">Chrome</option>
                                                        <option value="firefox">Firefox</option>
                                                        <option value="safari">Safari</option>
                                                        <option value="edge">Edge</option>
                                                        <option value="other">Autre</option>
                                                    </select>
                                                </div>
                                                <div>
                                                    <label
                                                        htmlFor="device"
                                                        className="block text-sm font-semibold text-foreground mb-2"
                                                    >
                                                        Appareil
                                                    </label>
                                                    <select
                                                        id="device"
                                                        name="device"
                                                        value={formData.device}
                                                        onChange={handleInputChange}
                                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                                    >
                                                        <option value="">Sélectionnez</option>
                                                        <option value="desktop">Ordinateur</option>
                                                        <option value="tablet">Tablette</option>
                                                        <option value="mobile">Mobile</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    {/* Contact email */}
                                    <div>
                                        <label
                                            htmlFor="email"
                                            className="block text-sm font-semibold text-foreground mb-2"
                                        >
                                            Email de contact (optionnel)
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            placeholder="<EMAIL>"
                                        />
                                        <p className="text-sm text-muted-foreground mt-2">
                                            Laissez votre email si vous souhaitez être tenu informé
                                            du traitement de votre signalement.
                                        </p>
                                    </div>

                                    {/* Urgent checkbox */}
                                    {(formData.type === "security" ||
                                        formData.type === "content") && (
                                        <div className="flex items-center">
                                            <input
                                                type="checkbox"
                                                id="urgent"
                                                name="urgent"
                                                checked={formData.urgent}
                                                onChange={handleInputChange}
                                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                            />
                                            <label
                                                htmlFor="urgent"
                                                className="ml-2 text-sm font-semibold text-foreground"
                                            >
                                                Signalement urgent (sécurité ou contenu dangereux)
                                            </label>
                                        </div>
                                    )}

                                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <p className="text-yellow-800 text-sm">
                                            <strong>⚠️ Important :</strong> Les signalements abusifs
                                            ou répétés peuvent entraîner des sanctions. Assurez-vous
                                            que votre signalement est justifié.
                                        </p>
                                    </div>

                                    <div className="flex flex-col sm:flex-row gap-4 justify-end">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => setFormData({ ...formData, type: "" })}
                                        >
                                            Changer de catégorie
                                        </Button>
                                        <Button
                                            type="submit"
                                            variant="primary"
                                            loading={isSubmitting}
                                            disabled={isSubmitting}
                                        >
                                            {isSubmitting
                                                ? "Envoi en cours..."
                                                : "Envoyer le signalement"}
                                        </Button>
                                    </div>
                                </form>
                            </Card>
                        </div>
                    </div>
                </section>
            )}

            {/* Help Section */}
            <section className="civic-section">
                <div className="civic-container text-center">
                    <h2 className="text-2xl font-bold mb-6 text-foreground">
                        Besoin d'aide pour signaler ?
                    </h2>
                    <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                        Si vous n'êtes pas sûr de la catégorie à choisir ou si vous avez besoin
                        d'assistance, consultez notre centre d'aide ou contactez-nous directement.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button variant="outline" size="lg" href="/help" as={Link}>
                            Centre d'aide
                        </Button>
                        <Button variant="outline" size="lg" href="/contact" as={Link}>
                            Nous contacter
                        </Button>
                    </div>
                </div>
            </section>
        </Layout>
    );
}
