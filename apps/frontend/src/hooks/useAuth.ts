"use client";

import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { login, logout, register } from "@/app/actions/auth";
import { toast } from "sonner";

export function useAuth() {
    const router = useRouter();
    const [isPending, startTransition] = useTransition();

    const handleLogin = async (identifier: string, password: string) => {
        startTransition(async () => {
            const result = await login({ identifier, password });

            if (result.success) {
                toast.success("Login successful!");

                // Check if there's a redirect URL in the query params
                const searchParams = new URLSearchParams(window.location.search);
                const from = searchParams.get("from");

                if (from && from.startsWith("/")) {
                    router.push(from);
                } else {
                    router.push("/app");
                }
            } else {
                toast.error(result.error);
            }
        });
    };

    const handleRegister = async (username: string, email: string, password: string) => {
        startTransition(async () => {
            const result = await register({ username, email, password });

            if (result.success) {
                toast.success("Registration successful!");
                router.push("/app");
            } else {
                toast.error(result.error);
            }
        });
    };

    const handleLogout = async () => {
        startTransition(async () => {
            await logout();
            toast.success("Logged out successfully");
        });
    };

    return {
        login: handleLogin,
        register: handleRegister,
        logout: handleLogout,
        isLoading: isPending,
    };
}
