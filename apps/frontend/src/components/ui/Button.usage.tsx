// This file demonstrates proper usage of the polymorphic Button component
// with type safety for Link components requiring href

import Link from "next/link";
import Button from "./Button";

export function ButtonUsageExamples() {
    return (
        <div className="space-y-4">
            {/* Regular button - href is optional */}
            <Button variant="primary" onClick={() => console.log("Clicked!")}>
                Regular Button
            </Button>

            {/* Button as Link - href should be required */}
            <Button as={Link} href="/app" variant="secondary">
                Link with href (required)
            </Button>

            {/* Button as anchor - href should be required */}
            <Button as="a" href="https://example.com" target="_blank" variant="outline">
                Anchor with href (required)
            </Button>

            {/* This should give a TypeScript error if href is missing */}
            {/* 
            <Button as={Link} variant="primary">
                Missing href - should error
            </Button> 
            */}

            {/* Button with all features */}
            <Button
                as={Link}
                href="/app/my-expressions/new"
                variant="success"
                size="lg"
                icon={<span>✨</span>}
                iconPosition="left"
                fullWidth
            >
                Create New Expression
            </Button>
        </div>
    );
}
