import React from "react";
import { cn } from "@/utils";

// Base button props
interface BaseButtonProps {
    variant?:
        | "primary"
        | "secondary"
        | "outline"
        | "ghost"
        | "destructive"
        | "success"
        | "warning"
        | "info";
    size?: "xs" | "sm" | "md" | "lg" | "xl";
    loading?: boolean;
    disabled?: boolean;
    icon?: React.ReactNode;
    iconPosition?: "left" | "right";
    fullWidth?: boolean;
    children: React.ReactNode;
    className?: string;
}

// Polymorphic component props
type PolymorphicProps<E extends React.ElementType> = BaseButtonProps & {
    as?: E;
} & Omit<React.ComponentPropsWithoutRef<E>, keyof BaseButtonProps | "as">;

// Component with proper typing
type ButtonComponent = <E extends React.ElementType = "button">(
    props: PolymorphicProps<E> & { ref?: React.ComponentPropsWithRef<E>["ref"] },
) => React.ReactElement | null;

const Button = <E extends React.ElementType = "button">({
    as,
    variant = "primary",
    size = "md",
    loading = false,
    disabled,
    icon,
    iconPosition = "left",
    fullWidth = false,
    children,
    className,
    ref,
    ...props
}: PolymorphicProps<E> & { ref?: React.ComponentPropsWithRef<E>["ref"] }) => {
    const Component = (as || "button") as React.ElementType;

    const baseClasses =
        "inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background active:scale-[0.98] relative overflow-hidden hover:shadow-md hover:-translate-y-0.5";

    const variants = {
        primary: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md",
        secondary:
            "bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-gray-200",
        outline:
            "border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground",
        ghost: "text-foreground hover:bg-accent hover:text-accent-foreground",
        destructive:
            "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
        success: "bg-success text-success-foreground hover:bg-success/90 shadow-sm hover:shadow-md",
        warning: "bg-warning text-warning-foreground hover:bg-warning/90 shadow-sm hover:shadow-md",
        info: "bg-info text-info-foreground hover:bg-info/90 shadow-sm hover:shadow-md",
    };

    const sizes = {
        xs: "h-8 px-3 text-xs gap-1.5",
        sm: "h-9 px-4 text-sm gap-2",
        md: "h-11 px-5 text-sm gap-2",
        lg: "h-12 px-7 text-base gap-2.5",
        xl: "h-14 px-9 text-lg gap-3",
    };

    const LoadingSpinner = () => (
        <svg
            className="h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
        >
            <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
            />
            <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
        </svg>
    );

    const isButton = Component === "button";
    const isDisabled = disabled || loading;

    // Build component props
    const componentProps: any = {
        className: cn(
            baseClasses,
            variants[variant as keyof typeof variants],
            sizes[size as keyof typeof sizes],
            fullWidth && "w-full",
            "group",
            isDisabled && "opacity-50 cursor-not-allowed pointer-events-none",
            className,
        ),
        ref,
        ...props,
    };

    // Add aria-disabled for accessibility
    if (isDisabled) {
        componentProps["aria-disabled"] = true;
    }

    // Only add disabled attribute for button elements
    if (isButton && isDisabled) {
        componentProps.disabled = true;
    }

    return (
        <Component {...componentProps}>
            {/* Subtle hover effect */}
            <span className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none">
                <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300" />
            </span>

            {/* Button content */}
            <span className="relative z-10 flex items-center justify-center">
                {loading ? (
                    <>
                        <LoadingSpinner />
                        <span className="ml-2">{children}</span>
                    </>
                ) : (
                    <>
                        {icon && iconPosition === "left" && <span className="mr-2">{icon}</span>}
                        {children}
                        {icon && iconPosition === "right" && <span className="ml-2">{icon}</span>}
                    </>
                )}
            </span>
        </Component>
    );
};

export default Button;

// Export type for external use
export type { PolymorphicProps as ButtonProps };
