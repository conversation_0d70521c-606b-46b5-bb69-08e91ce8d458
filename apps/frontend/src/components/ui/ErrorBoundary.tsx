"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import Button from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { ExclamationTriangleIcon, ArrowPathIcon, HomeIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

interface Props {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
    hasError: boolean;
    error: Error | null;
    errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
        };
    }

    static getDerivedStateFromError(error: Error): State {
        return {
            hasError: true,
            error,
            errorInfo: null,
        };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        this.setState({
            error,
            errorInfo,
        });

        // Log error to monitoring service
        console.error("ErrorBoundary caught an error:", error, errorInfo);

        // Call custom error handler if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }

        // In production, you might want to send this to an error reporting service
        if (process.env.NODE_ENV === "production") {
            // Example: Sentry.captureException(error, { extra: errorInfo });
        }
    }

    handleRetry = () => {
        this.setState({
            hasError: false,
            error: null,
            errorInfo: null,
        });
    };

    render() {
        if (this.state.hasError) {
            // Custom fallback if provided
            if (this.props.fallback) {
                return this.props.fallback;
            }

            return (
                <div className="min-h-screen bg-secondary/30 flex items-center justify-center p-4">
                    <Card className="w-full max-w-md p-8 text-center">
                        <div className="w-16 h-16 bg-destructive/10 rounded-full mx-auto mb-6 flex items-center justify-center">
                            <ExclamationTriangleIcon className="h-8 w-8 text-destructive" />
                        </div>

                        <h2 className="text-2xl font-bold text-foreground mb-4">
                            Oops ! Une erreur s'est produite
                        </h2>

                        <p className="text-muted-foreground mb-6">
                            Nous sommes désolés, mais quelque chose s'est mal passé. Veuillez
                            réessayer ou retourner à l'accueil.
                        </p>

                        {process.env.NODE_ENV === "development" && this.state.error && (
                            <details className="mb-6 text-left">
                                <summary className="cursor-pointer text-sm font-medium text-muted-foreground mb-2">
                                    Détails de l'erreur (mode développement)
                                </summary>
                                <div className="bg-destructive/10 p-3 rounded-lg text-xs text-destructive font-mono overflow-auto max-h-32">
                                    <div className="mb-2">
                                        <strong>Message:</strong> {this.state.error.message}
                                    </div>
                                    <div className="mb-2">
                                        <strong>Stack:</strong>
                                        <pre className="mt-1 whitespace-pre-wrap">
                                            {this.state.error.stack}
                                        </pre>
                                    </div>
                                    {this.state.errorInfo && (
                                        <div>
                                            <strong>Component Stack:</strong>
                                            <pre className="mt-1 whitespace-pre-wrap">
                                                {this.state.errorInfo.componentStack}
                                            </pre>
                                        </div>
                                    )}
                                </div>
                            </details>
                        )}

                        <div className="flex flex-col sm:flex-row gap-3">
                            <Button
                                variant="outline"
                                fullWidth
                                onClick={this.handleRetry}
                                icon={<ArrowPathIcon className="h-4 w-4" />}
                            >
                                Réessayer
                            </Button>
                            <Button
                                variant="primary"
                                fullWidth
                                icon={<HomeIcon className="h-4 w-4" />}
                                as={Link}
                                href="/"
                            >
                                Retour à l'accueil
                            </Button>
                        </div>
                    </Card>
                </div>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
