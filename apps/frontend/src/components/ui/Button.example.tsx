// Example usage of the Button component with the 'as' prop
// This demonstrates the polymorphic component pattern with type safety

import Link from "next/link";
import Button from "./Button";

export function ButtonExamples() {
    return (
        <div className="space-y-4">
            {/* Regular button */}
            <Button variant="primary" onClick={() => console.log("Clicked!")}>
                Regular Button
            </Button>

            {/* Button as a Link component - href is required when using Link */}
            <Button as={Link} href="/app" variant="secondary">
                Button as Next.js Link (href required)
            </Button>

            {/* Button as an anchor tag - href is required for anchors */}
            <Button as="a" href="https://example.com" target="_blank" variant="outline">
                Button as Anchor Tag (href required)
            </Button>

            {/* Button with icon */}
            <Button variant="success" icon={<span>✨</span>} iconPosition="left">
                Button with Icon
            </Button>

            {/* Disabled button */}
            <Button variant="destructive" disabled>
                Disabled <PERSON><PERSON>
            </Button>

            {/* Loading button */}
            <Button variant="info" loading>
                Loading...
            </Button>
        </div>
    );
}
