import React from "react";
import { cn } from "@/utils";

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    variant?: "default" | "elevated" | "bordered" | "ghost" | "glass" | "gradient";
    padding?: "none" | "sm" | "md" | "lg" | "xl";
    hover?: boolean;
    interactive?: boolean;
    glow?: boolean;
}

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
}

export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
    children: React.ReactNode;
    as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
    children: React.ReactNode;
}

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
}

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
}

const Card = ({
    className,
    children,
    variant = "default",
    padding = "md",
    hover = false,
    interactive = false,
    glow = false,
    ...props
}: CardProps) => {
    const baseClasses = "rounded-xl text-foreground transition-all duration-200";

    const variants = {
        default: "bg-background border border-gray-200 shadow-sm",
        elevated: "bg-background border border-gray-200 shadow-lg",
        bordered: "bg-background border-2 border-gray-200",
        ghost: "bg-transparent border border-transparent",
        glass: "glass-morphism shadow-lg",
        gradient: "bg-gradient-to-br from-white to-gray-50 border border-gray-200/30 shadow-lg",
    };

    const paddings = {
        none: "",
        sm: "p-4",
        md: "p-6",
        lg: "p-8",
        xl: "p-10",
    };

    const hoverStyles = hover ? "hover:shadow-lg hover:border-primary/20 hover:-translate-y-1" : "";
    const interactiveStyles = interactive ? "cursor-pointer civic-interactive" : "";
    const glowStyles = glow
        ? "relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-primary/20 before:to-info/20 before:blur-xl before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-500 before:-z-10"
        : "";

    return (
        <div
            className={cn(
                baseClasses,
                variants[variant],
                paddings[padding],
                hoverStyles,
                interactiveStyles,
                glowStyles,
                className,
            )}
            {...props}
        >
            {children}
        </div>
    );
};

Card.displayName = "Card";

const CardHeader = ({ className, children, ...props }: CardHeaderProps) => (
    <div className={cn("flex flex-col space-y-1.5 p-6", className)} {...props}>
        {children}
    </div>
);

CardHeader.displayName = "CardHeader";

const CardTitle = ({ className, children, as: Component = "h3", ...props }: CardTitleProps) => (
    <Component
        className={cn("text-xl font-bold leading-none tracking-tight text-foreground", className)}
        {...props}
    >
        {children}
    </Component>
);

CardTitle.displayName = "CardTitle";

const CardDescription = ({ className, children, ...props }: CardDescriptionProps) => (
    <p className={cn("text-sm text-muted-foreground", className)} {...props}>
        {children}
    </p>
);

CardDescription.displayName = "CardDescription";

const CardContent = ({ className, children, ...props }: CardContentProps) => (
    <div className={cn("p-6 pt-0", className)} {...props}>
        {children}
    </div>
);

CardContent.displayName = "CardContent";

const CardFooter = ({ className, children, ...props }: CardFooterProps) => (
    <div className={cn("flex items-center p-6 pt-0", className)} {...props}>
        {children}
    </div>
);

CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
