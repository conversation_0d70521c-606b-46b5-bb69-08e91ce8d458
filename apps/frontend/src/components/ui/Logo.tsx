import React from "react";
import Link from "next/link";

interface LogoProps {
    href?: string;
    showText?: boolean;
    size?: "sm" | "md" | "lg";
    className?: string;
}

const Logo = ({ href = "/", showText = true, size = "md", className = "" }: LogoProps) => {
    const sizeClasses = {
        sm: {
            icon: "h-6 w-6",
            text: "text-lg",
            iconText: "text-xs",
        },
        md: {
            icon: "h-8 w-8",
            text: "text-xl",
            iconText: "text-sm",
        },
        lg: {
            icon: "h-10 w-10",
            text: "text-2xl",
            iconText: "text-base",
        },
    };

    const sizes = sizeClasses[size];

    const logoContent = (
        <>
            <div
                className={`${sizes.icon} rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center`}
            >
                <span className={`text-white font-bold ${sizes.iconText}`}>PS</span>
            </div>
            {showText && (
                <span className={`hidden sm:block ${sizes.text} font-bold text-gray-900`}>
                    PillarScan
                </span>
            )}
        </>
    );

    if (href) {
        return (
            <Link href={href} className={`flex items-center space-x-2 ${className}`}>
                {logoContent}
            </Link>
        );
    }

    return <div className={`flex items-center space-x-2 ${className}`}>{logoContent}</div>;
};

export default Logo;
