"use client";

import React, { useState, useTransition } from "react";
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { useNotificationStore } from "@/stores/notificationStore";
import { toast } from "sonner";
import {
    markNotificationAsReadAction,
    markAllNotificationsAsReadAction,
    deleteNotificationAction,
    clearAllNotificationsAction,
} from "@/app/app/notifications/actions";
import {
    BellIcon,
    CheckIcon,
    XMarkIcon,
    TrashIcon,
    EyeIcon,
    ClockIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { BellIcon as BellIconSolid } from "@heroicons/react/24/solid";
import { ServerNotification } from "@/types";

const getNotificationIcon = (type: ServerNotification["type"]) => {
    switch (type) {
        case "success":
            return <CheckCircleIcon className="h-4 w-4 text-success" />;
        case "warning":
            return <ExclamationTriangleIcon className="h-4 w-4 text-warning" />;
        case "error":
            return <XMarkIcon className="h-4 w-4 text-destructive" />;
        default:
            return <InformationCircleIcon className="h-4 w-4 text-info" />;
    }
};

const getNotificationBg = (type: ServerNotification["type"]) => {
    switch (type) {
        case "success":
            return "bg-success/10 border-success/20";
        case "warning":
            return "bg-warning/10 border-warning/20";
        case "error":
            return "bg-destructive/10 border-destructive/20";
        default:
            return "bg-info/10 border-info/20";
    }
};

const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return "À l'instant";
    if (minutes < 60) return `Il y a ${minutes} min`;
    if (hours < 24) return `Il y a ${hours}h`;
    if (days < 7) return `Il y a ${days}j`;
    return date.toLocaleDateString("fr-FR");
};

export default function NotificationCenter() {
    const { notifications, unreadCount, markAsRead, markAllAsRead, removeNotification, clearAll } =
        useNotificationStore();

    const [isOpen, setIsOpen] = useState(false);
    const [isPending, startTransition] = useTransition();

    const handleNotificationClick = async (notification: ServerNotification) => {
        if (!notification.read) {
            // Update local state immediately
            markAsRead(notification.documentId);

            // If it's a server notification (has expressionId or userId), update server
            if (notification.metadata?.expressionId || notification.metadata?.userId) {
                startTransition(async () => {
                    const result = await markNotificationAsReadAction(notification.documentId);
                    if (!result.success) {
                        // Revert local state on error
                        markAsRead(notification.documentId); // This will toggle it back
                        toast.error(result.error || "Erreur lors du marquage de la notification");
                    }
                });
            }
        }
        setIsOpen(false);
    };

    const handleMarkAllAsRead = async () => {
        // Update local state immediately
        markAllAsRead();

        // Update server notifications
        startTransition(async () => {
            const result = await markAllNotificationsAsReadAction();
            if (!result.success) {
                toast.error(result.error || "Erreur lors du marquage des notifications");
                // Could revert state here if needed
            }
        });
    };

    const handleRemoveNotification = async (notification: ServerNotification) => {
        // Update local state immediately
        removeNotification(notification.documentId);

        // If it's a server notification, delete from server
        if (notification.metadata?.expressionId || notification.metadata?.userId) {
            startTransition(async () => {
                const result = await deleteNotificationAction(notification.documentId);
                if (!result.success) {
                    // Re-add notification on error
                    toast.error(result.error || "Erreur lors de la suppression");
                }
            });
        }
    };

    const handleClearAll = async () => {
        if (!confirm("Êtes-vous sûr de vouloir supprimer toutes les notifications ?")) {
            return;
        }

        // Clear local notifications immediately
        clearAll();

        // Clear server notifications
        startTransition(async () => {
            const result = await clearAllNotificationsAction();
            if (result.success) {
                toast.success(`${result.data?.count || 0} notifications supprimées du serveur`);
            } else {
                toast.error(result.error || "Erreur lors de la suppression des notifications");
            }
        });
    };

    return (
        <div className="relative">
            {/* Notification Bell */}
            <Button
                variant="ghost"
                size="sm"
                className="relative"
                onClick={() => setIsOpen(!isOpen)}
            >
                {unreadCount > 0 ? (
                    <BellIconSolid className="h-5 w-5 text-primary" />
                ) : (
                    <BellIcon className="h-5 w-5" />
                )}

                {unreadCount > 0 && (
                    <div
                        className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background bg-success`}
                    />
                )}
            </Button>

            {/* Notification Dropdown */}
            {isOpen && (
                <>
                    {/* Backdrop */}
                    <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />

                    {/* Dropdown */}
                    <Card className="absolute right-0 top-full mt-2 w-96 max-h-96 overflow-hidden z-50 shadow-2xl animate-civic-fade-in">
                        <CardHeader className="px-4 py-3 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-lg flex items-center gap-2">
                                    Notifications
                                    {isPending && (
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                    )}
                                </CardTitle>
                                <div className="flex items-center gap-2">
                                    {notifications.length > 0 && (
                                        <Button
                                            variant="ghost"
                                            size="xs"
                                            onClick={handleMarkAllAsRead}
                                            icon={<CheckIcon className="h-3 w-3" />}
                                            disabled={isPending}
                                        >
                                            Tout marquer lu
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </CardHeader>

                        <CardContent className="p-0 max-h-60 overflow-y-auto">
                            {notifications.length === 0 ? (
                                <div className="p-8 text-center">
                                    <BellIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-muted-foreground">Aucune notification</p>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        Vous serez notifié des mises à jour importantes.
                                    </p>
                                </div>
                            ) : (
                                <div className="divide-y divide-border">
                                    {notifications.map((notification) => (
                                        <div
                                            key={notification.documentId}
                                            className={`p-4 hover:bg-secondary/50 transition-colors cursor-pointer ${
                                                !notification.read ? "bg-primary/5" : ""
                                            }`}
                                            onClick={() => handleNotificationClick(notification)}
                                        >
                                            <div className="flex items-start gap-3">
                                                <div className="flex-shrink-0 mt-0.5">
                                                    {getNotificationIcon(notification.type)}
                                                </div>

                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-start justify-between">
                                                        <h4 className="text-sm font-medium text-foreground truncate">
                                                            {notification.title}
                                                        </h4>
                                                        <div className="flex items-center gap-1 ml-2">
                                                            {!notification.read && (
                                                                <div className="w-2 h-2 bg-primary rounded-full" />
                                                            )}
                                                            <Button
                                                                variant="ghost"
                                                                size="xs"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleRemoveNotification(
                                                                        notification,
                                                                    );
                                                                }}
                                                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                                                                disabled={isPending}
                                                            >
                                                                <XMarkIcon className="h-3 w-3" />
                                                            </Button>
                                                        </div>
                                                    </div>

                                                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                                        {notification.message}
                                                    </p>

                                                    <div className="flex items-center justify-between mt-2">
                                                        {notification.createdAt && (
                                                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                                                <ClockIcon className="h-3 w-3" />
                                                                {formatTime(
                                                                    new Date(
                                                                        notification.createdAt,
                                                                    ),
                                                                )}
                                                            </div>
                                                        )}

                                                        {notification.metadata?.actionUrl &&
                                                            notification.metadata?.actionLabel && (
                                                                <Link
                                                                    href={
                                                                        notification.metadata
                                                                            ?.actionUrl
                                                                    }
                                                                    onClick={(e) =>
                                                                        e.stopPropagation()
                                                                    }
                                                                    className="text-xs text-primary hover:text-primary/80 font-medium"
                                                                >
                                                                    {
                                                                        notification.metadata
                                                                            ?.actionLabel
                                                                    }
                                                                </Link>
                                                            )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>

                        {notifications.length > 0 && (
                            <div className="p-3 border-t border-gray-200 bg-secondary/30">
                                <div className="flex items-center justify-between">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={handleClearAll}
                                        icon={<TrashIcon className="h-4 w-4" />}
                                        disabled={isPending}
                                    >
                                        Tout effacer
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        as={Link}
                                        href="/app/notifications"
                                        onClick={() => setIsOpen(false)}
                                    >
                                        Voir tout
                                    </Button>
                                </div>
                            </div>
                        )}
                    </Card>
                </>
            )}
        </div>
    );
}
