"use client";

import React from "react";

interface SelectOption {
    value: string;
    label: string;
}

interface SelectProps {
    label?: string;
    value: string;
    onChange: (value: string) => void;
    options: SelectOption[];
    placeholder?: string;
    disabled?: boolean;
    error?: string;
    className?: string;
    name?: string;
    id?: string;
}

const Select = ({
    label,
    value,
    onChange,
    options,
    placeholder = "Sélectionner...",
    disabled = false,
    error,
    className = "",
    name,
    id,
}: SelectProps) => {
    const selectId = id || name || label?.toLowerCase().replace(/\s+/g, "-");

    return (
        <div className={`w-full ${className}`}>
            {label && (
                <label htmlFor={selectId} className="block text-sm font-medium text-gray-700 mb-1">
                    {label}
                </label>
            )}
            <select
                id={selectId}
                name={name}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled}
                className={`
                    w-full px-3 py-2 border rounded-lg
                    bg-white text-gray-900
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                    disabled:bg-gray-100 disabled:cursor-not-allowed
                    ${error ? "border-red-500" : "border-gray-300"}
                `}
            >
                {placeholder && (
                    <option value="" disabled={value !== ""}>
                        {placeholder}
                    </option>
                )}
                {options.map((option) => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
    );
};

export default Select;
