"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/utils";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { MagnifyingGlassIcon, XMarkIcon, FunnelIcon, ClockIcon } from "@heroicons/react/24/outline";

interface SearchBarProps {
    placeholder?: string;
    defaultValue?: string;
    onSearch?: (query: string) => void;
    onClear?: () => void;
    showFilters?: boolean;
    onFilterClick?: () => void;
    filterCount?: number;
    className?: string;
    autoFocus?: boolean;
    searchInUrl?: boolean;
    recentSearches?: string[];
    suggestions?: string[];
}

const SearchBar = ({
    placeholder = "Rechercher des expressions, piliers, lieux...",
    defaultValue = "",
    onSearch,
    onClear,
    showFilters = true,
    onFilterClick,
    filterCount = 0,
    className,
    autoFocus = false,
    searchInUrl = true,
    recentSearches = [],
    suggestions = [],
}: SearchBarProps) => {
    const router = useRouter();
    const [query, setQuery] = useState(defaultValue);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);

    const handleSearch = useCallback(
        (searchQuery: string) => {
            if (searchInUrl) {
                const params = new URLSearchParams(window.location.search);
                if (searchQuery) {
                    params.set("q", searchQuery);
                } else {
                    params.delete("q");
                }
                router.push(`?${params.toString()}`);
            }

            onSearch?.(searchQuery);
        },
        [router, searchInUrl, onSearch],
    );

    // Debounce search
    useEffect(() => {
        const timer = setTimeout(() => {
            if (query !== defaultValue) {
                handleSearch(query);
            }
        }, 300);

        return () => clearTimeout(timer);
    }, [query, defaultValue, handleSearch]);

    const handleClear = () => {
        setQuery("");
        setShowSuggestions(false);
        onClear?.();

        if (searchInUrl) {
            const params = new URLSearchParams(window.location.search);
            params.delete("q");
            router.push(`?${params.toString()}`);
        }
    };

    const handleSuggestionClick = (suggestion: string) => {
        setQuery(suggestion);
        setShowSuggestions(false);
        handleSearch(suggestion);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        const allSuggestions = [...recentSearches, ...suggestions];

        if (e.key === "ArrowDown") {
            e.preventDefault();
            setSelectedSuggestionIndex((prev) =>
                prev < allSuggestions.length - 1 ? prev + 1 : prev,
            );
        } else if (e.key === "ArrowUp") {
            e.preventDefault();
            setSelectedSuggestionIndex((prev) => (prev > -1 ? prev - 1 : -1));
        } else if (e.key === "Enter" && selectedSuggestionIndex >= 0) {
            e.preventDefault();
            handleSuggestionClick(allSuggestions[selectedSuggestionIndex]);
        } else if (e.key === "Escape") {
            setShowSuggestions(false);
            setSelectedSuggestionIndex(-1);
        }
    };

    const allSuggestions = [...recentSearches, ...suggestions];
    const filteredSuggestions = allSuggestions.filter(
        (s) => s.toLowerCase().includes(query.toLowerCase()) && s !== query,
    );

    return (
        <div className={cn("relative", className)}>
            <div className="flex gap-2">
                <div className="relative flex-1">
                    <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-info/20 to-success/20 rounded-lg blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                        <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary z-10 transition-colors duration-300 group-focus-within:text-primary" />

                        <input
                            type="text"
                            value={query}
                            onChange={(e) => {
                                setQuery(e.target.value);
                                setShowSuggestions(true);
                                setSelectedSuggestionIndex(-1);
                            }}
                            onFocus={() => setShowSuggestions(true)}
                            onBlur={() => {
                                // Delay to allow clicking on suggestions
                                setTimeout(() => setShowSuggestions(false), 200);
                            }}
                            onKeyDown={handleKeyDown}
                            placeholder={placeholder}
                            className="relative w-full h-12 pl-12 pr-12 bg-white/90 backdrop-blur-sm border-2 border-white/50 rounded-lg text-foreground placeholder:text-muted-foreground/70 focus:outline-none focus:border-primary focus:ring-4 focus:ring-primary/20 transition-all duration-300 shadow-lg hover:shadow-xl font-medium"
                            autoFocus={autoFocus}
                        />

                        {query && (
                            <button
                                onClick={handleClear}
                                className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 text-muted-foreground hover:text-primary transition-all duration-200 hover:scale-110 z-10"
                            >
                                <XMarkIcon className="h-4 w-4" />
                            </button>
                        )}
                    </div>

                    {/* Suggestions Dropdown */}
                    {showSuggestions && filteredSuggestions.length > 0 && (
                        <div className="absolute z-10 w-full mt-2 bg-white/95 backdrop-blur-md border border-white/50 rounded-lg shadow-2xl overflow-hidden animate-civic-scale-in">
                            {recentSearches.length > 0 &&
                                filteredSuggestions.some((s) => recentSearches.includes(s)) && (
                                    <div className="px-3 py-2 bg-secondary/50 border-b">
                                        <div className="flex items-center text-xs text-muted-foreground">
                                            <ClockIcon className="h-3 w-3 mr-1" />
                                            Recherches récentes
                                        </div>
                                    </div>
                                )}

                            <div className="max-h-64 overflow-y-auto">
                                {filteredSuggestions.map((suggestion, index) => {
                                    const isRecent = recentSearches.includes(suggestion);
                                    return (
                                        <button
                                            key={index}
                                            onClick={() => handleSuggestionClick(suggestion)}
                                            className={cn(
                                                "w-full px-4 py-2.5 text-left hover:bg-secondary/50 transition-colors flex items-center",
                                                index === selectedSuggestionIndex &&
                                                    "bg-secondary/50",
                                            )}
                                        >
                                            {isRecent ? (
                                                <ClockIcon className="h-4 w-4 mr-3 text-muted-foreground flex-shrink-0" />
                                            ) : (
                                                <MagnifyingGlassIcon className="h-4 w-4 mr-3 text-muted-foreground flex-shrink-0" />
                                            )}
                                            <span className="text-sm text-foreground truncate">
                                                {suggestion}
                                            </span>
                                        </button>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                </div>

                {showFilters && (
                    <Button
                        variant="outline"
                        size="md"
                        onClick={onFilterClick}
                        className="relative bg-white/90 backdrop-blur-sm border-2 border-white/50 hover:border-primary hover:bg-primary hover:text-white shadow-lg hover:shadow-xl transition-all duration-300 group"
                    >
                        <FunnelIcon className="h-4 w-4 transition-transform duration-300 group-hover:rotate-180" />
                        <span className="ml-2 font-medium">Filtres</span>
                        {filterCount > 0 && (
                            <Badge
                                variant="primary"
                                size="sm"
                                className="absolute -top-2 -right-2 h-6 w-6 p-0 flex items-center justify-center animate-bounce shadow-md"
                            >
                                {filterCount}
                            </Badge>
                        )}
                    </Button>
                )}
            </div>

            {/* Active Search Indicator */}
            {isLoading && (
                <div className="absolute -bottom-1 left-0 w-full h-1 bg-gradient-to-r from-primary via-info to-success rounded-full overflow-hidden">
                    <div className="h-full w-1/3 bg-white/50 animate-slide-infinite"></div>
                </div>
            )}
        </div>
    );
};

export default SearchBar;
