import React from "react";
import {
    HeartIcon,
    AcademicCapIcon,
    ShieldCheckIcon,
    TruckIcon,
    HomeIcon,
    SparklesIcon,
    ChartBarIcon,
    UserGroupIcon,
    BuildingLibraryIcon,
    ScaleIcon,
    BuildingOfficeIcon,
    MegaphoneIcon,
    CubeIcon,
} from "@heroicons/react/24/outline";

interface PilierIconProps {
    iconName: string;
    className?: string;
    style?: React.CSSProperties;
    showEmoji?: boolean;
}

// Map icon names from backend to actual React components
const iconMap: Record<string, React.ComponentType<any>> = {
    heart: HeartIcon,
    "academic-cap": AcademicCapIcon,
    "shield-check": ShieldCheckIcon,
    truck: TruckIcon,
    home: HomeIcon,
    leaf: SparklesIcon, // Using SparklesIcon as a substitute for leaf
    "chart-bar": ChartBarIcon,
    users: UserGroupIcon,
    "building-library": BuildingLibraryIcon,
    scale: ScaleIcon,
    "building-office": BuildingOfficeIcon,
    megaphone: MegaphoneIcon,
};

// Emoji fallbacks for each pillar
const emojiMap: Record<string, string> = {
    heart: "❤️",
    "academic-cap": "🎓",
    "shield-check": "🛡️",
    truck: "🚛",
    home: "🏠",
    leaf: "🌿",
    "chart-bar": "📊",
    users: "👥",
    "building-library": "🏛️",
    scale: "⚖️",
    "building-office": "🏢",
    megaphone: "📢",
};

export function PilierIcon({
    iconName,
    className = "h-6 w-6",
    style,
    showEmoji = false,
}: PilierIconProps) {
    // If showEmoji is true, use emoji instead of icon
    if (showEmoji && emojiMap[iconName]) {
        return (
            <span className="text-2xl" style={style}>
                {emojiMap[iconName]}
            </span>
        );
    }

    const Icon = iconMap[iconName] || CubeIcon; // Default to CubeIcon if not found

    return <Icon className={className} style={style} />;
}
