"use client";

import React, { useState, useMemo, useCallback, useEffect } from "react";
import Map, {
    <PERSON><PERSON>,
    <PERSON>up,
    Source,
    Layer,
    NavigationControl,
    GeolocateControl,
    ScaleControl,
    ViewStateChangeEvent,
} from "react-map-gl/mapbox";
import "mapbox-gl/dist/mapbox-gl.css";
import { Expression } from "@/types";
import { MapPinIcon, GlobeAltIcon, MapIcon } from "@heroicons/react/24/solid";

interface ReactMapGLExpressionMapProps {
    expressions: Expression[];
    selectedExpression: Expression | null;
    onSelectExpression: (expression: Expression | null) => void;
    showHeatmap?: boolean;
    showClusters?: boolean;
}

// Get marker color based on expression type
const getMarkerColor = (type: string) => {
    switch (type) {
        case "probleme":
            return "#ef4444";
        case "satisfaction":
            return "#10b981";
        case "idee":
            return "#f59e0b";
        case "question":
            return "#3b82f6";
        default:
            return "#6b7280";
    }
};

const getCoordinates = (expression: Expression) => {
    // Handle both string and object lieu
    const lieu = typeof expression.lieu === "string" ? null : expression.lieu;
    return lieu?.coordonnees;
};

// Calculate distance between two coordinates using Haversine formula
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Earth's radius in km
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
            Math.cos((lat2 * Math.PI) / 180) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
};

const ReactMapGLExpressionMap = ({
    expressions,
    selectedExpression,
    onSelectExpression,
    showHeatmap = false,
    showClusters = true,
}: ReactMapGLExpressionMapProps) => {
    // Default view state (centered on France)
    const defaultViewState = {
        longitude: 2.2137,
        latitude: 46.2276,
        // zoom: 5.5,
        zoom: 1.7,
        pitch: 0,
        bearing: 0,
    };

    const [viewState, setViewState] = useState(defaultViewState);
    const [popupInfo, setPopupInfo] = useState<Expression | null>(null);
    const [mapStyle, setMapStyle] = useState("mapbox://styles/mapbox/light-v11");
    const [hasInitiallyLocated, setHasInitiallyLocated] = useState(false);

    // Convert expressions to GeoJSON
    const geojsonData = useMemo(() => {
        const features = expressions
            .filter((exp) => {
                const coords = getCoordinates(exp);
                return coords?.lat && coords?.lng;
            })
            .map((exp) => {
                const coords = getCoordinates(exp);
                return {
                    type: "Feature" as const,
                    geometry: {
                        type: "Point" as const,
                        coordinates: [Number(coords?.lng), Number(coords?.lat)],
                    },
                    properties: {
                        id: exp.documentId,
                        title: exp.titre,
                        type: exp.type_expression,
                        urgency: exp.urgence,
                        status: exp.statut,
                        emotion: exp.etat_emotionnel,
                        piliers: exp.piliers?.map((p) => p.nom).join(", "),
                        // Store the full expression for popup (must be stringified for GeoJSON)
                        expression: JSON.stringify(exp),
                    },
                };
            });

        return {
            type: "FeatureCollection" as const,
            features,
        };
    }, [expressions]);

    // Handle marker click
    const handleMarkerClick = useCallback(
        (expression: Expression) => {
            onSelectExpression(expression);
            setPopupInfo(expression);
        },
        [onSelectExpression],
    );

    // Find nearest expression to given coordinates
    const findNearestExpression = (userLat: number, userLng: number) => {
        let nearestExpression: Expression | null = null;
        let minDistance = Infinity;

        expressions.forEach((exp) => {
            const coords = getCoordinates(exp);
            if (coords?.lat && coords?.lng) {
                const distance = calculateDistance(
                    userLat,
                    userLng,
                    Number(coords.lat),
                    Number(coords.lng),
                );
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestExpression = exp;
                }
            }
        });

        return nearestExpression;
    };

    const moveMapToNearestExpression = useCallback(() => {
        if (expressions.length > 0 && navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const { latitude, longitude } = position.coords;
                    const nearestExpression = findNearestExpression(latitude, longitude);

                    if (nearestExpression) {
                        const coords = getCoordinates(nearestExpression);
                        if (coords?.lat && coords?.lng) {
                            // Set view to show the nearest expression with appropriate zoom
                            setViewState({
                                longitude: Number(coords.lng),
                                latitude: Number(coords.lat),
                                zoom: 10, // Not too close, allows seeing other nearby expressions
                                pitch: 0,
                                bearing: 0,
                            });
                            // Also show popup for the nearest expression
                            // handleMarkerClick(nearestExpression);
                        }
                    }
                },
                (error) => {
                    console.error("Geolocation error:", error);
                    alert("Impossible d'obtenir votre position actuelle");
                },
            );
        }
    }, [expressions, handleMarkerClick]);

    // Get user's location and find nearest expression
    useEffect(() => {
        if (!hasInitiallyLocated) {
            moveMapToNearestExpression();
            setHasInitiallyLocated(true);
        }
    }, [moveMapToNearestExpression, hasInitiallyLocated]);

    // Reset map to default view
    const resetMapView = useCallback(() => {
        setViewState(defaultViewState);
        setPopupInfo(null);
        onSelectExpression(null);
    }, [onSelectExpression]);

    // Layer styles with proper typing
    const clusterLayer: any = {
        id: "clusters",
        type: "circle",
        source: "expressions",
        filter: ["has", "point_count"],
        paint: {
            "circle-color": [
                "step",
                ["get", "point_count"],
                "#51bbd6",
                10,
                "#f1f075",
                30,
                "#f28cb1",
            ],
            "circle-radius": ["step", ["get", "point_count"], 20, 10, 30, 30, 40],
            "circle-stroke-width": 2,
            "circle-stroke-color": "#ffffff",
        },
    };

    const clusterCountLayer: any = {
        id: "cluster-count",
        type: "symbol",
        source: "expressions",
        filter: ["has", "point_count"],
        layout: {
            "text-field": "{point_count_abbreviated}",
            "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
            "text-size": 12,
        },
        paint: {
            "text-color": "#ffffff",
        },
    };

    const unclusteredPointLayer: any = {
        id: "unclustered-point",
        type: "circle",
        source: "expressions",
        filter: ["!", ["has", "point_count"]],
        paint: {
            "circle-color": [
                "match",
                ["get", "type"],
                "probleme",
                "#ef4444",
                "satisfaction",
                "#10b981",
                "idee",
                "#f59e0b",
                "question",
                "#3b82f6",
                "#6b7280",
            ],
            "circle-radius": ["interpolate", ["linear"], ["get", "urgency"], 1, 6, 5, 12],
            "circle-stroke-width": 2,
            "circle-stroke-color": "#ffffff",
            "circle-opacity": 0.8,
        },
    };

    const heatmapLayer: any = {
        id: "expressions-heat",
        type: "heatmap",
        source: "expressions",
        maxzoom: 15,
        paint: {
            "heatmap-weight": ["interpolate", ["linear"], ["get", "urgency"], 1, 0.2, 5, 1],
            "heatmap-intensity": ["interpolate", ["linear"], ["zoom"], 0, 1, 15, 3],
            "heatmap-color": [
                "interpolate",
                ["linear"],
                ["heatmap-density"],
                0,
                "rgba(33,102,172,0)",
                0.2,
                "rgb(103,169,207)",
                0.4,
                "rgb(209,229,240)",
                0.6,
                "rgb(253,219,199)",
                0.8,
                "rgb(239,138,98)",
                1,
                "rgb(178,24,43)",
            ],
            "heatmap-radius": ["interpolate", ["linear"], ["zoom"], 0, 2, 15, 20],
            "heatmap-opacity": 0.6,
        },
    };

    // Handle layer click
    const handleLayerClick = useCallback(
        (event: any) => {
            // Check if we clicked on an expression point
            if (event.features && event.features.length > 0) {
                const feature = event.features[0];
                if (feature.properties?.expression) {
                    // Prevent default map behavior
                    event.originalEvent?.stopPropagation();
                    event.originalEvent?.preventDefault();

                    const expression = JSON.parse(feature.properties.expression);
                    handleMarkerClick(expression);
                }
            }
        },
        [handleMarkerClick],
    );

    // Remove the automatic focus on selected expression to prevent unwanted map movement
    // The map should only move when user explicitly requests it

    return (
        <Map
            {...viewState}
            onMove={(evt: ViewStateChangeEvent) => setViewState(evt.viewState)}
            mapStyle={mapStyle}
            mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}
            interactiveLayerIds={
                showClusters ? ["clusters", "unclustered-point"] : ["unclustered-point"]
            }
            onClick={handleLayerClick}
            style={{ width: "100%", height: "100%" }}
        >
            <NavigationControl />
            <GeolocateControl />
            <ScaleControl />

            {/* Source and Layers */}
            {geojsonData.features.length > 0 && (
                <Source
                    id="expressions"
                    type="geojson"
                    data={geojsonData}
                    cluster={showClusters}
                    clusterMaxZoom={14}
                    clusterRadius={50}
                >
                    <div>
                        {showHeatmap && <Layer {...heatmapLayer} />}
                        {showClusters && (
                            <>
                                <Layer {...clusterLayer} />
                                <Layer {...clusterCountLayer} />
                            </>
                        )}
                        <Layer {...unclusteredPointLayer} />
                    </div>
                </Source>
            )}

            {/* Fallback: Individual markers for small datasets */}
            {!showClusters &&
                geojsonData.features.length < 100 &&
                expressions.map((expression) => {
                    const coords = getCoordinates(expression);
                    if (!coords?.lat || !coords?.lng) return null;

                    return (
                        <Marker
                            key={expression.documentId}
                            longitude={Number(coords.lng)}
                            latitude={Number(coords.lat)}
                        >
                            <div
                                className="cursor-pointer transform hover:scale-110 transition-transform"
                                style={{
                                    color: getMarkerColor(expression.type_expression),
                                }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleMarkerClick(expression);
                                }}
                            >
                                <MapPinIcon className="h-8 w-8" />
                            </div>
                        </Marker>
                    );
                })}

            {/* Popup */}
            {popupInfo && getCoordinates(popupInfo) && (
                <Popup
                    longitude={Number(getCoordinates(popupInfo)?.lng)}
                    latitude={Number(getCoordinates(popupInfo)?.lat)}
                    onClose={() => setPopupInfo(null)}
                    closeButton={true}
                    closeOnClick={false}
                    anchor="bottom"
                >
                    <div className="p-2 max-w-xs">
                        <h3 className="font-semibold text-sm mb-1">{popupInfo.titre}</h3>
                        <p className="text-xs text-gray-600 mb-2">
                            {popupInfo.type_expression} - Urgence: {popupInfo.urgence}/5
                        </p>
                        <button
                            onClick={() => onSelectExpression(popupInfo)}
                            className="text-xs text-blue-600 hover:text-blue-800"
                        >
                            Voir les détails →
                        </button>
                    </div>
                </Popup>
            )}

            {/* Map controls */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
                {/* Map type toggle */}
                <div className="bg-white rounded-lg shadow-md p-2">
                    <div className="flex items-center gap-1">
                        <button
                            onClick={() => setMapStyle("mapbox://styles/mapbox/light-v11")}
                            className={`px-3 py-1 text-sm rounded transition-colors ${
                                mapStyle === "mapbox://styles/mapbox/light-v11"
                                    ? "bg-blue-500 text-white"
                                    : "hover:bg-gray-100"
                            }`}
                        >
                            Plan
                        </button>
                        <button
                            onClick={() =>
                                setMapStyle("mapbox://styles/mapbox/satellite-streets-v12")
                            }
                            className={`px-3 py-1 text-sm rounded transition-colors ${
                                mapStyle === "mapbox://styles/mapbox/satellite-streets-v12"
                                    ? "bg-blue-500 text-white"
                                    : "hover:bg-gray-100"
                            }`}
                        >
                            Satellite
                        </button>
                    </div>
                </div>

                {/* Map action buttons */}
                <div className="bg-white rounded-lg shadow-md p-2 space-y-2">
                    {/* Reset view button */}
                    <button
                        onClick={resetMapView}
                        className="flex items-center gap-2 px-3 py-1 text-sm rounded hover:bg-gray-100 transition-colors w-full"
                        title="Réinitialiser la vue"
                    >
                        <GlobeAltIcon className="h-4 w-4" />
                        <span>Vue globale</span>
                    </button>

                    {/* Go to nearest expression button */}
                    <button
                        onClick={moveMapToNearestExpression}
                        className="flex items-center gap-2 px-3 py-1 text-sm rounded hover:bg-gray-100 transition-colors w-full"
                        title="Aller à l'expression la plus proche"
                    >
                        <MapIcon className="h-4 w-4" />
                        <span>Plus proche</span>
                    </button>
                </div>
            </div>
        </Map>
    );
};

export default ReactMapGLExpressionMap;
