"use client";

import React, { useState } from "react";
import { Expression } from "@/types";
import ReactMapGLExpressionMap from "./ReactMapGLExpressionMap";
import { MapPinIcon } from "@heroicons/react/24/solid";

export interface ExpressionMapProps {
    expressions: Expression[];
    selectedExpression: Expression | null;
    onSelectExpression: (expression: Expression | null) => void;
}

const ExpressionMap = ({
    expressions,
    selectedExpression,
    onSelectExpression,
}: ExpressionMapProps) => {
    const [showHeatmap, setShowHeatmap] = useState(false);
    const [showClusters, setShowClusters] = useState(true);

    // Check if Mapbox token is available
    const hasMapboxToken = !!process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

    if (!hasMapboxToken) {
        // Fallback UI when Mapbox is not configured
        return (
            <div className="relative w-full h-full bg-gray-100">
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-green-50">
                    <div className="text-center max-w-lg p-8">
                        <MapPinIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-700 mb-2">
                            Configuration Mapbox requise
                        </h3>
                        <p className="text-gray-500 mb-4">
                            Pour afficher la carte interactive, veuillez configurer votre clé API
                            Mapbox.
                        </p>
                        <div className="bg-gray-100 rounded-lg p-4 text-left">
                            <p className="text-sm font-mono text-gray-700">
                                NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_token_here
                            </p>
                        </div>
                        <p className="text-sm text-gray-500 mt-4">
                            Ajoutez cette variable d'environnement dans votre fichier .env.local
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="relative w-full h-full">
            <ReactMapGLExpressionMap
                expressions={expressions}
                selectedExpression={selectedExpression}
                onSelectExpression={onSelectExpression}
                showHeatmap={showHeatmap}
                showClusters={showClusters}
            />

            {/* Map controls overlay */}
            <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-md p-3">
                <h4 className="text-sm font-semibold text-gray-700 mb-3">Options d'affichage</h4>
                <div className="space-y-2">
                    <label className="flex items-center gap-2 cursor-pointer">
                        <input
                            type="checkbox"
                            checked={showClusters}
                            onChange={(e) => setShowClusters(e.target.checked)}
                            className="rounded text-blue-600"
                        />
                        <span className="text-sm text-gray-600">Regroupement des points</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                        <input
                            type="checkbox"
                            checked={showHeatmap}
                            onChange={(e) => setShowHeatmap(e.target.checked)}
                            className="rounded text-blue-600"
                        />
                        <span className="text-sm text-gray-600">Carte de densité</span>
                    </label>
                </div>
            </div>

            {/* Legend */}
            <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-md p-4">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">Légende</h4>
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded-full bg-red-500" />
                        <span className="text-xs text-gray-600">Problème</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded-full bg-green-500" />
                        <span className="text-xs text-gray-600">Satisfaction</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded-full bg-yellow-500" />
                        <span className="text-xs text-gray-600">Idée</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded-full bg-blue-500" />
                        <span className="text-xs text-gray-600">Question</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ExpressionMap;
