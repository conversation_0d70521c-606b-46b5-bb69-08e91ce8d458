"use client";

import React, { useState } from "react";
import { cn } from "@/utils";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { Card } from "@/components/ui/Card";
import {
    XMarkIcon,
    CheckIcon,
    CalendarIcon,
    MapPinIcon,
    TagIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";

export interface FilterOptions {
    piliers?: string[];
    typeExpression?: string[];
    urgence?: number[];
    statut?: string[];
    dateRange?: {
        start: Date;
        end: Date;
    };
    lieu?: string;
    entites?: string[];
    tags?: string[];
}

interface AdvancedFiltersProps {
    isOpen: boolean;
    onClose: () => void;
    filters: FilterOptions;
    onFiltersChange: (filters: FilterOptions) => void;
    onApply: () => void;
    onReset: () => void;
    availableOptions?: {
        piliers?: Array<{ documentId: string; nom: string; couleur: string }>;
        lieux?: Array<{ documentId: string; nom: string }>;
        entites?: Array<{ documentId: string; nom: string }>;
        tags?: string[];
    };
}

const AdvancedFilters = ({
    isOpen,
    onClose,
    filters,
    onFiltersChange,
    onApply,
    onReset,
    availableOptions = {},
}: AdvancedFiltersProps) => {
    const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);

    const handleFilterChange = (key: keyof FilterOptions, value: any) => {
        setLocalFilters((prev) => ({
            ...prev,
            [key]: value,
        }));
    };

    const handleApply = () => {
        onFiltersChange(localFilters);
        onApply();
    };

    const handleReset = () => {
        const emptyFilters: FilterOptions = {
            piliers: [],
            typeExpression: [],
            urgence: [],
            statut: [],
            dateRange: undefined,
            lieu: undefined,
            entites: [],
            tags: [],
        };
        setLocalFilters(emptyFilters);
        onFiltersChange(emptyFilters);
        onReset();
    };

    const getActiveFilterCount = () => {
        let count = 0;
        if (localFilters.piliers?.length) count += localFilters.piliers.length;
        if (localFilters.typeExpression?.length) count += localFilters.typeExpression.length;
        if (localFilters.urgence?.length) count += localFilters.urgence.length;
        if (localFilters.statut?.length) count += localFilters.statut.length;
        if (localFilters.dateRange) count += 1;
        if (localFilters.lieu) count += 1;
        if (localFilters.entites?.length) count += localFilters.entites.length;
        if (localFilters.tags?.length) count += localFilters.tags.length;
        return count;
    };

    const toggleArrayFilter = (key: keyof FilterOptions, value: string | number) => {
        const currentArray = (localFilters[key] as any[]) || [];
        const newArray = currentArray.includes(value)
            ? currentArray.filter((v) => v !== value)
            : [...currentArray, value];
        handleFilterChange(key, newArray);
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-hidden">
            {/* Backdrop */}
            <div
                className={cn(
                    "absolute inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300",
                    isOpen ? "opacity-100" : "opacity-0",
                )}
                onClick={onClose}
            />

            {/* Sidebar */}
            <div
                className={cn(
                    "absolute right-0 top-0 h-full w-full max-w-md bg-white/95 backdrop-blur-md shadow-2xl transition-all duration-500 transform",
                    isOpen ? "translate-x-0" : "translate-x-full",
                )}
            >
                <div className="flex h-full flex-col">
                    {/* Header */}
                    <div className="flex items-center justify-between border-b border-primary/10 p-6 bg-gradient-to-r from-primary/5 to-info/5">
                        <div>
                            <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-info bg-clip-text text-transparent">
                                Filtres avancés
                            </h2>
                            {getActiveFilterCount() > 0 && (
                                <p className="text-sm text-muted-foreground mt-1 animate-civic-fade-in">
                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full bg-primary/10 text-primary font-medium">
                                        {getActiveFilterCount()}
                                    </span>
                                    <span className="ml-2">
                                        filtre{getActiveFilterCount() > 1 ? "s" : ""} actif
                                        {getActiveFilterCount() > 1 ? "s" : ""}
                                    </span>
                                </p>
                            )}
                        </div>
                        <button
                            onClick={onClose}
                            className="rounded-lg p-2 hover:bg-primary/10 transition-all duration-200 hover:rotate-90 group"
                        >
                            <XMarkIcon className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-colors" />
                        </button>
                    </div>

                    {/* Filters Content */}
                    <div className="flex-1 overflow-y-auto p-6 space-y-6">
                        {/* Piliers */}
                        {availableOptions.piliers && (
                            <div>
                                <h3 className="text-sm font-medium text-foreground mb-3">
                                    Piliers de société
                                </h3>
                                <div className="grid grid-cols-2 gap-2">
                                    {availableOptions.piliers.map((pilier) => (
                                        <button
                                            key={pilier.documentId}
                                            onClick={() =>
                                                toggleArrayFilter("piliers", pilier.documentId)
                                            }
                                            className={cn(
                                                "flex items-center gap-2 p-3 rounded-lg border-2 transition-all duration-200 transform hover:scale-105 hover:shadow-md",
                                                localFilters.piliers?.includes(pilier.documentId)
                                                    ? "border-primary bg-gradient-to-r from-primary/10 to-primary/5 shadow-md scale-105"
                                                    : "border-gray-200 hover:border-primary/50 bg-white",
                                            )}
                                        >
                                            <div
                                                className="w-3 h-3 rounded-full flex-shrink-0"
                                                style={{ backgroundColor: pilier.couleur }}
                                            />
                                            <span className="text-xs font-medium truncate">
                                                {pilier.nom}
                                            </span>
                                            {localFilters.piliers?.includes(pilier.documentId) && (
                                                <CheckIcon className="h-3 w-3 text-primary ml-auto flex-shrink-0" />
                                            )}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Type d'expression */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">
                                Type d'expression
                            </h3>
                            <div className="space-y-2">
                                {[
                                    { value: "probleme", label: "Problème", color: "destructive" },
                                    {
                                        value: "satisfaction",
                                        label: "Satisfaction",
                                        color: "success",
                                    },
                                    { value: "idee", label: "Idée", color: "info" },
                                    { value: "question", label: "Question", color: "warning" },
                                ].map((type) => (
                                    <button
                                        key={type.value}
                                        onClick={() =>
                                            toggleArrayFilter("typeExpression", type.value)
                                        }
                                        className={cn(
                                            "w-full flex items-center justify-between p-4 rounded-lg border-2 transition-all duration-200 transform hover:scale-[1.02] hover:shadow-md",
                                            localFilters.typeExpression?.includes(type.value)
                                                ? "border-primary bg-gradient-to-r from-primary/10 to-info/10 shadow-md"
                                                : "border-gray-200 hover:border-primary/50 bg-white",
                                        )}
                                    >
                                        <div className="flex items-center gap-2">
                                            <Badge variant={type.color as any} size="sm">
                                                {type.label}
                                            </Badge>
                                        </div>
                                        {localFilters.typeExpression?.includes(type.value) && (
                                            <CheckIcon className="h-4 w-4 text-primary" />
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Urgence */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">
                                Niveau d'urgence
                            </h3>
                            <div className="flex gap-2">
                                {[1, 2, 3, 4, 5].map((level) => (
                                    <button
                                        key={level}
                                        onClick={() => toggleArrayFilter("urgence", level)}
                                        className={cn(
                                            "flex-1 py-3 px-4 rounded-lg border-2 font-bold transition-all duration-200 transform hover:scale-110 text-lg",
                                            localFilters.urgence?.includes(level)
                                                ? "border-primary bg-gradient-to-r from-primary to-info text-white shadow-lg scale-110"
                                                : "border-gray-200 hover:border-primary/50 bg-white hover:bg-primary/5",
                                            level >= 4 && "hover:border-red-500 hover:text-red-500",
                                            level >= 3 &&
                                                level < 4 &&
                                                "hover:border-orange-500 hover:text-orange-500",
                                        )}
                                    >
                                        {level}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Statut */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">Statut</h3>
                            <div className="space-y-2">
                                {[
                                    { value: "brouillon", label: "Brouillon" },
                                    { value: "en_moderation", label: "En modération" },
                                    { value: "publie", label: "Publié" },
                                    { value: "rejete", label: "Rejeté" },
                                    { value: "resolu", label: "Résolu" },
                                    { value: "archive", label: "Archivé" },
                                ].map((status) => (
                                    <button
                                        key={status.value}
                                        onClick={() => toggleArrayFilter("statut", status.value)}
                                        className={cn(
                                            "w-full flex items-center justify-between p-3 rounded-lg border transition-all",
                                            localFilters.statut?.includes(status.value)
                                                ? "border-primary bg-primary/10"
                                                : "border-gray-200 hover:border-primary/50",
                                        )}
                                    >
                                        <span className="text-sm">{status.label}</span>
                                        {localFilters.statut?.includes(status.value) && (
                                            <CheckIcon className="h-4 w-4 text-primary" />
                                        )}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Date Range */}
                        <div>
                            <h3 className="text-sm font-medium text-foreground mb-3">Période</h3>
                            <div className="space-y-2">
                                <input
                                    type="date"
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    placeholder="Date de début"
                                    onChange={(e) => {
                                        const date = e.target.value
                                            ? new Date(e.target.value)
                                            : undefined;
                                        handleFilterChange("dateRange", {
                                            ...localFilters.dateRange,
                                            start: date,
                                        });
                                    }}
                                />
                                <input
                                    type="date"
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                    placeholder="Date de fin"
                                    onChange={(e) => {
                                        const date = e.target.value
                                            ? new Date(e.target.value)
                                            : undefined;
                                        handleFilterChange("dateRange", {
                                            ...localFilters.dateRange,
                                            end: date,
                                        });
                                    }}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="border-t border-primary/10 p-6 space-y-3 bg-gradient-to-t from-primary/5 to-transparent">
                        <div className="flex gap-3">
                            <Button
                                variant="outline"
                                fullWidth
                                onClick={handleReset}
                                disabled={getActiveFilterCount() === 0}
                                className="border-2 hover:border-destructive hover:text-destructive hover:bg-destructive/5 transition-all duration-300 disabled:opacity-50"
                            >
                                <XMarkIcon className="h-4 w-4 mr-2" />
                                Réinitialiser
                            </Button>
                            <Button
                                variant="primary"
                                fullWidth
                                onClick={handleApply}
                                className="bg-gradient-to-r from-primary to-info hover:from-primary/90 hover:to-info/90 transform hover:scale-[1.02] transition-all duration-300 shadow-lg hover:shadow-xl font-bold"
                            >
                                <CheckIcon className="h-4 w-4 mr-2" />
                                Appliquer ({getActiveFilterCount()})
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdvancedFilters;
