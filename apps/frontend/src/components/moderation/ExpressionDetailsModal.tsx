"use client";

import React, { Fragment } from "react";
import { Dialog, Transition, TransitionChild } from "@headlessui/react";
import { Expression } from "@/types";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import {
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    DocumentTextIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";

interface ExpressionDetailsModalProps {
    expression: Expression | null;
    isOpen: boolean;
    onClose: () => void;
    onApprove: (expressionId: string, notes: string) => void;
    onReject: (expressionId: string, notes: string) => void;
    isPending: boolean;
}

export default function ExpressionDetailsModal({
    expression,
    isOpen,
    onClose,
    onApprove,
    onReject,
    isPending,
}: ExpressionDetailsModalProps) {
    const [moderationNotes, setModerationNotes] = React.useState("");

    const getUrgenceColor = (urgence: number) => {
        const colors = {
            1: "bg-gray-100 text-gray-800",
            2: "bg-blue-100 text-blue-800",
            3: "bg-yellow-100 text-yellow-800",
            4: "bg-orange-100 text-orange-800",
            5: "bg-red-100 text-red-800",
        };
        return colors[urgence as keyof typeof colors] || colors[1];
    };

    const getTypeIcon = (type: string) => {
        const icons = {
            probleme: <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />,
            satisfaction: <CheckCircleIcon className="w-5 h-5 text-green-500" />,
            idee: <DocumentTextIcon className="w-5 h-5 text-blue-500" />,
            question: <DocumentTextIcon className="w-5 h-5 text-purple-500" />,
        };
        return icons[type as keyof typeof icons] || <DocumentTextIcon className="w-5 h-5" />;
    };

    const handleApprove = () => {
        if (expression) {
            onApprove(expression.documentId, moderationNotes);
            setModerationNotes("");
        }
    };

    const handleReject = () => {
        if (expression) {
            onReject(expression.documentId, moderationNotes);
            setModerationNotes("");
        }
    };

    return (
        <Transition show={isOpen} as={Fragment as any}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                <TransitionChild
                    as={Fragment as any}
                    enter="ease-in-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-gray-900/20 backdrop-blur-sm transition-opacity" />
                </TransitionChild>

                <div className="fixed inset-0 overflow-hidden">
                    <div className="absolute inset-0 overflow-hidden">
                        <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                            <TransitionChild
                                as={Fragment as any}
                                enter="transform transition ease-in-out duration-300 sm:duration-500"
                                enterFrom="translate-x-full"
                                enterTo="translate-x-0"
                                leave="transform transition ease-in-out duration-300 sm:duration-500"
                                leaveFrom="translate-x-0"
                                leaveTo="translate-x-full"
                            >
                                <Dialog.Panel className="pointer-events-auto relative w-screen max-w-lg">
                                    <div className="flex h-full flex-col bg-white shadow-2xl">
                                        {/* Header */}
                                        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-5">
                                            <div className="flex items-center justify-between">
                                                <Dialog.Title className="text-xl font-semibold text-white">
                                                    Détails de l'expression
                                                </Dialog.Title>
                                                <button
                                                    type="button"
                                                    className="rounded-lg p-2 text-white/80 hover:text-white hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white/50"
                                                    onClick={onClose}
                                                >
                                                    <span className="sr-only">Fermer</span>
                                                    <XMarkIcon
                                                        className="h-6 w-6"
                                                        aria-hidden="true"
                                                    />
                                                </button>
                                            </div>
                                        </div>

                                        {expression && (
                                            <div className="flex-1 overflow-y-auto">
                                                <div className="px-6 py-6 space-y-6">
                                                    {/* Title and Content */}
                                                    <div>
                                                        <h3 className="text-lg font-bold text-gray-900 mb-3">
                                                            {expression.titre}
                                                        </h3>
                                                        <p className="text-gray-700 leading-relaxed">
                                                            {expression.contenu}
                                                        </p>
                                                    </div>

                                                    {/* Expression Metadata */}
                                                    <div className="space-y-4">
                                                        <h4 className="font-semibold text-gray-800">
                                                            Informations
                                                        </h4>
                                                        <div className="grid grid-cols-2 gap-3">
                                                            <div className="bg-gray-50 rounded-lg p-3">
                                                                <span className="text-xs text-gray-600 block mb-1">
                                                                    Type
                                                                </span>
                                                                <div className="flex items-center">
                                                                    {getTypeIcon(
                                                                        expression.type_expression,
                                                                    )}
                                                                    <span className="text-sm font-medium ml-2 capitalize">
                                                                        {expression.type_expression}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div className="bg-gray-50 rounded-lg p-3">
                                                                <span className="text-xs text-gray-600 block mb-1">
                                                                    Urgence
                                                                </span>
                                                                <Badge
                                                                    className={`${getUrgenceColor(
                                                                        expression.urgence,
                                                                    )} text-sm`}
                                                                >
                                                                    Niveau {expression.urgence}/5
                                                                </Badge>
                                                            </div>
                                                        </div>
                                                        <div className="bg-gray-50 rounded-lg p-3">
                                                            <span className="text-xs text-gray-600 block mb-1">
                                                                État émotionnel
                                                            </span>
                                                            <span className="text-sm font-medium capitalize">
                                                                {expression.etat_emotionnel}
                                                            </span>
                                                        </div>

                                                        {/* Date and Location */}
                                                        <div className="grid grid-cols-2 gap-3">
                                                            <div className="bg-gray-50 rounded-lg p-3">
                                                                <span className="text-xs text-gray-600 block mb-1">
                                                                    Date de création
                                                                </span>
                                                                <span className="text-sm font-medium">
                                                                    {new Date(
                                                                        expression.date_creation,
                                                                    ).toLocaleDateString("fr-FR")}
                                                                </span>
                                                            </div>
                                                            {expression.lieu && (
                                                                <div className="bg-gray-50 rounded-lg p-3">
                                                                    <span className="text-xs text-gray-600 block mb-1">
                                                                        Lieu
                                                                    </span>
                                                                    <span className="text-sm font-medium">
                                                                        {typeof expression.lieu ===
                                                                        "string"
                                                                            ? expression.lieu
                                                                            : expression.lieu.nom}
                                                                    </span>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Pillars */}
                                                    {expression.piliers &&
                                                        expression.piliers.length > 0 && (
                                                            <div>
                                                                <h4 className="font-semibold text-gray-800 mb-3">
                                                                    Piliers concernés
                                                                </h4>
                                                                <div className="flex flex-wrap gap-2">
                                                                    {expression.piliers.map(
                                                                        (pilier) => (
                                                                            <span
                                                                                key={
                                                                                    pilier.documentId
                                                                                }
                                                                                className="px-3 py-1.5 text-sm rounded-full font-medium"
                                                                                style={{
                                                                                    backgroundColor:
                                                                                        pilier.couleur +
                                                                                        "20",
                                                                                    color: pilier.couleur,
                                                                                }}
                                                                            >
                                                                                {pilier.nom}
                                                                            </span>
                                                                        ),
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}

                                                    {/* Author */}
                                                    {expression.auteur && (
                                                        <div className="bg-gray-50 rounded-lg p-4">
                                                            <h4 className="font-semibold text-gray-800 mb-2">
                                                                Auteur
                                                            </h4>
                                                            <p className="text-sm text-gray-600">
                                                                {typeof expression.auteur ===
                                                                "string"
                                                                    ? "Anonyme"
                                                                    : expression.auteur.nom ||
                                                                      "Anonyme"}
                                                            </p>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {/* Footer with Actions */}
                                        <div className="border-t bg-gray-50/50 backdrop-blur-sm px-6 py-6">
                                            <div className="space-y-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                                        Notes de modération (optionnel)
                                                    </label>
                                                    <textarea
                                                        value={moderationNotes}
                                                        onChange={(e) =>
                                                            setModerationNotes(e.target.value)
                                                        }
                                                        rows={3}
                                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none shadow-sm"
                                                        placeholder="Ajoutez des notes sur votre décision..."
                                                    />
                                                </div>

                                                <div className="grid grid-cols-2 gap-3">
                                                    <Button
                                                        variant="primary"
                                                        size="lg"
                                                        className="w-full shadow-sm hover:shadow-md transition-shadow"
                                                        onClick={handleApprove}
                                                        disabled={isPending || !expression}
                                                    >
                                                        <CheckCircleIcon className="w-5 h-5 mr-2" />
                                                        Approuver
                                                    </Button>
                                                    <Button
                                                        variant="destructive"
                                                        size="lg"
                                                        className="w-full shadow-sm hover:shadow-md transition-shadow"
                                                        onClick={handleReject}
                                                        disabled={isPending || !expression}
                                                    >
                                                        <XCircleIcon className="w-5 h-5 mr-2" />
                                                        Rejeter
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Dialog.Panel>
                            </TransitionChild>
                        </div>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
