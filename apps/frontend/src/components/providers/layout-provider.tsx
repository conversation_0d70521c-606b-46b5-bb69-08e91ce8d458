"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

interface LayoutConfig {
    showHeader?: boolean;
    showFooter?: boolean;
    className?: string;
}

interface LayoutContextType {
    config: LayoutConfig;
    setConfig: (config: LayoutConfig) => void;
    resetConfig: () => void;
}

const defaultConfig: LayoutConfig = {
    showHeader: true,
    showFooter: true,
    className: "",
};

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

export function LayoutProvider({ children }: { children: ReactNode }) {
    const [config, setConfigState] = useState<LayoutConfig>(defaultConfig);

    const setConfig = (newConfig: LayoutConfig) => {
        setConfigState({ ...defaultConfig, ...newConfig });
    };

    const resetConfig = () => {
        setConfigState(defaultConfig);
    };

    return (
        <LayoutContext.Provider value={{ config, setConfig, resetConfig }}>
            {children}
        </LayoutContext.Provider>
    );
}

export function useLayout() {
    const context = useContext(LayoutContext);
    if (context === undefined) {
        throw new Error("useLayout must be used within a LayoutProvider");
    }
    return context;
}

// Hook for pages to easily configure layout
export function useLayoutConfig(config: LayoutConfig) {
    const { setConfig, resetConfig } = useLayout();

    React.useEffect(() => {
        setConfig(config);
        return () => resetConfig();
    }, [setConfig, resetConfig, config]);
}
