"use client";

import React, { createContext, useContext, useState, ReactNode, useCallback } from "react";

interface LayoutConfig {
    showHeader?: boolean;
    showFooter?: boolean;
    className?: string;
}

interface LayoutContextType {
    config: LayoutConfig;
    setConfig: (config: LayoutConfig) => void;
    resetConfig: () => void;
}

const defaultConfig: LayoutConfig = {
    showHeader: true,
    showFooter: true,
    className: "",
};

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

export function LayoutProvider({ children }: { children: ReactNode }) {
    const [config, setConfigState] = useState<LayoutConfig>(defaultConfig);

    const setConfig = useCallback((newConfig: LayoutConfig) => {
        setConfigState({ ...defaultConfig, ...newConfig });
    }, []);

    const resetConfig = useCallback(() => {
        setConfigState(defaultConfig);
    }, []);

    return (
        <LayoutContext.Provider value={{ config, setConfig, resetConfig }}>
            {children}
        </LayoutContext.Provider>
    );
}

export function useLayout() {
    const context = useContext(LayoutContext);
    if (context === undefined) {
        throw new Error("useLayout must be used within a LayoutProvider");
    }
    return context;
}

// Hook for pages to easily configure layout
export function useLayoutConfig(config: LayoutConfig) {
    const { setConfig, resetConfig } = useLayout();

    // Memoize the config to prevent unnecessary re-renders
    const memoizedConfig = React.useMemo(
        () => config,
        [config.showHeader, config.showFooter, config.className],
    );

    React.useEffect(() => {
        setConfig(memoizedConfig);
        return () => resetConfig();
    }, [setConfig, resetConfig, memoizedConfig]);
}

// Individual hooks for better performance when only one property is needed
export function useHideHeader() {
    const { setConfig, resetConfig } = useLayout();

    React.useEffect(() => {
        setConfig({ showHeader: false });
        return () => resetConfig();
    }, [setConfig, resetConfig]);
}

export function useHideFooter() {
    const { setConfig, resetConfig } = useLayout();

    React.useEffect(() => {
        setConfig({ showFooter: false });
        return () => resetConfig();
    }, [setConfig, resetConfig]);
}

export function useLayoutClassName(className: string) {
    const { setConfig, resetConfig } = useLayout();

    React.useEffect(() => {
        setConfig({ className });
        return () => resetConfig();
    }, [setConfig, resetConfig, className]);
}
