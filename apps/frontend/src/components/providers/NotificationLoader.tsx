"use client";

import { useEffect } from "react";
import { useNotificationStore } from "@/stores/notificationStore";
import { ServerNotification } from "@/types";

interface NotificationLoaderProps {
    initialNotifications?: ServerNotification[];
}

export function NotificationLoader({ initialNotifications = [] }: NotificationLoaderProps) {
    const { setNotifications, setConnectionStatus } = useNotificationStore();

    useEffect(() => {
        // Load initial notifications into the store
        if (initialNotifications.length > 0) {
            setNotifications(initialNotifications);
        }

        // Mark as connected when component mounts
        setConnectionStatus(true);

        return () => {
            setConnectionStatus(false);
        };
    }, [initialNotifications]);

    return null;
}
