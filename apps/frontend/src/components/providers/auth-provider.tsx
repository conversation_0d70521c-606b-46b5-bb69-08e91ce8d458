"use client";

import { createContext, useContext } from "react";
import { Profile, User } from "@/types";

interface AuthContextType {
    user: User | null;
    profile: Profile | null;
}

const AuthContext = createContext<AuthContextType>({
    user: null,
    profile: null,
});

export function useAuthContext() {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error("useAuthContext must be used within an AuthProvider");
    }
    return context;
}

interface AuthProviderProps {
    user: User | null;
    children: React.ReactNode;
}

export function AuthProvider({ user, children }: AuthProviderProps) {
    return (
        <AuthContext.Provider value={{ user, profile: user?.profile || null }}>
            {children}
        </AuthContext.Provider>
    );
}
