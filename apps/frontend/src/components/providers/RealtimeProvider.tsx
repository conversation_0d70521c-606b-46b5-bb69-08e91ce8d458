"use client";

import React, { useEffect } from "react";
import { useAuthContext } from "@/components/providers/auth-provider";
import { useRealtime } from "@/lib/realtime";

interface RealtimeProviderProps {
    children: React.ReactNode;
}

export default function RealtimeProvider({ children }: RealtimeProviderProps) {
    const { user } = useAuthContext();
    const { connect, disconnect } = useRealtime();

    useEffect(() => {
        if (user) {
            // For now, we'll skip token since it's in httpOnly cookie
            // The websocket server should be configured to read the cookie directly
            console.log("Connecting to realtime service...");
            connect(user.documentId, "");
        } else {
            disconnect();
        }

        // Cleanup on unmount
        return () => {
            if (user) {
                console.log("Disconnecting from realtime service...");
                disconnect();
            }
        };
    }, [user, connect, disconnect]);

    return <>{children}</>;
}
