"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useAuthContext } from "@/components/providers/auth-provider";
import { useAuth } from "@/hooks/useAuth";
import Button from "@/components/ui/Button";
import NotificationCenter from "@/components/ui/NotificationCenter";
import Logo from "@/components/ui/Logo";
import { UserIcon, Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";

const Header = () => {
    const { user, profile } = useAuthContext();
    const { logout } = useAuth();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const isAuthenticated = !!user;

    const handleLogout = () => {
        logout();
        setMobileMenuOpen(false);
    };

    const getNavigation = () => {
        const baseNav = [
            { name: "Accueil", href: "/" },
            { name: "Expressions", href: "/expressions" },
            { name: "Pi<PERSON>", href: "/piliers" },
            { name: "<PERSON><PERSON>", href: "/carte" },
        ];

        // // Add admin/validator specific links
        // if (profile?.role === "super_admin" || profile?.role === "validateur") {
        //     baseNav.push(
        //         { name: "Analytics", href: "/app/analytics" },
        //         { name: "Modération", href: "/app/moderation" },
        //     );
        // }

        return baseNav;
    };

    const navigation = getNavigation();

    return (
        <header className="bg-white shadow-sm border-b">
            <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
                <div className="flex w-full items-center justify-between py-4">
                    {/* Logo */}
                    <div className="flex items-center">
                        <Logo />
                    </div>

                    {/* Desktop Navigation */}
                    <div className="hidden lg:flex lg:items-center lg:space-x-6">
                        {navigation.map((item) => (
                            <Link
                                key={item.name}
                                href={item.href}
                                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition"
                            >
                                {item.name}
                            </Link>
                        ))}
                    </div>

                    {/* Right side */}
                    <div className="flex items-center space-x-4">
                        {isAuthenticated ? (
                            <>
                                {/* Notifications */}
                                <NotificationCenter />

                                {/* User Menu */}
                                <div className="hidden lg:flex lg:items-center lg:space-x-4">
                                    <Link
                                        href="/app/profile"
                                        className="flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900"
                                    >
                                        <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                            <UserIcon className="h-5 w-5 text-gray-600" />
                                        </div>
                                        <span className="font-medium">
                                            {profile?.nom || user?.username}
                                        </span>
                                    </Link>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={handleLogout}
                                        className="text-gray-700 hover:text-gray-900"
                                    >
                                        Déconnexion
                                    </Button>
                                </div>
                            </>
                        ) : (
                            <div className="hidden lg:flex lg:items-center lg:space-x-4">
                                <Button variant="ghost" size="sm" as={Link} href="/auth/login">
                                    Se connecter
                                </Button>

                                <Button size="sm" as={Link} href="/auth/register">
                                    Créer un compte
                                </Button>
                            </div>
                        )}

                        {/* Mobile menu button */}
                        <button
                            type="button"
                            className="lg:hidden rounded-md p-2 text-gray-700 hover:bg-gray-100"
                            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                        >
                            <span className="sr-only">Open menu</span>
                            {mobileMenuOpen ? (
                                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                            ) : (
                                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
                            )}
                        </button>
                    </div>
                </div>

                {/* Mobile menu */}
                {mobileMenuOpen && (
                    <div className="lg:hidden">
                        <div className="space-y-1 pb-3 pt-2">
                            {navigation.map((item) => (
                                <Link
                                    key={item.name}
                                    href={item.href}
                                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                    onClick={() => setMobileMenuOpen(false)}
                                >
                                    {item.name}
                                </Link>
                            ))}
                        </div>
                        {isAuthenticated ? (
                            <div className="border-t border-gray-200 pb-3 pt-4">
                                <div className="flex items-center px-4">
                                    <div className="flex-shrink-0">
                                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                            <UserIcon className="h-6 w-6 text-gray-600" />
                                        </div>
                                    </div>
                                    <div className="ml-3">
                                        <div className="text-base font-medium text-gray-800">
                                            {profile?.nom || user?.username}
                                        </div>
                                        <div className="text-sm font-medium text-gray-500">
                                            {user?.email}
                                        </div>
                                    </div>
                                </div>
                                <div className="mt-3 space-y-1">
                                    <Link
                                        href="/app/profile"
                                        className="block rounded-md px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Mon profil
                                    </Link>
                                    <Link
                                        href="/app"
                                        className="block rounded-md px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Tableau de bord
                                    </Link>
                                    <button
                                        onClick={handleLogout}
                                        className="block w-full rounded-md px-4 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                    >
                                        Déconnexion
                                    </button>
                                </div>
                            </div>
                        ) : (
                            <div className="border-t border-gray-200 pb-3 pt-4">
                                <div className="space-y-1">
                                    <Link
                                        href="/auth/login"
                                        className="block rounded-md px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Se connecter
                                    </Link>
                                    <Link
                                        href="/auth/register"
                                        className="block rounded-md px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                                        onClick={() => setMobileMenuOpen(false)}
                                    >
                                        Créer un compte
                                    </Link>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </nav>
        </header>
    );
};

export default Header;
