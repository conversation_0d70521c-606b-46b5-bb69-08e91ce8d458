"use client";

import React, { PropsWithChildren } from "react";
import Header from "./Header";
import Footer from "./Footer";
import RealtimeProvider from "@/components/providers/RealtimeProvider";
import { useLayout } from "@/components/providers/layout-provider";

interface LayoutProps {
    children: React.ReactNode;
    showHeader?: boolean;
    showFooter?: boolean;
    className?: string;
}

const Layout = ({
    children,
    showHeader: propShowHeader,
    showFooter: propShowFooter,
    className: propClassName,
}: PropsWithChildren<LayoutProps>) => {
    const { config } = useLayout();

    // Props take precedence over context, context takes precedence over defaults
    const showHeader = propShowHeader !== undefined ? propShowHeader : (config.showHeader ?? true);
    const showFooter = propShowFooter !== undefined ? propShowFooter : (config.showFooter ?? true);
    const className = propClassName || config.className || "";

    return (
        <RealtimeProvider>
            <div className="min-h-screen flex flex-col bg-gray-50">
                {showHeader && <Header />}

                <main className={`flex-1 ${className}`}>{children}</main>

                {showFooter && <Footer />}
            </div>
        </RealtimeProvider>
    );
};

export default Layout;
