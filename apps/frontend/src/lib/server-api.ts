// Server-side API client for Next.js App Router
// This file runs on the server only and supports caching

import { cache } from "react";
import {
    ApiResponse,
    Expression,
    Pilier,
    SousPilier,
    Lieu,
    Profile,
    Validateur,
    Action,
    Entite,
    ExpressionFilters,
    PilierStats,
    ProfileStats,
    User,
} from "../types";
import { API_BASE_URL } from "./constants";
import { getAuthCookie } from "./auth/cookies";

// Create server-side fetch function with authentication
async function serverFetch<T>(
    endpoint: string,
    options: RequestInit = {},
): Promise<ApiResponse<T>> {
    const authToken = await getAuthCookie();

    const headers: Record<string, string> = {
        "Content-Type": "application/json",
        ...((options.headers as Record<string, string>) || {}),
    };

    // Add authentication header if token exists
    if (authToken) {
        headers["Authorization"] = `Bearer ${authToken}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        cache: "no-store",
        headers,
        // Enable caching for GET requests by default
        next: {
            revalidate: options.method === "GET" ? 3600 : 0, // Cache for 1 hour
            tags: [endpoint.split("/")[1] || "api"], // Tag based on resource type
        },
    });

    if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401) {
            throw new Error("Non autorisé : Veuillez vous reconnecter");
        }

        throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
}

// Cached server functions for data fetching
// Using React's cache() function to deduplicate requests within a single render

export const fetchPiliers = cache(async (): Promise<Pilier[]> => {
    const response = await serverFetch<Pilier[]>("/piliers?populate=*");
    return response.data;
});

export const fetchPilierById = cache(async (documentId: string): Promise<Pilier> => {
    const response = await serverFetch<Pilier>(`/piliers/${documentId}?populate=*`);
    return response.data;
});

export const fetchSousPiliers = cache(async (pilierId?: string): Promise<SousPilier[]> => {
    const endpoint = pilierId
        ? `/sous-piliers?filters[pilier]=${pilierId}&populate=*`
        : "/sous-piliers?populate=*";
    const response = await serverFetch<SousPilier[]>(endpoint);
    return response.data;
});

export const fetchExpressions = cache(
    async (filters?: ExpressionFilters): Promise<Expression[]> => {
        const params = new URLSearchParams();

        // Add filters to query parameters
        if (filters) {
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined) {
                    params.append(key, String(value));
                }
            });
        }

        const queryString = params.toString();
        const endpoint = `/expressions${queryString ? `?${queryString}` : ""}`;

        const response = await serverFetch<Expression[]>(endpoint);
        return response.data;
    },
);

export const fetchPublicExpressions = cache(
    async (filters?: ExpressionFilters): Promise<ApiResponse<Expression[]>> => {
        const params = new URLSearchParams();

        if (filters) {
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined) {
                    params.append(key, String(value));
                }
            });
        }

        const queryString = params.toString();
        const endpoint = `/expressions/public${queryString ? `?${queryString}` : ""}`;

        const response = await serverFetch<Expression[]>(endpoint);
        return response;
    },
);

export const fetchExpressionById = cache(async (documentId: string): Promise<Expression> => {
    const response = await serverFetch<Expression>(`/expressions/${documentId}?populate=*`);
    return response.data;
});

export const fetchLieux = cache(async (params?: Record<string, any>): Promise<Lieu[]> => {
    const searchParams = new URLSearchParams();

    if (params) {
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined) {
                searchParams.append(key, String(value));
            }
        });
    }

    const queryString = searchParams.toString();
    const endpoint = `/lieux${queryString ? `?${queryString}` : ""}`;

    const response = await serverFetch<Lieu[]>(endpoint);
    return response.data;
});

export const fetchLieuById = cache(async (documentId: string): Promise<Lieu> => {
    const response = await serverFetch<Lieu>(`/lieux/${documentId}?populate=*`);
    return response.data;
});

export const searchLieux = cache(async (query: string): Promise<Lieu[]> => {
    const response = await serverFetch<Lieu[]>(`/lieux/search?q=${encodeURIComponent(query)}`);
    return response.data;
});

export const fetchEntites = cache(async (): Promise<Entite[]> => {
    const response = await serverFetch<Entite[]>("/entites?populate=*");
    return response.data;
});

export const fetchEntiteById = cache(async (documentId: string): Promise<Entite> => {
    const response = await serverFetch<Entite>(`/entites/${documentId}?populate=*`);
    return response.data;
});

export const searchEntites = cache(async (query: string): Promise<Entite[]> => {
    const endpoint = `/entites?filters[nom][$containsi]=${encodeURIComponent(query)}&populate=*`;
    const response = await serverFetch<Entite[]>(endpoint);
    return response.data;
});

// Authentication-required functions
export const fetchCurrentUser = cache(async (): Promise<User> => {
    const response = await serverFetch<User>("/users/me?populate=profile");
    return response.data;
});

export const fetchUserProfile = cache(async (): Promise<Profile> => {
    const response = await serverFetch<Profile>("/profiles/me");
    return response.data;
});

export const fetchUserStats = cache(async (userDocumentId?: string): Promise<ProfileStats> => {
    const endpoint = userDocumentId ? `/profiles/${userDocumentId}/stats` : "/profiles/me/stats";
    const response = await serverFetch<ProfileStats>(endpoint);
    return response.data;
});

export const fetchPilierStats = cache(async (pilierDocumentId: string): Promise<PilierStats> => {
    const response = await serverFetch<PilierStats>(`/piliers/${pilierDocumentId}/stats`);
    return response.data;
});

// Moderation functions (validator/admin only)
export const fetchModerationQueue = cache(
    async (params?: {
        page?: number;
        pageSize?: number;
        urgence?: string;
        type_expression?: string;
        pilier?: string;
        searchQuery?: string;
    }): Promise<{ expressions: Expression[]; pagination: any }> => {
        const queryParams: any = {
            filters: {
                statut: "en_moderation",
            },
            populate: ["piliers", "auteur.profile.lieu_residence", "lieu", "medias"],
            pagination: {
                page: params?.page || 1,
                pageSize: params?.pageSize || 10,
            },
            sort: ["urgence:desc", "date_creation:asc"],
        };

        // Apply filters
        if (params?.urgence) {
            queryParams.filters.urgence = params.urgence;
        }
        if (params?.type_expression) {
            queryParams.filters.type_expression = params.type_expression;
        }
        if (params?.pilier) {
            queryParams.filters.piliers = { documentId: params.pilier };
        }
        if (params?.searchQuery) {
            queryParams.filters.$or = [
                { titre: { $containsi: params.searchQuery } },
                { contenu: { $containsi: params.searchQuery } },
            ];
        }

        const searchParams = new URLSearchParams();

        // Convert complex params to URL search params
        Object.entries(queryParams).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                if (typeof value === "object") {
                    searchParams.append(key, JSON.stringify(value));
                } else {
                    searchParams.append(key, String(value));
                }
            }
        });

        const queryString = searchParams.toString();
        const endpoint = `/expressions/moderation/queue${queryString ? `?${queryString}` : ""}`;

        const response = await serverFetch<Expression[]>(endpoint, {
            next: { revalidate: 30 }, // Revalidate every 30 seconds
        });

        return {
            expressions: response.data,
            pagination: response.meta?.pagination,
        };
    },
);

export const fetchValidateurs = cache(async (): Promise<Validateur[]> => {
    const response = await serverFetch<Validateur[]>("/validateurs?populate=*");
    return response.data;
});

export const fetchActions = cache(async (params?: Record<string, any>): Promise<Action[]> => {
    const searchParams = new URLSearchParams();

    if (params) {
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined) {
                searchParams.append(key, String(value));
            }
        });
    }

    const queryString = searchParams.toString();
    const endpoint = `/actions${queryString ? `?${queryString}` : ""}`;

    const response = await serverFetch<Action[]>(endpoint);
    return response.data;
});

// Analytics API
export const fetchAnalytics = cache(
    async (timeRange: "week" | "month" | "year" = "month"): Promise<any> => {
        const response = await serverFetch<any>(`/expressions/analytics?timeRange=${timeRange}`, {
            next: { revalidate: 300 }, // Revalidate every 5 minutes
        });
        return response.data;
    },
);

export const fetchExpressionUserStats = cache(async (): Promise<any> => {
    const response = await serverFetch<any>("/expressions/user-stats", {
        next: { revalidate: 60 }, // Revalidate every minute
    });
    return response.data;
});

// Moderation stats API

export const fetchModerationStats = cache(
    async (): Promise<{
        pending: number;
        approved: number;
        rejected: number;
        todayProcessed: number;
    }> => {
        const response = await serverFetch<any>("/expressions/moderation/stats", {
            next: { revalidate: 60 }, // Revalidate every minute
        });

        return (
            response.data || {
                pending: 0,
                approved: 0,
                rejected: 0,
                todayProcessed: 0,
            }
        );
    },
);

// Notifications API
export const fetchNotifications = cache(
    async (params?: {
        page?: number;
        pageSize?: number;
        type?: string;
        category?: string;
        read?: string;
        timeRange?: string;
    }): Promise<{ notifications: any[]; pagination: any }> => {
        const searchParams = new URLSearchParams();

        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined) {
                    searchParams.append(key, String(value));
                }
            });
        }

        const queryString = searchParams.toString();
        const endpoint = `/notifications${queryString ? `?${queryString}` : ""}`;

        const response = await serverFetch<any[]>(endpoint, {
            next: { revalidate: 60 }, // Revalidate every minute
        });

        return {
            notifications: response.data,
            pagination: response.meta?.pagination,
        };
    },
);

export const fetchNotificationStats = cache(async (): Promise<any> => {
    const response = await serverFetch<any>("/notifications/stats", {
        next: { revalidate: 60 }, // Revalidate every minute
    });
    return response.data;
});

// Error handling utility for server components
export function handleServerError(error: unknown): never {
    console.error("Server API Error:", error);

    if (error instanceof Error) {
        if (error.message.includes("Unauthorized")) {
            throw new Error("Authentification requise. Veuillez vous connecter.");
        }
        throw new Error(`Erreur Serveur: ${error.message}`);
    }

    throw new Error("Une erreur serveur inattendue s'est produite");
}

// Cache revalidation utilities
export async function revalidateApiCache(tag: string) {
    // This would be used with Next.js revalidateTag function
    // import { revalidateTag } from 'next/cache';
    // revalidateTag(tag);
    console.log(`Revalidating cache for tag: ${tag}`);
}

// Type-safe server-side mutations (for use in server actions)
export async function serverMutation<T>(
    endpoint: string,
    data: any,
    method: "POST" | "PUT" | "PATCH" | "DELETE" = "POST",
): Promise<ApiResponse<T>> {
    return serverFetch<T>(endpoint, {
        method,
        body: JSON.stringify({ data }),
    });
}
