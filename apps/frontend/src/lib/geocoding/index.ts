// Main geocoding module that orchestrates between different geocoding services
// Geoapify is the primary service, OpenCage is the fallback

import { LocationDetails, CoordinateResult } from "./types";
import { getLocationDetailsFromGeoapify, getCoordinatesFromAddressGeoapify } from "./geoapify";
import { getLocationDetailsFromOpenCage, getCoordinatesFromAddressOpenCage } from "./opencage";

// Export types for use in other modules
export type { LocationDetails, CoordinateResult };

/**
 * Reverse geocoding - convert coordinates to address
 * Uses Geoapify as primary service and OpenCage as fallback
 */
export async function getAddressFromCoordinates(
    latitude: number,
    longitude: number,
): Promise<LocationDetails | null> {
    let geoapifyResult: LocationDetails | null = null;
    let openCageResult: LocationDetails | null = null;

    // Try Geoapify first (primary)
    geoapifyResult = await getLocationDetailsFromGeoapify(latitude, longitude);

    // If Geoapify failed or returned incomplete data, try OpenCage as fallback
    if (
        !geoapifyResult ||
        !geoapifyResult.ville ||
        (!geoapifyResult.rue && geoapifyResult.type === "road")
    ) {
        console.log("Trying OpenCage as fallback for reverse geocoding...");
        openCageResult = await getLocationDetailsFromOpenCage(latitude, longitude);
    }

    // Merge results, preferring Geoapify but filling gaps with OpenCage
    if (geoapifyResult && openCageResult) {
        return {
            nom: geoapifyResult.nom || openCageResult.nom,
            adresse_complete: geoapifyResult.adresse_complete || openCageResult.adresse_complete,
            rue: geoapifyResult.rue || openCageResult.rue,
            ville: geoapifyResult.ville || openCageResult.ville,
            code_postal: geoapifyResult.code_postal || openCageResult.code_postal,
            departement: geoapifyResult.departement || openCageResult.departement,
            region: geoapifyResult.region || openCageResult.region,
            pays: geoapifyResult.pays || openCageResult.pays,
            type: geoapifyResult.type || openCageResult.type,
        };
    }

    // Return whichever result we have
    return geoapifyResult || openCageResult;
}

/**
 * Forward geocoding - convert address to coordinates
 * Uses Geoapify as primary service and OpenCage as fallback
 */
export async function getCoordinatesFromAddress(address: string): Promise<CoordinateResult | null> {
    // Try Geoapify first (primary)
    const geoapifyResult = await getCoordinatesFromAddressGeoapify(address);

    if (geoapifyResult) {
        return geoapifyResult;
    }

    // If Geoapify failed, try OpenCage as fallback
    console.log("Trying OpenCage for forward geocoding as fallback...");
    return await getCoordinatesFromAddressOpenCage(address);
}
