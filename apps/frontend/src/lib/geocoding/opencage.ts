// OpenCage geocoding service (fallback)
import { LocationDetails, CoordinateResult } from "./types";

// --- OpenCage Types ---
interface OpenCageLatLng {
    lat: number;
    lng: number;
}

interface OpenCageBounds {
    northeast: OpenCageLatLng;
    southwest: OpenCageLatLng;
}

interface OpenCageComponents {
    "ISO_3166-1_alpha-2"?: string;
    "ISO_3166-1_alpha-3"?: string;
    _category?: string;
    _type?: string;
    country?: string;
    country_code?: string;
    road?: string;
    state?: string;
    town?: string;
    city?: string;
    village?: string;
    hamlet?: string;
    continent?: string;
    county?: string;
    road_type?: string;
    postcode?: string;
    state_district?: string;
    [key: string]: any;
}

interface OpenCageResult {
    bounds?: OpenCageBounds;
    components: OpenCageComponents;
    confidence: number;
    formatted: string;
    geometry: OpenCageLatLng;
}

interface OpenCageStatus {
    code: number;
    message: string;
}

interface OpenCageApiResponse {
    documentation: string;
    licenses: Array<{ name: string; url: string }>;
    results: OpenCageResult[];
    status: OpenCageStatus;
    total_results: number;
}

// Get API key from environment variable
const getApiKey = (): string => {
    const apiKey = process.env.NEXT_PUBLIC_OPENCAGE_API_KEY;
    if (!apiKey) {
        console.error("NEXT_PUBLIC_OPENCAGE_API_KEY is not set in environment variables");
        throw new Error("OpenCage API key is required");
    }
    return apiKey;
};

// Convert OpenCage data to our location format
export function parseOpenCageResult(result: OpenCageResult): LocationDetails {
    const components = result.components;

    // Determine the primary name based on what's available
    const nom =
        components.city ||
        components.town ||
        components.village ||
        components.hamlet ||
        components.state_district ||
        components.county ||
        result.formatted.split(",")[0];

    // Determine location type based on components
    let type = "commune";
    if (components._type === "road" || components.road) {
        type = "road";
    } else if (components.city || components.town || components.village) {
        type = "commune";
    } else if (components.state_district || components.county) {
        type = "departement";
    } else if (components.state) {
        type = "region";
    } else if (components._type) {
        type = components._type;
    }

    return {
        nom: nom,
        adresse_complete: result.formatted,
        rue: components.road,
        ville: components.city || components.town || components.village,
        code_postal: components.postcode,
        departement: components.county || components.state_district,
        region: components.state,
        pays: (components["ISO_3166-1_alpha-2"] || components.country_code || "FR")
            .toUpperCase()
            .slice(0, 2),
        type: type,
    };
}

// Get location details using OpenCage API (reverse geocoding)
export async function getLocationDetailsFromOpenCage(
    latitude: number,
    longitude: number,
): Promise<LocationDetails | null> {
    try {
        const apiKey = getApiKey();
        const queryValue = `${latitude}+${longitude}`;

        const params = new URLSearchParams({
            q: queryValue,
            key: apiKey,
            no_annotations: "1",
            language: "fr",
        });

        const url = `https://www.gps-coordinates.net/geoproxy?${params.toString()}`;

        const response = await fetch(url);

        if (!response.ok) {
            console.error(
                `OpenCage API request failed with status: ${response.status} ${response.statusText}`,
            );
            return null;
        }

        const data: OpenCageApiResponse = await response.json();

        if (data.status.code !== 200) {
            console.error(
                `OpenCage API returned an error: ${data.status.code} - ${data.status.message}`,
            );
            return null;
        }

        if (data.results && data.results.length > 0) {
            return parseOpenCageResult(data.results[0]);
        }

        return null;
    } catch (error) {
        console.error("OpenCage error:", error);
        return null;
    }
}

// Forward geocoding using OpenCage
export async function getCoordinatesFromAddressOpenCage(
    address: string,
): Promise<CoordinateResult | null> {
    try {
        const apiKey = getApiKey();

        const params = new URLSearchParams({
            q: address,
            key: apiKey,
            no_annotations: "1",
            language: "fr",
            countrycode: "fr",
            limit: "1",
        });

        const url = `https://www.gps-coordinates.net/geoproxy?${params.toString()}`;

        const response = await fetch(url);

        if (!response.ok) {
            console.error(
                `OpenCage search failed with status: ${response.status} ${response.statusText}`,
            );
            return null;
        }

        const data: OpenCageApiResponse = await response.json();

        if (data.status.code === 200 && data.results && data.results.length > 0) {
            const result = data.results[0];
            return {
                lat: result.geometry.lat,
                lng: result.geometry.lng,
                formatted: result.formatted,
            };
        }

        return null;
    } catch (error) {
        console.error("OpenCage forward geocoding error:", error);
        return null;
    }
}
