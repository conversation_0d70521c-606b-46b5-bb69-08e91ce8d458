import { z } from "zod";
import { formatZodErrors, safeJsonParse, commonRefinements, errorMessages } from "./utils";

// Expression form validation schema
export const expressionFormSchema = z.object({
    titre: z
        .string()
        .min(5, "Le titre doit contenir au moins 5 caractères")
        .max(200, "Le titre ne peut pas dépasser 200 caractères")
        .trim(),

    contenu: z
        .string()
        .min(20, "La description doit contenir au moins 20 caractères")
        .max(2000, "La description ne peut pas dépasser 2000 caractères")
        .trim(),

    type_expression: z.enum(["probleme", "satisfaction", "idee", "question"], {
        errorMap: () => ({ message: "Type d'expression invalide" }),
    }),

    urgence: z
        .number()
        .int()
        .min(1, "L'urgence doit être entre 1 et 5")
        .max(5, "L'urgence doit être entre 1 et 5"),

    etat_emotionnel: z.enum(["colere", "joie", "tristesse", "espoir", "neutre", "frustration"], {
        errorMap: () => ({ message: "État émotionnel invalide" }),
    }),

    date_evenement: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Format de date invalide (YYYY-MM-DD)")
        .refine(commonRefinements.notFuture, {
            message: "La date de l'événement ne peut pas être dans le futur",
        }),

    lieu: z.string().min(1, "Le lieu est requis"),

    piliers: z
        .array(z.string())
        .min(1, "Vous devez sélectionner au moins un pilier")
        .max(5, "Vous ne pouvez pas sélectionner plus de 5 piliers"),

    sous_piliers: z.array(z.string()).optional().default([]),

    entites: z.array(z.string()).optional().default([]),

    tags: z
        .array(z.string())
        .optional()
        .default([])
        .transform((tags) => tags.filter((tag) => tag.trim().length > 0)),

    medias: z
        .array(z.union([z.string(), z.number()]))
        .optional()
        .default([]),
});

// Inferred type from schema
export type ExpressionFormValues = z.infer<typeof expressionFormSchema>;

// Schema for parsing FormData directly
export const expressionFormDataSchema = z
    .instanceof(FormData)
    .transform((formData) => ({
        titre: formData.get("titre") as string,
        contenu: formData.get("contenu") as string,
        type_expression: formData.get("type_expression") as string,
        urgence: formData.get("urgence") as string,
        etat_emotionnel: formData.get("etat_emotionnel") as string,
        date_evenement: formData.get("date_evenement") as string,
        lieu: formData.get("lieu") as string,
        piliers: formData.get("piliers") as string,
        sous_piliers: formData.get("sous_piliers") as string,
        entites: formData.get("entites") as string,
        tags: formData.get("tags") as string,
        medias: formData.get("medias") as string,
    }))
    .pipe(
        z.object({
            titre: z.string(),
            contenu: z.string(),
            type_expression: z.string(),
            urgence: z.string().transform((val) => parseInt(val, 10)),
            etat_emotionnel: z.string(),
            date_evenement: z.string(),
            lieu: z.string(),
            piliers: z.string().transform((val) => safeJsonParse(val, [])),
            sous_piliers: z
                .string()
                .nullable()
                .transform((val) => safeJsonParse(val, [])),
            entites: z
                .string()
                .nullable()
                .transform((val) => safeJsonParse(val, [])),
            tags: z
                .string()
                .nullable()
                .transform((val) => safeJsonParse(val, [])),

            medias: z
                .string()
                .nullable()
                .transform((val) => safeJsonParse(val, [])),
        }),
    );

// Re-export formatZodErrors from utils
export { formatZodErrors } from "./utils";
