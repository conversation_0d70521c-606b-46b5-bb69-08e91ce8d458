import { z } from "zod";

// Coordinates validation schema
export const coordinatesSchema = z.object({
    lat: z
        .number()
        .min(-90, "La latitude doit être entre -90 et 90")
        .max(90, "La latitude doit être entre -90 et 90"),
    lng: z
        .number()
        .min(-180, "La longitude doit être entre -180 et 180")
        .max(180, "La longitude doit être entre -180 et 180"),
});

// Lieu (Location) creation schema
export const lieuFormSchema = z.object({
    nom: z
        .string()
        .min(2, "Le nom du lieu doit contenir au moins 2 caractères")
        .max(200, "Le nom du lieu ne peut pas dépasser 200 caractères")
        .trim(),

    type: z.enum(["adresse", "zone", "point_interet", "commune", "departement", "region", "pays"], {
        errorMap: () => ({ message: "Type de lieu invalide" }),
    }),

    niveau: z.enum(["pays", "region", "departement", "ville", "quartier", "rue"], {
        errorMap: () => ({ message: "Niveau administratif invalide" }),
    }),

    coordonnees: coordinatesSchema,

    adresse_complete: z
        .string()
        .max(500, "L'adresse complète ne peut pas dépasser 500 caractères")
        .optional(),

    code_postal: z
        .string()
        .optional()
        .refine(
            (val) => {
                if (!val) return true;
                // French postal code validation
                return /^(?:0[1-9]|[1-8]\d|9[0-8])\d{3}$/.test(val);
            },
            {
                message: "Code postal invalide (format français à 5 chiffres attendu)",
            },
        ),

    rue: z.string().max(200, "Le nom de la rue ne peut pas dépasser 200 caractères").optional(),

    ville: z.string().max(100, "Le nom de la ville ne peut pas dépasser 100 caractères").optional(),

    departement: z
        .string()
        .max(100, "Le nom du département ne peut pas dépasser 100 caractères")
        .optional(),

    region: z
        .string()
        .max(100, "Le nom de la région ne peut pas dépasser 100 caractères")
        .optional(),

    pays: z
        .string()
        .max(2, "Le code pays doit être au format ISO (2 lettres)")
        .optional()
        .default("FR"),

    parent: z.string().optional(), // Parent location documentId
});

// Search location schema
export const searchLieuSchema = z.object({
    query: z
        .string()
        .min(2, "La recherche doit contenir au moins 2 caractères")
        .max(200, "La recherche est trop longue"),

    type: z
        .enum(["adresse", "zone", "point_interet", "commune", "departement", "region", "pays"])
        .optional(),

    niveau: z.enum(["pays", "region", "departement", "ville", "quartier", "rue"]).optional(),

    bounds: z
        .object({
            north: z.number(),
            south: z.number(),
            east: z.number(),
            west: z.number(),
        })
        .optional(),

    limit: z.number().int().min(1).max(50).default(10),
});

// Geocoding request schema
export const geocodingRequestSchema = z.object({
    address: z
        .string()
        .min(5, "L'adresse doit contenir au moins 5 caractères")
        .max(500, "L'adresse est trop longue"),

    country: z.string().length(2, "Le code pays doit contenir 2 lettres").default("FR"),
});

// Reverse geocoding request schema
export const reverseGeocodingRequestSchema = z.object({
    lat: z
        .number()
        .min(-90, "La latitude doit être entre -90 et 90")
        .max(90, "La latitude doit être entre -90 et 90"),

    lng: z
        .number()
        .min(-180, "La longitude doit être entre -180 et 180")
        .max(180, "La longitude doit être entre -180 et 180"),
});

// Create location from coordinates schema
export const createLocationFromCoordinatesSchema = z.object({
    nom: z.string().min(2).max(200),
    type: z.enum(["adresse", "zone", "point_interet", "commune", "departement", "region", "pays"]),
    niveau: z.enum(["pays", "region", "departement", "ville", "quartier", "rue"]).optional(),
    coordonnees: coordinatesSchema,
    adresse_complete: z.string().optional(),
    rue: z.string().optional(),
    ville: z.string().optional(),
    code_postal: z.string().optional(),
    departement: z.string().optional(),
    region: z.string().optional(),
    pays: z.string().max(2).default("FR"),
});

// Inferred types
export type LieuFormValues = z.infer<typeof lieuFormSchema>;
export type SearchLieuValues = z.infer<typeof searchLieuSchema>;
export type GeocodingRequestValues = z.infer<typeof geocodingRequestSchema>;
export type ReverseGeocodingRequestValues = z.infer<typeof reverseGeocodingRequestSchema>;
export type CreateLocationFromCoordinatesValues = z.infer<
    typeof createLocationFromCoordinatesSchema
>;
export type Coordinates = z.infer<typeof coordinatesSchema>;
