import { z } from "zod";

// Profile form validation schema
export const profileFormSchema = z.object({
    nom: z
        .string()
        .min(2, "Le nom doit contenir au moins 2 caractères")
        .max(100, "Le nom ne peut pas dépasser 100 caractères")
        .regex(
            /^[a-zA-ZÀ-ÿ\s'-]+$/,
            "Le nom ne peut contenir que des lettres, espaces, apostrophes et tirets",
        )
        .trim(),

    telephone: z
        .string()
        .optional()
        .nullable()
        .refine(
            (val) => {
                if (!val || val.length === 0) return true;
                // French phone number validation
                const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
                return phoneRegex.test(val.replace(/\s/g, ""));
            },
            {
                message: "Numéro de téléphone invalide (format français attendu)",
            },
        )
        .transform((val) => {
            if (!val) return undefined;
            // Normalize phone number format
            return val.replace(/\s/g, "").replace(/^(?:\+33|0033)/, "0");
        }),

    date_naissance: z
        .string()
        .optional()
        .nullable()
        .refine(
            (val) => {
                if (!val) return true;
                const date = new Date(val);
                const today = new Date();
                const minAge = new Date(
                    today.getFullYear() - 13,
                    today.getMonth(),
                    today.getDate(),
                );
                const maxAge = new Date(
                    today.getFullYear() - 120,
                    today.getMonth(),
                    today.getDate(),
                );
                return date <= minAge && date >= maxAge;
            },
            {
                message: "Vous devez avoir entre 13 et 120 ans",
            },
        ),

    genre: z.enum(["M", "F", "Autre"], {
        errorMap: () => ({ message: "Genre invalide" }),
    }),

    lieu_residence: z.string().optional().nullable(),

    preferences: z.object({
        notifications_email: z.boolean().default(true),
        notifications_push: z.boolean().default(true),
        langue: z.enum(["fr", "en", "es", "de", "it"]).default("fr"),
        theme: z.enum(["auto", "light", "dark"]).default("auto"),
    }),
});

// Profile preferences schema (for updating preferences only)
export const profilePreferencesSchema = z.object({
    notifications_email: z.boolean(),
    notifications_push: z.boolean(),
    langue: z.enum(["fr", "en", "es", "de", "it"]),
    theme: z.enum(["auto", "light", "dark"]),
});

// Schema for parsing profile FormData
export const profileFormDataSchema = z
    .instanceof(FormData)
    .transform((formData) => ({
        nom: formData.get("nom") as string,
        telephone: formData.get("telephone") as string | null,
        date_naissance: formData.get("date_naissance") as string | null,
        genre: formData.get("genre") as string,
        lieu_residence: formData.get("lieu_residence") as string | null,
        notifications_email: formData.get("notifications_email") as string,
        notifications_push: formData.get("notifications_push") as string,
        langue: formData.get("langue") as string,
        theme: formData.get("theme") as string,
    }))
    .pipe(
        z
            .object({
                nom: z.string(),
                telephone: z.string().nullable(),
                date_naissance: z.string().nullable(),
                genre: z.string(),
                lieu_residence: z.string().nullable(),
                notifications_email: z.string().transform((val) => val === "true"),
                notifications_push: z.string().transform((val) => val === "true"),
                langue: z.string(),
                theme: z.string(),
            })
            .transform((data) => ({
                ...data,
                preferences: {
                    notifications_email: data.notifications_email,
                    notifications_push: data.notifications_push,
                    langue: data.langue,
                    theme: data.theme,
                },
            })),
    );

// Inferred types
export type ProfileFormValues = z.infer<typeof profileFormSchema>;
export type ProfilePreferencesValues = z.infer<typeof profilePreferencesSchema>;
