import { z } from "zod";

/**
 * Format Zod errors into a field errors object
 * @param error - Zod validation error
 * @returns Object with field names as keys and error messages as values
 */
export function formatZodErrors(error: z.ZodError): Record<string, string> {
    const fieldErrors: Record<string, string> = {};

    error.errors.forEach((err) => {
        const path = err.path.join(".");
        if (!fieldErrors[path]) {
            fieldErrors[path] = err.message;
        }
    });

    return fieldErrors;
}

/**
 * Transform a string value to a boolean
 * @param value - String value to transform
 * @returns Boolean value
 */
export function stringToBoolean(value: string | null | undefined): boolean {
    return value === "true" || value === "1" || value === "on";
}

/**
 * Parse JSON string safely
 * @param value - JSON string to parse
 * @param fallback - Fallback value if parsing fails
 * @returns Parsed value or fallback
 */
export function safeJsonParse<T>(value: string | null | undefined, fallback: T): T {
    if (!value) return fallback;
    try {
        return JSON.parse(value);
    } catch {
        return fallback;
    }
}

/**
 * Common string transformations
 */
export const stringTransforms = {
    trim: (val: string) => val.trim(),
    toLowerCase: (val: string) => val.toLowerCase(),
    toUpperCase: (val: string) => val.toUpperCase(),
    removeSpaces: (val: string) => val.replace(/\s/g, ""),
    normalizeSpaces: (val: string) => val.replace(/\s+/g, " ").trim(),
};

/**
 * Common refinements
 */
export const commonRefinements = {
    notFuture: (date: string) => {
        const eventDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return eventDate <= today;
    },

    isFuture: (date: string) => {
        const eventDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return eventDate > today;
    },

    minAge: (birthDate: string, minYears: number) => {
        const date = new Date(birthDate);
        const today = new Date();
        const minDate = new Date(today.getFullYear() - minYears, today.getMonth(), today.getDate());
        return date <= minDate;
    },

    maxAge: (birthDate: string, maxYears: number) => {
        const date = new Date(birthDate);
        const today = new Date();
        const maxDate = new Date(today.getFullYear() - maxYears, today.getMonth(), today.getDate());
        return date >= maxDate;
    },
};

/**
 * Common error messages in French
 */
export const errorMessages = {
    required: (field: string) => `${field} est requis`,
    minLength: (field: string, min: number) => `${field} doit contenir au moins ${min} caractères`,
    maxLength: (field: string, max: number) => `${field} ne peut pas dépasser ${max} caractères`,
    invalidEmail: "L'adresse email n'est pas valide",
    invalidPhone: "Le numéro de téléphone n'est pas valide",
    invalidPostalCode: "Le code postal n'est pas valide",
    passwordMismatch: "Les mots de passe ne correspondent pas",
    weakPassword:
        "Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre",
    invalidDate: "La date n'est pas valide",
    dateTooEarly: (date: string) => `La date ne peut pas être avant ${date}`,
    dateTooLate: (date: string) => `La date ne peut pas être après ${date}`,
    invalidChoice: "Choix invalide",
    numberTooSmall: (field: string, min: number) => `${field} doit être au moins ${min}`,
    numberTooLarge: (field: string, max: number) => `${field} ne peut pas dépasser ${max}`,
};
