import { z } from "zod";

// Notification preferences schema
export const notificationPreferencesSchema = z.object({
    email_enabled: z.boolean().default(true),
    push_enabled: z.boolean().default(false),

    // Email notification types
    email_new_response: z.boolean().default(true),
    email_status_change: z.boolean().default(true),
    email_weekly_digest: z.boolean().default(false),
    email_marketing: z.boolean().default(false),

    // Push notification types
    push_new_response: z.boolean().default(true),
    push_status_change: z.boolean().default(true),
    push_urgent_alerts: z.boolean().default(true),

    // Frequency settings
    digest_frequency: z.enum(["daily", "weekly", "monthly", "never"]).default("weekly"),
    quiet_hours_start: z
        .string()
        .regex(/^([01]\d|2[0-3]):([0-5]\d)$/)
        .optional(),
    quiet_hours_end: z
        .string()
        .regex(/^([01]\d|2[0-3]):([0-5]\d)$/)
        .optional(),
});

// Mark notification as read schema
export const markNotificationReadSchema = z.object({
    notification_id: z.string().min(1, "L'ID de la notification est requis"),
    read: z.boolean().default(true),
});

// Mark multiple notifications as read schema
export const markMultipleNotificationsReadSchema = z.object({
    notification_ids: z
        .array(z.string())
        .min(1, "Vous devez sélectionner au moins une notification"),
    read: z.boolean().default(true),
});

// Delete notification schema
export const deleteNotificationSchema = z.object({
    notification_id: z.string().min(1, "L'ID de la notification est requis"),
});

// Notification filters schema
export const notificationFiltersSchema = z.object({
    read: z.boolean().optional(),
    category: z.enum(["system", "expression", "moderation", "profile", "achievement"]).optional(),
    priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
    date_from: z.string().optional(),
    date_to: z.string().optional(),
    search: z.string().optional(),
});

// Inferred types
export type NotificationPreferencesValues = z.infer<typeof notificationPreferencesSchema>;
export type MarkNotificationReadValues = z.infer<typeof markNotificationReadSchema>;
export type MarkMultipleNotificationsReadValues = z.infer<
    typeof markMultipleNotificationsReadSchema
>;
export type DeleteNotificationValues = z.infer<typeof deleteNotificationSchema>;
export type NotificationFiltersValues = z.infer<typeof notificationFiltersSchema>;
