import { z } from "zod";

// Expression validation (moderation) schema
export const moderationDecisionSchema = z
    .object({
        decision: z.enum(["approuve", "rejete"], {
            errorMap: () => ({ message: "Décision invalide" }),
        }),

        raison_rejet: z.string().optional(),

        piliers: z.array(z.string()).optional(),

        sous_piliers: z.array(z.string()).optional(),

        entites: z.array(z.string()).optional(),

        notes_internes: z
            .string()
            .max(500, "Les notes internes ne peuvent pas dépasser 500 caractères")
            .optional(),
    })
    .refine(
        (data) => {
            // Reason is required when decision is "rejete"
            if (data.decision === "rejete") {
                return data.raison_rejet && data.raison_rejet.trim().length >= 10;
            }
            return true;
        },
        {
            message: "La raison du rejet doit contenir au moins 10 caractères",
            path: ["raison_rejet"],
        },
    )
    .refine(
        (data) => {
            // If piliers is provided, must have at least one pilier
            if (data.piliers && data.piliers.length === 0) {
                return false;
            }
            return true;
        },
        {
            message: "Si vous modifiez les piliers, vous devez en sélectionner au moins un",
            path: ["piliers"],
        },
    );

// Batch moderation schema
export const batchModerationSchema = z
    .object({
        expression_ids: z
            .array(z.string())
            .min(1, "Vous devez sélectionner au moins une expression")
            .max(50, "Vous ne pouvez pas traiter plus de 50 expressions à la fois"),

        decision: z.enum(["approuve", "rejete"], {
            errorMap: () => ({ message: "Décision invalide" }),
        }),

        raison_rejet: z.string().optional(),
    })
    .refine(
        (data) => {
            if (data.decision === "rejete") {
                return data.raison_rejet && data.raison_rejet.trim().length >= 10;
            }
            return true;
        },
        {
            message: "La raison du rejet doit contenir au moins 10 caractères pour un rejet groupé",
            path: ["raison_rejet"],
        },
    );

// Moderation filters schema
export const moderationFiltersSchema = z.object({
    pilier: z.string().optional(),
    urgence: z.number().int().min(1).max(5).optional(),
    type_expression: z.enum(["probleme", "satisfaction", "idee", "question"]).optional(),
    date_debut: z.string().optional(),
    date_fin: z.string().optional(),
    tri_urgence: z.enum(["asc", "desc"]).optional(),
    tri_date: z.enum(["asc", "desc"]).optional(),
});

// Validator assignment schema
export const validatorAssignmentSchema = z.object({
    expression_id: z.string().min(1, "L'ID de l'expression est requis"),

    validateur_id: z.string().min(1, "L'ID du validateur est requis"),

    priorite: z.enum(["basse", "normale", "haute", "critique"]).default("normale"),

    date_limite: z
        .string()
        .optional()
        .refine(
            (val) => {
                if (!val) return true;
                const date = new Date(val);
                return date > new Date();
            },
            {
                message: "La date limite doit être dans le futur",
            },
        ),

    notes: z.string().max(200, "Les notes ne peuvent pas dépasser 200 caractères").optional(),
});

// Inferred types
export type ModerationDecisionValues = z.infer<typeof moderationDecisionSchema>;
export type BatchModerationValues = z.infer<typeof batchModerationSchema>;
export type ModerationFiltersValues = z.infer<typeof moderationFiltersSchema>;
export type ValidatorAssignmentValues = z.infer<typeof validatorAssignmentSchema>;
