import { z } from "zod";

// Login form validation schema
export const loginSchema = z.object({
    identifier: z
        .string()
        .min(1, "L'identifiant est requis")
        .refine(
            (val) => {
                // Check if it's an email or username
                const isEmail = z.string().email().safeParse(val).success;
                const isUsername = val.length >= 3;
                return isEmail || isUsername;
            },
            {
                message:
                    "Veuillez entrer un email valide ou un nom d'utilisateur (min. 3 caractères)",
            },
        ),

    password: z
        .string()
        .min(6, "Le mot de passe doit contenir au moins 6 caractères")
        .max(100, "Le mot de passe est trop long"),
});

// Register form validation schema
export const registerSchema = z
    .object({
        username: z
            .string()
            .min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères")
            .max(30, "Le nom d'utilisateur ne peut pas dépasser 30 caractères")
            .regex(
                /^[a-zA-Z0-9_-]+$/,
                "Le nom d'utilisateur ne peut contenir que des lettres, chiffres, tirets et underscores",
            )
            .trim(),

        email: z
            .string()
            .min(1, "L'email est requis")
            .email("L'email n'est pas valide")
            .max(100, "L'email est trop long")
            .toLowerCase()
            .trim(),

        password: z
            .string()
            .min(6, "Le mot de passe doit contenir au moins 6 caractères")
            .max(100, "Le mot de passe est trop long")
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                "Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre",
            ),

        confirmPassword: z.string().min(1, "Veuillez confirmer votre mot de passe"),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Les mots de passe ne correspondent pas",
        path: ["confirmPassword"],
    });

// Forgot password schema
export const forgotPasswordSchema = z.object({
    email: z
        .string()
        .min(1, "L'email est requis")
        .email("L'email n'est pas valide")
        .toLowerCase()
        .trim(),
});

// Reset password schema
export const resetPasswordSchema = z
    .object({
        password: z
            .string()
            .min(6, "Le mot de passe doit contenir au moins 6 caractères")
            .max(100, "Le mot de passe est trop long")
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                "Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre",
            ),

        confirmPassword: z.string().min(1, "Veuillez confirmer votre mot de passe"),

        code: z.string().min(1, "Le code de réinitialisation est requis"),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Les mots de passe ne correspondent pas",
        path: ["confirmPassword"],
    });

// Change password schema (for authenticated users)
export const changePasswordSchema = z
    .object({
        currentPassword: z.string().min(1, "Le mot de passe actuel est requis"),

        newPassword: z
            .string()
            .min(6, "Le nouveau mot de passe doit contenir au moins 6 caractères")
            .max(100, "Le mot de passe est trop long")
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                "Le mot de passe doit contenir au moins une majuscule, une minuscule et un chiffre",
            ),

        confirmNewPassword: z.string().min(1, "Veuillez confirmer votre nouveau mot de passe"),
    })
    .refine((data) => data.newPassword !== data.currentPassword, {
        message: "Le nouveau mot de passe doit être différent de l'ancien",
        path: ["newPassword"],
    })
    .refine((data) => data.newPassword === data.confirmNewPassword, {
        message: "Les mots de passe ne correspondent pas",
        path: ["confirmNewPassword"],
    });

// Inferred types
export type LoginFormValues = z.infer<typeof loginSchema>;
export type RegisterFormValues = z.infer<typeof registerSchema>;
export type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;
