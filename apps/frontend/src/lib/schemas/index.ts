// Central export file for all validation schemas

// Expression schemas
export {
    expressionFormSchema,
    expressionFormDataSchema,
    type ExpressionFormValues,
} from "./expression";

// Auth schemas
export {
    loginSchema,
    registerSchema,
    forgotPasswordSchema,
    resetPasswordSchema,
    changePasswordSchema,
    type LoginFormValues,
    type RegisterFormValues,
    type ForgotPasswordFormValues,
    type ResetPasswordFormValues,
    type ChangePasswordFormValues,
} from "./auth";

// Profile schemas
export {
    profileFormSchema,
    profileFormDataSchema,
    profilePreferencesSchema,
    type ProfileFormValues,
    type ProfilePreferencesValues,
} from "./profile";

// Moderation schemas
export {
    moderationDecisionSchema,
    batchModerationSchema,
    moderationFiltersSchema,
    validatorAssignmentSchema,
    type ModerationDecisionValues,
    type BatchModerationValues,
    type ModerationFiltersValues,
    type ValidatorAssignmentValues,
} from "./moderation";

// Location schemas
export {
    coordinatesSchema,
    lieuFormSchema,
    searchLieuSchema,
    geocodingRequestSchema,
    reverseGeocodingRequestSchema,
    createLocationFromCoordinatesSchema,
    type LieuFormValues,
    type SearchLieuValues,
    type GeocodingRequestValues,
    type ReverseGeocodingRequestValues,
    type CreateLocationFromCoordinatesValues,
    type Coordinates,
} from "./lieu";

// Utility functions
export {
    formatZodErrors,
    stringToBoolean,
    safeJsonParse,
    stringTransforms,
    commonRefinements,
    errorMessages,
} from "./utils";
