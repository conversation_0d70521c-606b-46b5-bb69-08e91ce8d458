import { cache } from "react";
import { getAuthCookie } from "./cookies";
import { Profile, User } from "@/types";
import { API_BASE_URL } from "../constants";

export const getCurrentUser = cache(async (): Promise<User | null> => {
    const token = await getAuthCookie();
    if (!token) return null;

    try {
        const response = await fetch(`${API_BASE_URL}/users/me?populate=profile`, {
            headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
            },
            cache: "no-store",
        });

        if (!response.ok) {
            if (response.status === 401) {
                // Token is invalid, return null
                // Cookie removal should be handled in middleware or server actions
                console.error("Authentication failed: Invalid token");
            }
            return null;
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Failed to fetch current user:", error);
        return null;
    }
});

export async function validateSession(): Promise<boolean> {
    const user = await getCurrentUser();
    return user !== null;
}

export async function getUserProfile(): Promise<Profile | null> {
    const user = await getCurrentUser();
    return user?.profile || null;
}
