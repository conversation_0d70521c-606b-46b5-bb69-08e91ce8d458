import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";

export const AUTH_COOKIE_NAME = "pillarscan-auth-token";
const AUTH_COOKIE_OPTIONS = {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax" as const,
    path: "/",
    maxAge: 60 * 60 * 24 * 7, // 7 days
};

export interface DecodedToken {
    id: number; // JWT token still uses numeric id from Strapi auth
    email: string;
    exp: number;
    iat: number;
}

export async function setAuthCookie(token: string) {
    const cookieStore = await cookies();
    cookieStore.set(AUTH_COOKIE_NAME, token, AUTH_COOKIE_OPTIONS);
}

export async function getAuthCookie() {
    const cookieStore = await cookies();
    return cookieStore.get(AUTH_COOKIE_NAME)?.value;
}

export async function removeAuthCookie() {
    const cookieStore = await cookies();
    cookieStore.delete(AUTH_COOKIE_NAME);
}

export async function getDecodedToken(): Promise<DecodedToken | null> {
    const token = await getAuthCookie();
    if (!token) return null;

    try {
        const decoded = jwtDecode<DecodedToken>(token);

        // Check if token is expired
        if (decoded.exp * 1000 < Date.now()) {
            await removeAuthCookie();
            return null;
        }

        return decoded;
    } catch (error) {
        console.error("Failed to decode token:", error);
        await removeAuthCookie();
        return null;
    }
}

export async function isAuthenticated(): Promise<boolean> {
    const token = await getDecodedToken();
    return token !== null;
}
