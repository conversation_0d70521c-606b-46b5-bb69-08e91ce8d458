// Server Actions for Next.js App Router
// These functions run on the server and can be called from forms or client components

"use server";

import { revalidateTag, revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { serverMutation, searchLieux } from "./server-api";
import {
    Expression,
    Profile,
    ExpressionFormData,
    ProfileFormData,
    Lieu,
    ExpressionFilters,
} from "../types";
import { API_BASE_URL } from "./constants";
import { getAuthCookie } from "./auth/cookies";
import {
    expressionFormDataSchema,
    expressionFormSchema,
    formatZodErrors,
} from "./schemas/expression";
import { profileFormDataSchema, profileFormSchema } from "./schemas/app/profile";
import { moderationDecisionSchema } from "./schemas/app/moderation";
import { createLocationFromCoordinatesSchema } from "./schemas/lieu";

// Action result type for better error handling
export type ActionResult<T = any, M = any> = {
    success: boolean;
    data?: T;
    error?: string;
    fieldErrors?: Record<string, string>;
    meta?: M;
};

// Expression actions
export async function createExpressionAction(
    formData: FormData,
): Promise<ActionResult<Expression>> {
    try {
        // Step 1: Parse and transform FormData
        const parseResult = expressionFormDataSchema.safeParse(formData);

        if (!parseResult.success) {
            // Extract the first error for general message
            const firstError = parseResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(parseResult.error),
            };
        }

        const parsedData = parseResult.data;

        // Step 2: Apply business logic validation
        const validationResult = expressionFormSchema.safeParse(parsedData);

        if (!validationResult.success) {
            // Extract the first error for general message
            const firstError = validationResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(validationResult.error),
            };
        }

        // Step 3: Send to API
        const response = await serverMutation<Expression>(
            "/expressions",
            validationResult.data,
            "POST",
        );

        // Step 5: Revalidate relevant caches
        revalidateTag("expressions");
        revalidateTag("piliers");
        revalidatePath("/expressions");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Create expression error:", error);

        // Handle API validation errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;

            // Check for specific field errors from Strapi
            if (apiError.details?.errors) {
                const fieldErrors: Record<string, string> = {};
                apiError.details.errors.forEach((err: any) => {
                    if (err.path && err.path.length > 0) {
                        const fieldName = err.path[err.path.length - 1];
                        fieldErrors[fieldName] = err.message;
                    }
                });

                return {
                    success: false,
                    error: apiError.message || "Erreur de validation",
                    fieldErrors: Object.keys(fieldErrors).length > 0 ? fieldErrors : undefined,
                };
            }

            return {
                success: false,
                error:
                    apiError.message ||
                    "Une erreur est survenue lors de la création de l'expression",
            };
        }

        // Handle other errors
        if (error instanceof Error) {
            return {
                success: false,
                error:
                    error.message || "Une erreur est survenue lors de la création de l'expression",
            };
        }

        return {
            success: false,
            error: "Une erreur inattendue est survenue",
        };
    }
}

export async function updateExpressionAction(
    expressionDocumentId: string,
    formData: FormData,
): Promise<ActionResult<Expression>> {
    // Step 1: Parse and transform FormData
    const parseResult = expressionFormDataSchema.safeParse(formData);

    if (!parseResult.success) {
        // Extract the first error for general message
        const firstError = parseResult.error.errors[0];
        return {
            success: false,
            error: firstError.message,
            fieldErrors: formatZodErrors(parseResult.error),
        };
    }

    const parsedData = parseResult.data;

    // Step 2: Apply business logic validation
    const validationResult = expressionFormSchema.safeParse(parsedData);

    if (!validationResult.success) {
        // Extract the first error for general message
        const firstError = validationResult.error.errors[0];
        return {
            success: false,
            error: firstError.message,
            fieldErrors: formatZodErrors(validationResult.error),
        };
    }

    try {
        // Handle arrays
        const response = await serverMutation<Expression>(
            `/expressions/${expressionDocumentId}`,
            validationResult.data,
            "PUT",
        );

        // Revalidate caches
        revalidateTag("expressions");
        revalidatePath(`/expressions/${expressionDocumentId}`);

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Update expression error:", error);
        return {
            success: false,
            error: "Échec de la mise à jour de l'expression",
        };
    }
}

export async function deleteExpressionAction(expressionDocumentId: string): Promise<ActionResult> {
    try {
        await serverMutation(`/expressions/${expressionDocumentId}`, {}, "DELETE");

        // Revalidate caches
        revalidateTag("expressions");
        revalidatePath("/expressions");

        return { success: true };
    } catch (error) {
        console.error("Delete expression error:", error);
        return {
            success: false,
            error: "Échec de la suppression de l'expression",
        };
    }
}

export async function submitExpressionAction(
    expressionDocumentId: string,
): Promise<ActionResult<Expression>> {
    try {
        const response = await serverMutation<Expression>(
            `/expressions/${expressionDocumentId}/submit`,
            {},
            "POST",
        );

        // Revalidate caches
        revalidateTag("expressions");
        revalidatePath(`/expressions/${expressionDocumentId}`);

        return {
            success: true,
            data: response.data,
        };
    } catch (error) {
        console.error("Submit expression error:", error);
        return {
            success: false,
            error: "Échec de la soumission de l'expression pour révision",
        };
    }
}

// Profile actions
export async function updateProfileAction(formData: FormData): Promise<ActionResult<Profile>> {
    try {
        // Step 1: Parse and transform FormData
        const parseResult = profileFormDataSchema.safeParse(formData);

        if (!parseResult.success) {
            const firstError = parseResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(parseResult.error),
            };
        }

        const parsedData = parseResult.data;

        // Step 2: Apply business logic validation
        const validationResult = profileFormSchema.safeParse(parsedData);

        if (!validationResult.success) {
            const firstError = validationResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(validationResult.error),
            };
        }

        // Step 3: Send to API
        const response = await serverMutation<Profile>(
            "/profiles/me",
            validationResult.data,
            "PUT",
        );

        // Step 4: Revalidate profile cache
        revalidateTag("profiles");
        revalidatePath("/dashboard/app/profile");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Update profile error:", error);

        // Handle API validation errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error: apiError.message || "Échec de la mise à jour du profil",
            };
        }

        return {
            success: false,
            error: "Échec de la mise à jour du profil",
        };
    }
}

// Moderation actions
export async function validateExpressionAction(
    expressionDocumentId: string,
    decision: "approved" | "rejected",
    moderationNotes?: string,
    piliers?: string[],
    sous_piliers?: string[],
): Promise<ActionResult<Expression>> {
    try {
        // Validate moderation decision
        const validationData = {
            decision: decision === "approved" ? "approuve" : "rejete",
            raison_rejet: moderationNotes,
            piliers,
            sous_piliers,
        };

        const validationResult = moderationDecisionSchema.safeParse(validationData);

        if (!validationResult.success) {
            const firstError = validationResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(validationResult.error),
            };
        }

        const response = await serverMutation<Expression>(
            `/expressions/${expressionDocumentId}/validate`,
            validationResult.data,
            "POST",
        );

        // Revalidate moderation queue
        revalidateTag("expressions");
        revalidatePath("/app/moderation");

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Validate expression error:", error);

        // Handle API validation errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error: apiError.message || "Échec de la validation de l'expression",
            };
        }

        return {
            success: false,
            error: "Échec de la validation de l'expression",
        };
    }
}

// Utility function for redirecting after actions
export async function redirectAfterAction(path: string) {
    redirect(path);
}

// File upload action
export async function uploadFileAction(
    formData: FormData,
): Promise<ActionResult<{ documentId: string; url: string; id: string }[]>> {
    try {
        const files = formData.getAll("files") as File[];

        if (files.length === 0) {
            return {
                success: false,
                error: "Aucun fichier fourni",
            };
        }

        // Use the existing API upload endpoint
        const uploadFormData = new FormData();
        files.forEach((file) => uploadFormData.append("files", file));

        const authToken = await getAuthCookie();

        const response = await fetch(`${API_BASE_URL}/upload`, {
            method: "POST",
            headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
            body: uploadFormData,
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Upload failed:", errorText);
            throw new Error(`Échec du téléchargement: ${response.statusText}`);
        }

        const uploadedFiles = await response.json();

        return {
            success: true,
            data: uploadedFiles,
        };
    } catch (error) {
        console.error("File upload error:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Échec du téléchargement des fichiers",
        };
    }
}

// Location search action
export async function searchLieuxAction(query: string): Promise<ActionResult<Lieu[]>> {
    try {
        if (!query || query.trim().length < 2) {
            return {
                success: true,
                data: [],
            };
        }

        const lieux = await searchLieux(query);

        return {
            success: true,
            data: lieux,
        };
    } catch (error) {
        console.error("Search lieux error:", error);
        return {
            success: false,
            error: "Échec de la recherche de lieux",
            data: [],
        };
    }
}

// Reverse geocoding action
export async function reverseGeocodeAction(
    lat: number,
    lng: number,
): Promise<
    ActionResult<{
        nom: string;
        adresse_complete: string;
        rue?: string;
        ville?: string;
        code_postal?: string;
        departement?: string;
        region?: string;
        pays?: string;
        type: string;
    }>
> {
    try {
        const { getAddressFromCoordinates } = await import("./geocoding");
        const result = await getAddressFromCoordinates(lat, lng);

        if (result) {
            return {
                success: true,
                data: result,
            };
        } else {
            return {
                success: false,
                error: "Impossible de déterminer l'adresse à partir des coordonnées",
            };
        }
    } catch (error) {
        console.error("Reverse geocode error:", error);
        return {
            success: false,
            error: "Échec de la géolocalisation inverse",
        };
    }
}

// Create or find location from geocoded data
export async function createLocationFromGeocodedDataAction(geocodedData: {
    nom: string;
    type: string;
    niveau?: string;
    coordonnees: { lat: number; lng: number };
    adresse_complete?: string;
    rue?: string;
    ville?: string;
    code_postal?: string;
    departement?: string;
    region?: string;
    pays?: string;
}): Promise<ActionResult<Lieu>> {
    try {
        // Validate location data
        const validationResult = createLocationFromCoordinatesSchema.safeParse(geocodedData);

        if (!validationResult.success) {
            const firstError = validationResult.error.errors[0];
            return {
                success: false,
                error: firstError.message,
                fieldErrors: formatZodErrors(validationResult.error),
            };
        }

        const response = await serverMutation<Lieu>(
            "/lieux/create-from-coordinates",
            validationResult.data,
            "POST",
        );

        return {
            success: true,
            data: response.data,
        };
    } catch (error: any) {
        console.error("Create location error:", error);

        // Handle API validation errors
        if (error.response?.data?.error) {
            const apiError = error.response.data.error;
            return {
                success: false,
                error: apiError.message || "Échec de la création ou de la recherche du lieu",
            };
        }

        return {
            success: false,
            error: "Échec de la création ou de la recherche du lieu",
        };
    }
}

// Fetch public expressions action
export async function fetchPublicExpressionsAction(
    filters?: ExpressionFilters,
): Promise<ActionResult<Expression[], { pagination?: any }>> {
    try {
        const { fetchPublicExpressions } = await import("./server-api");
        const response = await fetchPublicExpressions(filters);

        return {
            success: true,
            data: response.data || response,
            meta: response.meta,
        };
    } catch (error) {
        console.error("Fetch public expressions error:", error);
        return {
            success: false,
            error: "Échec de la récupération des expressions",
            data: [],
        };
    }
}
