export const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_URL || process.env.API_URL || "http://localhost:1337/api";

// Server URL for serving uploaded files (without /api)
export const SERVER_URL =
    process.env.NEXT_PUBLIC_SERVER_URL ||
    process.env.NEXT_PUBLIC_API_URL?.replace("/api", "") ||
    "http://localhost:1337";

export const assetUrl = (url: string | null | undefined) => {
    if (!url) return "";

    if (url.startsWith("http")) return url;
    if (url.startsWith("/")) return `${SERVER_URL}${url}`;
    return `${SERVER_URL}/${url}`;
};
