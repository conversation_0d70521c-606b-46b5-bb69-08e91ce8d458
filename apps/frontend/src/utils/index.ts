import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { format, formatDistanceToNow, parseISO } from "date-fns";
import { fr } from "date-fns/locale";

// Utility for combining class names
export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

// Date formatting utilities
export const formatDate = (date: string | Date, formatStr: string = "dd/MM/yyyy") => {
    const dateObj = typeof date === "string" ? parseISO(date) : date;
    return format(dateObj, formatStr, { locale: fr });
};

export const formatDateTime = (date: string | Date) => {
    return formatDate(date, "dd/MM/yyyy à HH:mm");
};

export const formatRelativeTime = (date: string | Date) => {
    const dateObj = typeof date === "string" ? parseISO(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true, locale: fr });
};

// Text utilities
export const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
};

export const capitalizeFirst = (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1);
};

export const slugify = (text: string) => {
    return text
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "") // Remove accents
        .replace(/[^a-z0-9 -]/g, "") // Remove special characters
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/-+/g, "-") // Replace multiple hyphens with single
        .trim();
};

// Number utilities
export const formatNumber = (num: number) => {
    return new Intl.NumberFormat("fr-FR").format(num);
};

export const formatPercentage = (num: number, decimals: number = 1) => {
    return new Intl.NumberFormat("fr-FR", {
        style: "percent",
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    }).format(num / 100);
};

// Expression utilities
export const getExpressionStatusColor = (status: string) => {
    const colors = {
        brouillon: "bg-gray-100 text-gray-800",
        en_moderation: "bg-yellow-100 text-yellow-800",
        publie: "bg-green-100 text-green-800",
        en_traitement: "bg-blue-100 text-blue-800",
        resolu: "bg-emerald-100 text-emerald-800",
        rejete: "bg-red-100 text-red-800",
        archive: "bg-gray-100 text-gray-600",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

export const getExpressionStatusLabel = (status: string) => {
    const labels = {
        brouillon: "Brouillon",
        en_moderation: "En modération",
        publie: "Publié",
        en_traitement: "En traitement",
        resolu: "Résolu",
        rejete: "Rejeté",
        archive: "Archivé",
    };
    return labels[status as keyof typeof labels] || status;
};

export const getUrgencyColor = (urgency: number) => {
    const colors = {
        1: "bg-green-100 text-green-800",
        2: "bg-yellow-100 text-yellow-800",
        3: "bg-orange-100 text-orange-800",
        4: "bg-red-100 text-red-800",
        5: "bg-red-200 text-red-900",
    };
    return colors[urgency as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

export const getUrgencyLabel = (urgency: number) => {
    const labels = {
        1: "Très faible",
        2: "Faible",
        3: "Moyenne",
        4: "Élevée",
        5: "Critique",
    };
    return labels[urgency as keyof typeof labels] || "Inconnue";
};

export const getEmotionalStateColor = (state: string) => {
    const colors = {
        colere: "bg-red-100 text-red-800",
        joie: "bg-green-100 text-green-800",
        tristesse: "bg-blue-100 text-blue-800",
        espoir: "bg-emerald-100 text-emerald-800",
        neutre: "bg-gray-100 text-gray-800",
        frustration: "bg-orange-100 text-orange-800",
    };
    return colors[state as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

export const getEmotionalStateLabel = (state: string) => {
    const labels = {
        colere: "Colère",
        joie: "Joie",
        tristesse: "Tristesse",
        espoir: "Espoir",
        neutre: "Neutre",
        frustration: "Frustration",
    };
    return labels[state as keyof typeof labels] || state;
};

export const getExpressionTypeLabel = (type: string) => {
    const labels = {
        probleme: "Problème",
        satisfaction: "Satisfaction",
        idee: "Idée",
        question: "Question",
    };
    return labels[type as keyof typeof labels] || type;
};

export const getExpressionTypeColor = (type: string) => {
    const colors = {
        probleme: "bg-red-100 text-red-800",
        satisfaction: "bg-green-100 text-green-800",
        idee: "bg-blue-100 text-blue-800",
        question: "bg-purple-100 text-purple-800",
    };
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

// Role utilities
export const getRoleLabel = (role: string) => {
    const labels = {
        observateur: "Observateur",
        contributeur: "Contributeur",
        contributeur_verifie: "Contributeur vérifié",
        validateur: "Validateur",
        validateur_senior: "Validateur senior",
        admin_regional: "Admin régional",
        admin_national: "Admin national",
        super_admin: "Super admin",
    };
    return labels[role as keyof typeof labels] || role;
};

export const getRoleColor = (role: string) => {
    const colors = {
        observateur: "bg-gray-100 text-gray-800",
        contributeur: "bg-blue-100 text-blue-800",
        contributeur_verifie: "bg-green-100 text-green-800",
        validateur: "bg-purple-100 text-purple-800",
        validateur_senior: "bg-purple-200 text-purple-900",
        admin_regional: "bg-orange-100 text-orange-800",
        admin_national: "bg-red-100 text-red-800",
        super_admin: "bg-red-200 text-red-900",
    };
    return colors[role as keyof typeof colors] || "bg-gray-100 text-gray-800";
};

// Permission utilities
export const canModerateExpressions = (role: string) => {
    return [
        "validateur",
        "validateur_senior",
        "admin_regional",
        "admin_national",
        "super_admin",
    ].includes(role);
};

export const canManageUsers = (role: string) => {
    return ["admin_regional", "admin_national", "super_admin"].includes(role);
};

export const canAccessAdminPanel = (role: string) => {
    return [
        "validateur",
        "validateur_senior",
        "admin_regional",
        "admin_national",
        "super_admin",
    ].includes(role);
};

// File utilities
export const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const getFileExtension = (filename: string) => {
    return filename.slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2);
};

export const isImageFile = (filename: string) => {
    const imageExtensions = ["jpg", "jpeg", "png", "gif", "webp", "svg"];
    return imageExtensions.includes(getFileExtension(filename).toLowerCase());
};

// URL utilities
export const buildQueryString = (params: Record<string, any>) => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
            if (Array.isArray(value)) {
                value.forEach((v) => searchParams.append(key, v));
            } else {
                searchParams.append(key, value.toString());
            }
        }
    });

    return searchParams.toString();
};

// Validation utilities
export const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

export const isValidPhone = (phone: string) => {
    const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
    return phoneRegex.test(phone);
};

// Local storage utilities
export const getFromStorage = (key: string, defaultValue: any = null) => {
    if (typeof window === "undefined") return defaultValue;

    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error(`Error reading from localStorage key "${key}":`, error);
        return defaultValue;
    }
};

export const setToStorage = (key: string, value: any) => {
    if (typeof window === "undefined") return;

    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error(`Error writing to localStorage key "${key}":`, error);
    }
};

export const removeFromStorage = (key: string) => {
    if (typeof window === "undefined") return;

    try {
        localStorage.removeItem(key);
    } catch (error) {
        console.error(`Error removing from localStorage key "${key}":`, error);
    }
};
