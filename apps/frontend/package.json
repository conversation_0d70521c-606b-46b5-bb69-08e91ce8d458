{"name": "frontend", "version": "0.1.0", "private": true, "description": "PillarScan Frontend - Citizen Expression Platform", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^10.18.0", "jwt-decode": "^4.0.0", "mapbox-gl": "^3.12.0", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.0", "react-hot-toast": "^2.5.2", "react-map-gl": "^8.0.4", "react-query": "^3.39.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.64", "zustand": "^4.5.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-map-gl": "^6.1.7", "eslint": "^8.57.1", "eslint-config-next": "15.3.3", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}}